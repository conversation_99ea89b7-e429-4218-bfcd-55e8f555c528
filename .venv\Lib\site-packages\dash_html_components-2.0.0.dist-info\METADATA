Metadata-Version: 2.1
Name: dash-html-components
Version: 2.0.0
Summary: Vanilla HTML components for Dash
Home-page: https://github.com/plotly/dash-html-components
Author: <PERSON> <<EMAIL>>
Author-email: <EMAIL>
License: MIT
Platform: UNKNOWN
Description-Content-Type: text/markdown
License-File: LICENSE.md
License-File: LICENSE

# Notice

As of Dash 2, the development of dash-html-components has been moved to the [main Dash repo](https://github.com/plotly/dash)

This package exists for backward compatibility

---

# dash-html-components

Vanilla HTML components for [Dash][]

### Install dependencies

1. Create a virtual env and activate.
    ```
    $ virtualenv venv
    $ venv/bin/activate
    ```
    _Note: venv\Scripts\activate for Windows_

2. Install Python packages required to build components.
    ```
    $ pip install -r dev-requirements.txt
    ```
3. Generate components and install npm packages
    ```
    $ npm install
    ```

### Generating HTML Components

The components in `src/components`, as well as the export index in
`src/index.js` are programmatically generated from element definitions in
`scripts/`. To regenerate:


```sh
$ npm run generate-components
```
The list of attributes is regenerated by scraping the
[MDN HTML attribute reference][].

_Note: This step will have already been done for you when you ran `npm install`_

### Development

#### Testing your components in Dash

1. Watch for changes

        $ npm run build:watch

2. Install module locally (after every change)

        # Generate metadata, and build the JavaScript bundle
        $ npm run install-local

        # Now you're done. For subsequent changes, if you've got `npm run build:watch`
        $ python setup.py install

3. Run the Dash layout you want to test

        # Import dash_html_components to your layout, then run it:
        $ python my_dash_layout.py

#### Installing Python package locally

Before publishing to PyPi, you can test installing the module locally:

```sh
# Install in `site-packages` on your machine
$ npm run install-local
```

#### Uninstalling Python package locally

```sh
$ npm run uninstall-local
```

## Contributing

See the [contributing guide](CONTRIBUTING.md) for guidelines on contributing to this project.


### Create a production build and publish:

1. Build your code:
    ```
    $ npm run build
    ```
2. Create a Python tarball
    ```
    $ python setup.py sdist
    ```
    This distribution tarball will get generated in the `dist/` folder

3. Test your tarball by copying it into a new environment and installing it locally:
    ```
    $ pip install dash-html-components-<new-version>.tar.gz
    ```

4. If it works, then you can publish the component to NPM and PyPI:
    1. Publish on PyPI
        ```
        $ twine upload dist/*
        ```
    2. Cleanup the dist folder (optional)
        ```
        $ rm -rf dist
        ```
    3. Publish on NPM (Optional if chosen False in `publish_on_npm`)
        ```
        $ npm publish
        ```
        _Publishing your component to NPM will make the JavaScript bundles available on the unpkg CDN. By default, Dash servers the component library's CSS and JS from the remote unpkg CDN, so if you haven't published the component package to NPM you'll need to set the `serve_locally` flags to `True` (unless you choose `False` on `publish_on_npm`). We will eventually make `serve_locally=True` the default, [follow our progress in this issue](https://github.com/plotly/dash/issues/284)._

[Dash]: https://plotly.com/dash
[MDN HTML attribute reference]: https://developer.mozilla.org/en-US/docs/Web/HTML/Attributes
[NPM package authors]: https://www.npmjs.com/package/dash-html-components/access
[PyPi]: https://pypi.python.org/pypi


