/*! For license information please see dash_renderer.min.js.LICENSE.txt */
!function(){var t={800:function(t,e,r){var n;window,t.exports=(n=r(196),function(t){var e={};function r(n){if(e[n])return e[n].exports;var o=e[n]={i:n,l:!1,exports:{}};return t[n].call(o.exports,o,o.exports,r),o.l=!0,o.exports}return r.m=t,r.c=e,r.d=function(t,e,n){r.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:n})},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.t=function(t,e){if(1&e&&(t=r(t)),8&e)return t;if(4&e&&"object"==typeof t&&t&&t.__esModule)return t;var n=Object.create(null);if(r.r(n),Object.defineProperty(n,"default",{enumerable:!0,value:t}),2&e&&"string"!=typeof t)for(var o in t)r.d(n,o,function(e){return t[e]}.bind(null,o));return n},r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,"a",e),e},r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.p="",r(r.s=1)}([function(t,e){t.exports=n},function(t,e,r){"use strict";r.r(e),r.d(e,"asyncDecorator",(function(){return a})),r.d(e,"inheritAsyncDecorator",(function(){return u})),r.d(e,"isReady",(function(){return c})),r.d(e,"History",(function(){return l}));var n=r(0);function o(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function i(t){return function(){var e=this,r=arguments;return new Promise((function(n,i){var a=t.apply(e,r);function u(t){o(a,n,i,u,c,"next",t)}function c(t){o(a,n,i,u,c,"throw",t)}u(void 0)}))}}var a=function(t,e){var r,o={isReady:new Promise((function(t){r=t})),get:Object(n.lazy)((function(){return Promise.resolve(e()).then((function(t){return setTimeout(i(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,r(!0);case 2:o.isReady=!0;case 3:case"end":return t.stop()}}),t)}))),0),t}))}))};return Object.defineProperty(t,"_dashprivate_isLazyComponentReady",{get:function(){return o.isReady}}),o.get},u=function(t,e){Object.defineProperty(t,"_dashprivate_isLazyComponentReady",{get:function(){return c(e)}})},c=function(t){return t&&t._dashprivate_isLazyComponentReady};function s(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}var f="_dashprivate_historychange",l=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t)}var e,r;return e=t,r=[{key:"dispatchChangeEvent",value:function(){window.dispatchEvent(new CustomEvent(f))}},{key:"onChange",value:function(t){return window.addEventListener(f,t),function(){return window.removeEventListener(f,t)}}}],null&&s(e.prototype,null),r&&s(e,r),Object.defineProperty(e,"prototype",{writable:!1}),t}()}]))},566:function(t,e){"use strict";var r=Symbol.for("react.element"),n=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),a=Symbol.for("react.profiler"),u=Symbol.for("react.provider"),c=Symbol.for("react.context"),s=Symbol.for("react.server_context"),f=Symbol.for("react.forward_ref"),l=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),d=Symbol.for("react.lazy");Symbol.for("react.offscreen");Symbol.for("react.module.reference"),e.isContextConsumer=function(t){return function(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case r:switch(t=t.type){case o:case a:case i:case l:case p:return t;default:switch(t=t&&t.$$typeof){case s:case c:case f:case d:case h:case u:return t;default:return e}}case n:return e}}}(t)===c}},100:function(t,e,r){"use strict";t.exports=r(566)},489:function(t,e){"use strict";e.parse=function(t,e){if("string"!=typeof t)throw new TypeError("argument str must be a string");for(var r={},n=(e||{}).decode||o,i=0;i<t.length;){var u=t.indexOf("=",i);if(-1===u)break;var c=t.indexOf(";",i);if(-1===c)c=t.length;else if(c<u){i=t.lastIndexOf(";",u-1)+1;continue}var s=t.slice(i,u).trim();if(void 0===r[s]){var f=t.slice(u+1,c).trim();34===f.charCodeAt(0)&&(f=f.slice(1,-1)),r[s]=a(f,n)}i=c+1}return r},e.serialize=function(t,e,o){var a=o||{},u=a.encode||i;if("function"!=typeof u)throw new TypeError("option encode is invalid");if(!n.test(t))throw new TypeError("argument name is invalid");var c=u(e);if(c&&!n.test(c))throw new TypeError("argument val is invalid");var s=t+"="+c;if(null!=a.maxAge){var f=a.maxAge-0;if(isNaN(f)||!isFinite(f))throw new TypeError("option maxAge is invalid");s+="; Max-Age="+Math.floor(f)}if(a.domain){if(!n.test(a.domain))throw new TypeError("option domain is invalid");s+="; Domain="+a.domain}if(a.path){if(!n.test(a.path))throw new TypeError("option path is invalid");s+="; Path="+a.path}if(a.expires){var l=a.expires;if(!function(t){return"[object Date]"===r.call(t)||t instanceof Date}(l)||isNaN(l.valueOf()))throw new TypeError("option expires is invalid");s+="; Expires="+l.toUTCString()}if(a.httpOnly&&(s+="; HttpOnly"),a.secure&&(s+="; Secure"),a.partitioned&&(s+="; Partitioned"),a.priority)switch("string"==typeof a.priority?a.priority.toLowerCase():a.priority){case"low":s+="; Priority=Low";break;case"medium":s+="; Priority=Medium";break;case"high":s+="; Priority=High";break;default:throw new TypeError("option priority is invalid")}if(a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:s+="; SameSite=Strict";break;case"lax":s+="; SameSite=Lax";break;case"strict":s+="; SameSite=Strict";break;case"none":s+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return s};var r=Object.prototype.toString,n=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function o(t){return-1!==t.indexOf("%")?decodeURIComponent(t):t}function i(t){return encodeURIComponent(t)}function a(t,e){try{return e(t)}catch(e){return t}}},789:function(t,e,r){"use strict";var n=r(81),o=r.n(n),i=r(645),a=r.n(i)()(o());a.push([t.id,"._dash-undo-redo {\n    position: fixed;\n    bottom: 30px;\n    left: 30px;\n    font-size: 20px;\n    text-align: center;\n    z-index: 9999;\n    background-color: rgba(255, 255, 255, 0.9);\n\n}\n._dash-undo-redo>div {\n    position: relative;\n}\n._dash-undo-redo-link {\n    color: #0074D9;\n    cursor: pointer;\n    margin-left: 10px;\n    margin-right: 10px;\n    display: inline-block;\n    opacity: 0.2;\n}\n._dash-undo-redo-link:hover {\n    opacity: 1;\n}\n._dash-undo-redo-link ._dash-icon-undo {\n    font-size: 20px;\n    transform: rotate(270deg);\n}\n._dash-undo-redo-link ._dash-icon-redo {\n    font-size: 20px;\n    transform: rotate(90deg);\n}\n._dash-undo-redo-link ._dash-undo-redo-label {\n    font-size: 15px;\n}\n",""]),e.Z=a},645:function(t){"use strict";t.exports=function(t){var e=[];return e.toString=function(){return this.map((function(e){var r="",n=void 0!==e[5];return e[4]&&(r+="@supports (".concat(e[4],") {")),e[2]&&(r+="@media ".concat(e[2]," {")),n&&(r+="@layer".concat(e[5].length>0?" ".concat(e[5]):""," {")),r+=t(e),n&&(r+="}"),e[2]&&(r+="}"),e[4]&&(r+="}"),r})).join("")},e.i=function(t,r,n,o,i){"string"==typeof t&&(t=[[null,t,void 0]]);var a={};if(n)for(var u=0;u<this.length;u++){var c=this[u][0];null!=c&&(a[c]=!0)}for(var s=0;s<t.length;s++){var f=[].concat(t[s]);n&&a[f[0]]||(void 0!==i&&(void 0===f[5]||(f[1]="@layer".concat(f[5].length>0?" ".concat(f[5]):""," {").concat(f[1],"}")),f[5]=i),r&&(f[2]?(f[1]="@media ".concat(f[2]," {").concat(f[1],"}"),f[2]=r):f[2]=r),o&&(f[4]?(f[1]="@supports (".concat(f[4],") {").concat(f[1],"}"),f[4]=o):f[4]="".concat(o)),e.push(f))}},e}},81:function(t){"use strict";t.exports=function(t){return t[1]}},357:function(t,e){function r(t,e,r,n){var i={};return function(a){if(!i[a]){var u={},c=[],s=[];for(s.push({node:a,processed:!1});s.length>0;){var f=s[s.length-1],l=f.processed,p=f.node;if(l)s.pop(),c.pop(),u[p]=!1,i[p]=!0,e&&0!==t[p].length||r.push(p);else{if(i[p]){s.pop();continue}if(u[p]){if(n){s.pop();continue}throw c.push(p),new o(c)}u[p]=!0,c.push(p);for(var h=t[p],d=h.length-1;d>=0;d--)s.push({node:h[d],processed:!1});f.processed=!0}}}}}var n=e.f=function(t){this.nodes={},this.outgoingEdges={},this.incomingEdges={},this.circular=t&&!!t.circular};n.prototype={size:function(){return Object.keys(this.nodes).length},addNode:function(t,e){this.hasNode(t)||(this.nodes[t]=2===arguments.length?e:t,this.outgoingEdges[t]=[],this.incomingEdges[t]=[])},removeNode:function(t){this.hasNode(t)&&(delete this.nodes[t],delete this.outgoingEdges[t],delete this.incomingEdges[t],[this.incomingEdges,this.outgoingEdges].forEach((function(e){Object.keys(e).forEach((function(r){var n=e[r].indexOf(t);n>=0&&e[r].splice(n,1)}),this)})))},hasNode:function(t){return this.nodes.hasOwnProperty(t)},getNodeData:function(t){if(this.hasNode(t))return this.nodes[t];throw new Error("Node does not exist: "+t)},setNodeData:function(t,e){if(!this.hasNode(t))throw new Error("Node does not exist: "+t);this.nodes[t]=e},addDependency:function(t,e){if(!this.hasNode(t))throw new Error("Node does not exist: "+t);if(!this.hasNode(e))throw new Error("Node does not exist: "+e);return-1===this.outgoingEdges[t].indexOf(e)&&this.outgoingEdges[t].push(e),-1===this.incomingEdges[e].indexOf(t)&&this.incomingEdges[e].push(t),!0},removeDependency:function(t,e){var r;this.hasNode(t)&&(r=this.outgoingEdges[t].indexOf(e))>=0&&this.outgoingEdges[t].splice(r,1),this.hasNode(e)&&(r=this.incomingEdges[e].indexOf(t))>=0&&this.incomingEdges[e].splice(r,1)},clone:function(){var t=this,e=new n;return Object.keys(t.nodes).forEach((function(r){e.nodes[r]=t.nodes[r],e.outgoingEdges[r]=t.outgoingEdges[r].slice(0),e.incomingEdges[r]=t.incomingEdges[r].slice(0)})),e},directDependenciesOf:function(t){if(this.hasNode(t))return this.outgoingEdges[t].slice(0);throw new Error("Node does not exist: "+t)},directDependantsOf:function(t){if(this.hasNode(t))return this.incomingEdges[t].slice(0);throw new Error("Node does not exist: "+t)},dependenciesOf:function(t,e){if(this.hasNode(t)){var n=[];r(this.outgoingEdges,e,n,this.circular)(t);var o=n.indexOf(t);return o>=0&&n.splice(o,1),n}throw new Error("Node does not exist: "+t)},dependantsOf:function(t,e){if(this.hasNode(t)){var n=[];r(this.incomingEdges,e,n,this.circular)(t);var o=n.indexOf(t);return o>=0&&n.splice(o,1),n}throw new Error("Node does not exist: "+t)},overallOrder:function(t){var e=this,n=[],o=Object.keys(this.nodes);if(0===o.length)return n;if(!this.circular){var i=r(this.outgoingEdges,!1,[],this.circular);o.forEach((function(t){i(t)}))}var a=r(this.outgoingEdges,t,n,this.circular);return o.filter((function(t){return 0===e.incomingEdges[t].length})).forEach((function(t){a(t)})),this.circular&&o.filter((function(t){return-1===n.indexOf(t)})).forEach((function(t){a(t)})),n},entryNodes:function(){var t=this;return Object.keys(this.nodes).filter((function(e){return 0===t.incomingEdges[e].length}))}},n.prototype.directDependentsOf=n.prototype.directDependantsOf,n.prototype.dependentsOf=n.prototype.dependantsOf;var o=function(t){var e="Dependency Cycle Found: "+t.join(" -> "),r=new Error(e);return r.cyclePath=t,Object.setPrototypeOf(r,Object.getPrototypeOf(this)),Error.captureStackTrace&&Error.captureStackTrace(r,o),r};o.prototype=Object.create(Error.prototype,{constructor:{value:Error,enumerable:!1,writable:!0,configurable:!0}}),Object.setPrototypeOf(o,Error)},924:function(t,e,r){"use strict";var n=r(244);t.exports=function(t){var e=typeof t;if("string"===e){var r=t;if(0==(t=+t)&&n(r))return!1}else if("number"!==e)return!1;return t-t<1}},679:function(t,e,r){"use strict";var n=r(864),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},i={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},a={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},u={};function c(t){return n.isMemo(t)?a:u[t.$$typeof]||o}u[n.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},u[n.Memo]=a;var s=Object.defineProperty,f=Object.getOwnPropertyNames,l=Object.getOwnPropertySymbols,p=Object.getOwnPropertyDescriptor,h=Object.getPrototypeOf,d=Object.prototype;t.exports=function t(e,r,n){if("string"!=typeof r){if(d){var o=h(r);o&&o!==d&&t(e,o,n)}var a=f(r);l&&(a=a.concat(l(r)));for(var u=c(e),y=c(r),v=0;v<a.length;++v){var b=a[v];if(!(i[b]||n&&n[b]||y&&y[b]||u&&u[b])){var g=p(r,b);try{s(e,b,g)}catch(t){}}}}return e}},143:function(t){"use strict";t.exports=function(t,e,r,n,o,i,a,u){if(!t){var c;if(void 0===e)c=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var s=[r,n,o,i,a,u],f=0;(c=new Error(e.replace(/%s/g,(function(){return s[f++]})))).name="Invariant Violation"}throw c.framesToPop=1,c}}},244:function(t){"use strict";t.exports=function(t){for(var e,r=t.length,n=0;n<r;n++)if(((e=t.charCodeAt(n))<9||e>13)&&32!==e&&133!==e&&160!==e&&5760!==e&&6158!==e&&(e<8192||e>8205)&&8232!==e&&8233!==e&&8239!==e&&8287!==e&&8288!==e&&12288!==e&&65279!==e)return!1;return!0}},414:function(t){"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},921:function(t,e){"use strict";var r="function"==typeof Symbol&&Symbol.for,n=r?Symbol.for("react.element"):60103,o=r?Symbol.for("react.portal"):60106,i=r?Symbol.for("react.fragment"):60107,a=r?Symbol.for("react.strict_mode"):60108,u=r?Symbol.for("react.profiler"):60114,c=r?Symbol.for("react.provider"):60109,s=r?Symbol.for("react.context"):60110,f=r?Symbol.for("react.async_mode"):60111,l=r?Symbol.for("react.concurrent_mode"):60111,p=r?Symbol.for("react.forward_ref"):60112,h=r?Symbol.for("react.suspense"):60113,d=r?Symbol.for("react.suspense_list"):60120,y=r?Symbol.for("react.memo"):60115,v=r?Symbol.for("react.lazy"):60116,b=r?Symbol.for("react.block"):60121,g=r?Symbol.for("react.fundamental"):60117,m=r?Symbol.for("react.responder"):60118,w=r?Symbol.for("react.scope"):60119;function O(t){if("object"==typeof t&&null!==t){var e=t.$$typeof;switch(e){case n:switch(t=t.type){case f:case l:case i:case u:case a:case h:return t;default:switch(t=t&&t.$$typeof){case s:case p:case v:case y:case c:return t;default:return e}}case o:return e}}}function _(t){return O(t)===l}e.AsyncMode=f,e.ConcurrentMode=l,e.ContextConsumer=s,e.ContextProvider=c,e.Element=n,e.ForwardRef=p,e.Fragment=i,e.Lazy=v,e.Memo=y,e.Portal=o,e.Profiler=u,e.StrictMode=a,e.Suspense=h,e.isAsyncMode=function(t){return _(t)||O(t)===f},e.isConcurrentMode=_,e.isContextConsumer=function(t){return O(t)===s},e.isContextProvider=function(t){return O(t)===c},e.isElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===n},e.isForwardRef=function(t){return O(t)===p},e.isFragment=function(t){return O(t)===i},e.isLazy=function(t){return O(t)===v},e.isMemo=function(t){return O(t)===y},e.isPortal=function(t){return O(t)===o},e.isProfiler=function(t){return O(t)===u},e.isStrictMode=function(t){return O(t)===a},e.isSuspense=function(t){return O(t)===h},e.isValidElementType=function(t){return"string"==typeof t||"function"==typeof t||t===i||t===l||t===u||t===a||t===h||t===d||"object"==typeof t&&null!==t&&(t.$$typeof===v||t.$$typeof===y||t.$$typeof===c||t.$$typeof===s||t.$$typeof===p||t.$$typeof===g||t.$$typeof===m||t.$$typeof===w||t.$$typeof===b)},e.typeOf=O},864:function(t,e,r){"use strict";t.exports=r(921)},379:function(t){"use strict";var e=[];function r(t){for(var r=-1,n=0;n<e.length;n++)if(e[n].identifier===t){r=n;break}return r}function n(t,n){for(var i={},a=[],u=0;u<t.length;u++){var c=t[u],s=n.base?c[0]+n.base:c[0],f=i[s]||0,l="".concat(s," ").concat(f);i[s]=f+1;var p=r(l),h={css:c[1],media:c[2],sourceMap:c[3],supports:c[4],layer:c[5]};if(-1!==p)e[p].references++,e[p].updater(h);else{var d=o(h,n);n.byIndex=u,e.splice(u,0,{identifier:l,updater:d,references:1})}a.push(l)}return a}function o(t,e){var r=e.domAPI(e);return r.update(t),function(e){if(e){if(e.css===t.css&&e.media===t.media&&e.sourceMap===t.sourceMap&&e.supports===t.supports&&e.layer===t.layer)return;r.update(t=e)}else r.remove()}}t.exports=function(t,o){var i=n(t=t||[],o=o||{});return function(t){t=t||[];for(var a=0;a<i.length;a++){var u=r(i[a]);e[u].references--}for(var c=n(t,o),s=0;s<i.length;s++){var f=r(i[s]);0===e[f].references&&(e[f].updater(),e.splice(f,1))}i=c}}},569:function(t){"use strict";var e={};t.exports=function(t,r){var n=function(t){if(void 0===e[t]){var r=document.querySelector(t);if(window.HTMLIFrameElement&&r instanceof window.HTMLIFrameElement)try{r=r.contentDocument.head}catch(t){r=null}e[t]=r}return e[t]}(t);if(!n)throw new Error("Couldn't find a style target. This probably means that the value for the 'insert' parameter is invalid.");n.appendChild(r)}},216:function(t){"use strict";t.exports=function(t){var e=document.createElement("style");return t.setAttributes(e,t.attributes),t.insert(e,t.options),e}},565:function(t,e,r){"use strict";t.exports=function(t){var e=r.nc;e&&t.setAttribute("nonce",e)}},795:function(t){"use strict";t.exports=function(t){if("undefined"==typeof document)return{update:function(){},remove:function(){}};var e=t.insertStyleElement(t);return{update:function(r){!function(t,e,r){var n="";r.supports&&(n+="@supports (".concat(r.supports,") {")),r.media&&(n+="@media ".concat(r.media," {"));var o=void 0!==r.layer;o&&(n+="@layer".concat(r.layer.length>0?" ".concat(r.layer):""," {")),n+=r.css,o&&(n+="}"),r.media&&(n+="}"),r.supports&&(n+="}");var i=r.sourceMap;i&&"undefined"!=typeof btoa&&(n+="\n/*# sourceMappingURL=data:application/json;base64,".concat(btoa(unescape(encodeURIComponent(JSON.stringify(i))))," */")),e.styleTagTransform(n,t,e.options)}(e,t,r)},remove:function(){!function(t){if(null===t.parentNode)return!1;t.parentNode.removeChild(t)}(e)}}}},589:function(t){"use strict";t.exports=function(t,e){if(e.styleSheet)e.styleSheet.cssText=t;else{for(;e.firstChild;)e.removeChild(e.firstChild);e.appendChild(document.createTextNode(t))}}},250:function(t,e,r){"use strict";var n=r(196),o="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},i=n.useState,a=n.useEffect,u=n.useLayoutEffect,c=n.useDebugValue;function s(t){var e=t.getSnapshot;t=t.value;try{var r=e();return!o(t,r)}catch(t){return!0}}var f="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(t,e){return e()}:function(t,e){var r=e(),n=i({inst:{value:r,getSnapshot:e}}),o=n[0].inst,f=n[1];return u((function(){o.value=r,o.getSnapshot=e,s(o)&&f({inst:o})}),[t,r,e]),a((function(){return s(o)&&f({inst:o}),t((function(){s(o)&&f({inst:o})}))}),[t]),c(r),r};e.useSyncExternalStore=void 0!==n.useSyncExternalStore?n.useSyncExternalStore:f},139:function(t,e,r){"use strict";var n=r(196),o=r(688),i="function"==typeof Object.is?Object.is:function(t,e){return t===e&&(0!==t||1/t==1/e)||t!=t&&e!=e},a=o.useSyncExternalStore,u=n.useRef,c=n.useEffect,s=n.useMemo,f=n.useDebugValue;e.useSyncExternalStoreWithSelector=function(t,e,r,n,o){var l=u(null);if(null===l.current){var p={hasValue:!1,value:null};l.current=p}else p=l.current;l=s((function(){function t(t){if(!c){if(c=!0,a=t,t=n(t),void 0!==o&&p.hasValue){var e=p.value;if(o(e,t))return u=e}return u=t}if(e=u,i(a,t))return e;var r=n(t);return void 0!==o&&o(e,r)?e:(a=t,u=r)}var a,u,c=!1,s=void 0===r?null:r;return[function(){return t(e())},null===s?void 0:function(){return t(s())}]}),[e,r,n,o]);var h=a(t,l[0],l[1]);return c((function(){p.hasValue=!0,p.value=h}),[h]),f(h),h}},688:function(t,e,r){"use strict";t.exports=r(250)},798:function(t,e,r){"use strict";t.exports=r(139)},196:function(t){"use strict";t.exports=window.React}},e={};function r(n){var o=e[n];if(void 0!==o)return o.exports;var i=e[n]={id:n,exports:{}};return t[n](i,i.exports,r),i.exports}r.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return r.d(e,{a:e}),e},r.d=function(t,e){for(var n in e)r.o(e,n)&&!r.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),r.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},r.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},r.nc=void 0;var n={};!function(){"use strict";var t="undefined"!=typeof globalThis&&globalThis||"undefined"!=typeof self&&self||void 0!==r.g&&r.g||{},e="URLSearchParams"in t,n="Symbol"in t&&"iterator"in Symbol,o="FileReader"in t&&"Blob"in t&&function(){try{return new Blob,!0}catch(t){return!1}}(),i="FormData"in t,a="ArrayBuffer"in t;if(a)var u=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],c=ArrayBuffer.isView||function(t){return t&&u.indexOf(Object.prototype.toString.call(t))>-1};function s(t){if("string"!=typeof t&&(t=String(t)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(t)||""===t)throw new TypeError('Invalid character in header field name: "'+t+'"');return t.toLowerCase()}function f(t){return"string"!=typeof t&&(t=String(t)),t}function l(t){var e={next:function(){var e=t.shift();return{done:void 0===e,value:e}}};return n&&(e[Symbol.iterator]=function(){return e}),e}function p(t){this.map={},t instanceof p?t.forEach((function(t,e){this.append(e,t)}),this):Array.isArray(t)?t.forEach((function(t){if(2!=t.length)throw new TypeError("Headers constructor: expected name/value pair to be length 2, found"+t.length);this.append(t[0],t[1])}),this):t&&Object.getOwnPropertyNames(t).forEach((function(e){this.append(e,t[e])}),this)}function h(t){if(!t._noBody)return t.bodyUsed?Promise.reject(new TypeError("Already read")):void(t.bodyUsed=!0)}function d(t){return new Promise((function(e,r){t.onload=function(){e(t.result)},t.onerror=function(){r(t.error)}}))}function y(t){var e=new FileReader,r=d(e);return e.readAsArrayBuffer(t),r}function v(t){if(t.slice)return t.slice(0);var e=new Uint8Array(t.byteLength);return e.set(new Uint8Array(t)),e.buffer}function b(){return this.bodyUsed=!1,this._initBody=function(t){var r;this.bodyUsed=this.bodyUsed,this._bodyInit=t,t?"string"==typeof t?this._bodyText=t:o&&Blob.prototype.isPrototypeOf(t)?this._bodyBlob=t:i&&FormData.prototype.isPrototypeOf(t)?this._bodyFormData=t:e&&URLSearchParams.prototype.isPrototypeOf(t)?this._bodyText=t.toString():a&&o&&(r=t)&&DataView.prototype.isPrototypeOf(r)?(this._bodyArrayBuffer=v(t.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):a&&(ArrayBuffer.prototype.isPrototypeOf(t)||c(t))?this._bodyArrayBuffer=v(t):this._bodyText=t=Object.prototype.toString.call(t):(this._noBody=!0,this._bodyText=""),this.headers.get("content-type")||("string"==typeof t?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):e&&URLSearchParams.prototype.isPrototypeOf(t)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},o&&(this.blob=function(){var t=h(this);if(t)return t;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))}),this.arrayBuffer=function(){if(this._bodyArrayBuffer)return h(this)||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer));if(o)return this.blob().then(y);throw new Error("could not read as ArrayBuffer")},this.text=function(){var t,e,r,n,o,i=h(this);if(i)return i;if(this._bodyBlob)return t=this._bodyBlob,r=d(e=new FileReader),o=(n=/charset=([A-Za-z0-9_-]+)/.exec(t.type))?n[1]:"utf-8",e.readAsText(t,o),r;if(this._bodyArrayBuffer)return Promise.resolve(function(t){for(var e=new Uint8Array(t),r=new Array(e.length),n=0;n<e.length;n++)r[n]=String.fromCharCode(e[n]);return r.join("")}(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},i&&(this.formData=function(){return this.text().then(w)}),this.json=function(){return this.text().then(JSON.parse)},this}p.prototype.append=function(t,e){t=s(t),e=f(e);var r=this.map[t];this.map[t]=r?r+", "+e:e},p.prototype.delete=function(t){delete this.map[s(t)]},p.prototype.get=function(t){return t=s(t),this.has(t)?this.map[t]:null},p.prototype.has=function(t){return this.map.hasOwnProperty(s(t))},p.prototype.set=function(t,e){this.map[s(t)]=f(e)},p.prototype.forEach=function(t,e){for(var r in this.map)this.map.hasOwnProperty(r)&&t.call(e,this.map[r],r,this)},p.prototype.keys=function(){var t=[];return this.forEach((function(e,r){t.push(r)})),l(t)},p.prototype.values=function(){var t=[];return this.forEach((function(e){t.push(e)})),l(t)},p.prototype.entries=function(){var t=[];return this.forEach((function(e,r){t.push([r,e])})),l(t)},n&&(p.prototype[Symbol.iterator]=p.prototype.entries);var g=["CONNECT","DELETE","GET","HEAD","OPTIONS","PATCH","POST","PUT","TRACE"];function m(e,r){if(!(this instanceof m))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');var n,o,i=(r=r||{}).body;if(e instanceof m){if(e.bodyUsed)throw new TypeError("Already read");this.url=e.url,this.credentials=e.credentials,r.headers||(this.headers=new p(e.headers)),this.method=e.method,this.mode=e.mode,this.signal=e.signal,i||null==e._bodyInit||(i=e._bodyInit,e.bodyUsed=!0)}else this.url=String(e);if(this.credentials=r.credentials||this.credentials||"same-origin",!r.headers&&this.headers||(this.headers=new p(r.headers)),this.method=(o=(n=r.method||this.method||"GET").toUpperCase(),g.indexOf(o)>-1?o:n),this.mode=r.mode||this.mode||null,this.signal=r.signal||this.signal||function(){if("AbortController"in t)return(new AbortController).signal}(),this.referrer=null,("GET"===this.method||"HEAD"===this.method)&&i)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(i),!("GET"!==this.method&&"HEAD"!==this.method||"no-store"!==r.cache&&"no-cache"!==r.cache)){var a=/([?&])_=[^&]*/;a.test(this.url)?this.url=this.url.replace(a,"$1_="+(new Date).getTime()):this.url+=(/\?/.test(this.url)?"&":"?")+"_="+(new Date).getTime()}}function w(t){var e=new FormData;return t.trim().split("&").forEach((function(t){if(t){var r=t.split("="),n=r.shift().replace(/\+/g," "),o=r.join("=").replace(/\+/g," ");e.append(decodeURIComponent(n),decodeURIComponent(o))}})),e}function O(t,e){if(!(this instanceof O))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');if(e||(e={}),this.type="default",this.status=void 0===e.status?200:e.status,this.status<200||this.status>599)throw new RangeError("Failed to construct 'Response': The status provided (0) is outside the range [200, 599].");this.ok=this.status>=200&&this.status<300,this.statusText=void 0===e.statusText?"":""+e.statusText,this.headers=new p(e.headers),this.url=e.url||"",this._initBody(t)}m.prototype.clone=function(){return new m(this,{body:this._bodyInit})},b.call(m.prototype),b.call(O.prototype),O.prototype.clone=function(){return new O(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new p(this.headers),url:this.url})},O.error=function(){var t=new O(null,{status:200,statusText:""});return t.status=0,t.type="error",t};var _=[301,302,303,307,308];O.redirect=function(t,e){if(-1===_.indexOf(e))throw new RangeError("Invalid status code");return new O(null,{status:e,headers:{location:t}})};var E=t.DOMException;try{new E}catch(t){(E=function(t,e){this.message=t,this.name=e;var r=Error(t);this.stack=r.stack}).prototype=Object.create(Error.prototype),E.prototype.constructor=E}function j(e,r){return new Promise((function(n,i){var u=new m(e,r);if(u.signal&&u.signal.aborted)return i(new E("Aborted","AbortError"));var c=new XMLHttpRequest;function l(){c.abort()}if(c.onload=function(){var t,e,r={statusText:c.statusText,headers:(t=c.getAllResponseHeaders()||"",e=new p,t.replace(/\r?\n[\t ]+/g," ").split("\r").map((function(t){return 0===t.indexOf("\n")?t.substr(1,t.length):t})).forEach((function(t){var r=t.split(":"),n=r.shift().trim();if(n){var o=r.join(":").trim();try{e.append(n,o)}catch(t){console.warn("Response "+t.message)}}})),e)};u.url.startsWith("file://")&&(c.status<200||c.status>599)?r.status=200:r.status=c.status,r.url="responseURL"in c?c.responseURL:r.headers.get("X-Request-URL");var o="response"in c?c.response:c.responseText;setTimeout((function(){n(new O(o,r))}),0)},c.onerror=function(){setTimeout((function(){i(new TypeError("Network request failed"))}),0)},c.ontimeout=function(){setTimeout((function(){i(new TypeError("Network request timed out"))}),0)},c.onabort=function(){setTimeout((function(){i(new E("Aborted","AbortError"))}),0)},c.open(u.method,function(e){try{return""===e&&t.location.href?t.location.href:e}catch(t){return e}}(u.url),!0),"include"===u.credentials?c.withCredentials=!0:"omit"===u.credentials&&(c.withCredentials=!1),"responseType"in c&&(o?c.responseType="blob":a&&(c.responseType="arraybuffer")),r&&"object"==typeof r.headers&&!(r.headers instanceof p||t.Headers&&r.headers instanceof t.Headers)){var h=[];Object.getOwnPropertyNames(r.headers).forEach((function(t){h.push(s(t)),c.setRequestHeader(t,f(r.headers[t]))})),u.headers.forEach((function(t,e){-1===h.indexOf(e)&&c.setRequestHeader(e,t)}))}else u.headers.forEach((function(t,e){c.setRequestHeader(e,t)}));u.signal&&(u.signal.addEventListener("abort",l),c.onreadystatechange=function(){4===c.readyState&&u.signal.removeEventListener("abort",l)}),c.send(void 0===u._bodyInit?null:u._bodyInit)}))}j.polyfill=!0,t.fetch||(t.fetch=j,t.Headers=p,t.Request=m,t.Response=O)}(),function(){"use strict";r.r(n);var t=r(196),e=r.n(t),o=window.ReactDOM,i=r.n(o),a=window.PropTypes,u=r.n(a),c=r(688),s=r(798),f=function(t){t()},l=function(){return f},p=(0,t.createContext)(null);function h(){return h=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},h.apply(this,arguments)}function d(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}var y=r(679),v=r.n(y),b=r(100),g=["initMapStateToProps","initMapDispatchToProps","initMergeProps"];function m(t,e,r,n,o){var i,a,u,c,s,f=o.areStatesEqual,l=o.areOwnPropsEqual,p=o.areStatePropsEqual,h=!1;return function(o,d){return h?function(o,h){var d,y,v=!l(h,a),b=!f(o,i,h,a);return i=o,a=h,v&&b?(u=t(i,a),e.dependsOnOwnProps&&(c=e(n,a)),s=r(u,c,a)):v?(t.dependsOnOwnProps&&(u=t(i,a)),e.dependsOnOwnProps&&(c=e(n,a)),s=r(u,c,a)):b?(d=t(i,a),y=!p(d,u),u=d,y&&(s=r(u,c,a)),s):s}(o,d):(u=t(i=o,a=d),c=e(n,a),s=r(u,c,a),h=!0,s)}}function w(t){return function(e){var r=t(e);function n(){return r}return n.dependsOnOwnProps=!1,n}}function O(t){return t.dependsOnOwnProps?Boolean(t.dependsOnOwnProps):1!==t.length}function _(t,e){return function(e,r){r.displayName;var n=function(t,e){return n.dependsOnOwnProps?n.mapToProps(t,e):n.mapToProps(t,void 0)};return n.dependsOnOwnProps=!0,n.mapToProps=function(e,r){n.mapToProps=t,n.dependsOnOwnProps=O(t);var o=n(e,r);return"function"==typeof o&&(n.mapToProps=o,n.dependsOnOwnProps=O(o),o=n(e,r)),o},n}}function E(t,e){return function(r,n){throw new Error("Invalid value of type ".concat(typeof t," for ").concat(e," argument when connecting component ").concat(n.wrappedComponentName,"."))}}function j(t,e,r){return h({},r,t,e)}var S={notify(){},get:function(){return[]}};function x(t,e){var r,n=S;function o(){a.onStateChange&&a.onStateChange()}function i(){r||(r=e?e.addNestedSub(o):t.subscribe(o),n=function(){var t=l(),e=null,r=null;return{clear(){e=null,r=null},notify(){t((function(){for(var t=e;t;)t.callback(),t=t.next}))},get(){for(var t=[],r=e;r;)t.push(r),r=r.next;return t},subscribe(t){var n=!0,o=r={callback:t,next:null,prev:r};return o.prev?o.prev.next=o:e=o,function(){n&&null!==e&&(n=!1,o.next?o.next.prev=o.prev:r=o.prev,o.prev?o.prev.next=o.next:e=o.next)}}}}())}var a={addNestedSub:function(t){return i(),n.subscribe(t)},notifyNestedSubs:function(){n.notify()},handleChangeWrapper:o,isSubscribed:function(){return Boolean(r)},trySubscribe:i,tryUnsubscribe:function(){r&&(r(),r=void 0,n.clear(),n=S)},getListeners:function(){return n}};return a}var P="undefined"!=typeof window&&void 0!==window.document&&void 0!==window.document.createElement?t.useLayoutEffect:t.useEffect;function A(t,e){return t===e?0!==t||0!==e||1/t==1/e:t!=t&&e!=e}function k(t,e){if(A(t,e))return!0;if("object"!=typeof t||null===t||"object"!=typeof e||null===e)return!1;var r=Object.keys(t),n=Object.keys(e);if(r.length!==n.length)return!1;for(var o=0;o<r.length;o++)if(!Object.prototype.hasOwnProperty.call(e,r[o])||!A(t[r[o]],e[r[o]]))return!1;return!0}function T(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(t,e)||L(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function L(t,e){if(t){if("string"==typeof t)return I(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?I(t,e):void 0}}function I(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var R=["reactReduxForwardedRef"],C=function(){throw new Error("uSES not initialized!")},N=[null,null];function D(t,e,r,n,o,i){t.current=n,r.current=!1,o.current&&(o.current=null,i())}function M(t,e){return t===e}var U,q=function(r,n,o){var i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},a=(i.pure,i.areStatesEqual),u=void 0===a?M:a,c=i.areOwnPropsEqual,s=void 0===c?k:c,f=i.areStatePropsEqual,l=void 0===f?k:f,y=i.areMergedPropsEqual,O=void 0===y?k:y,S=i.forwardRef,A=void 0!==S&&S,U=i.context,q=void 0===U?p:U,G=function(t){return t?"function"==typeof t?_(t):E(t,"mapStateToProps"):w((function(){return{}}))}(r),B=function(t){return t&&"object"==typeof t?w((function(e){return function(t,e){var r={},n=function(){var n=t[o];"function"==typeof n&&(r[o]=function(){return e(n.apply(void 0,arguments))})};for(var o in t)n();return r}(t,e)})):t?"function"==typeof t?_(t):E(t,"mapDispatchToProps"):w((function(t){return{dispatch:t}}))}(n),F=function(t){return t?"function"==typeof t?function(t){return function(e,r){r.displayName;var n,o=r.areMergedPropsEqual,i=!1;return function(e,r,a){var u=t(e,r,a);return i?o(u,n)||(n=u):(i=!0,n=u),n}}}(t):E(t,"mergeProps"):function(){return j}}(o),H=Boolean(r);return function(r){var n=r.displayName||r.name||"Component",o="Connect(".concat(n,")"),i={shouldHandleStateChanges:H,displayName:o,wrappedComponentName:n,WrappedComponent:r,initMapStateToProps:G,initMapDispatchToProps:B,initMergeProps:F,areStatesEqual:u,areStatePropsEqual:l,areOwnPropsEqual:s,areMergedPropsEqual:O};function a(n){var o=(0,t.useMemo)((function(){var t=n.reactReduxForwardedRef,e=d(n,R);return[n.context,t,e]}),[n]),a=T(o,3),u=a[0],c=a[1],s=a[2],f=(0,t.useMemo)((function(){return u&&u.Consumer&&(0,b.isContextConsumer)(e().createElement(u.Consumer,null))?u:q}),[u,q]),l=(0,t.useContext)(f),p=Boolean(n.store)&&Boolean(n.store.getState)&&Boolean(n.store.dispatch),y=Boolean(l)&&Boolean(l.store),v=p?n.store:l.store,w=y?l.getServerState:v.getState,O=(0,t.useMemo)((function(){return function(t,e){var r=e.initMapStateToProps,n=e.initMapDispatchToProps,o=e.initMergeProps,i=d(e,g);return m(r(t,i),n(t,i),o(t,i),t,i)}(v.dispatch,i)}),[v]),_=(0,t.useMemo)((function(){if(!H)return N;var t=x(v,p?void 0:l.subscription),e=t.notifyNestedSubs.bind(t);return[t,e]}),[v,p,l]),E=T(_,2),j=E[0],S=E[1],A=(0,t.useMemo)((function(){return p?l:h({},l,{subscription:j})}),[p,l,j]),k=(0,t.useRef)(),M=(0,t.useRef)(s),U=(0,t.useRef)(),G=(0,t.useRef)(!1),B=((0,t.useRef)(!1),(0,t.useRef)(!1)),F=(0,t.useRef)();P((function(){return B.current=!0,function(){B.current=!1}}),[]);var $,z,J,K=(0,t.useMemo)((function(){return function(){return U.current&&s===M.current?U.current:O(v.getState(),s)}}),[v,s]),Y=(0,t.useMemo)((function(){return function(t){return j?function(t,e,r,n,o,i,a,u,c,s,f){if(!t)return function(){};var l=!1,p=null,h=function(){if(!l&&u.current){var t,r,h=e.getState();try{t=n(h,o.current)}catch(t){r=t,p=t}r||(p=null),t===i.current?a.current||s():(i.current=t,c.current=t,a.current=!0,f())}};return r.onStateChange=h,r.trySubscribe(),h(),function(){if(l=!0,r.tryUnsubscribe(),r.onStateChange=null,p)throw p}}(H,v,j,O,M,k,G,B,U,S,t):function(){}}}),[j]);$=D,z=[M,k,G,s,U,S],P((function(){return $.apply(void 0,function(t){if(Array.isArray(t))return I(t)}(t=z)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||L(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}());var t}),undefined);try{J=C(Y,K,w?function(){return O(w(),s)}:K)}catch(t){throw F.current&&(t.message+="\nThe error may be correlated with this previous error:\n".concat(F.current.stack,"\n\n")),t}P((function(){F.current=void 0,U.current=void 0,k.current=J}));var W=(0,t.useMemo)((function(){return e().createElement(r,h({},J,{ref:c}))}),[c,r,J]);return(0,t.useMemo)((function(){return H?e().createElement(f.Provider,{value:A},W):W}),[f,W,A])}var c=e().memo(a);if(c.WrappedComponent=r,c.displayName=a.displayName=o,A){var f=e().forwardRef((function(t,r){return e().createElement(c,h({},t,{reactReduxForwardedRef:r}))}));return f.displayName=o,f.WrappedComponent=r,v()(f,r)}return v()(c,r)}},G=function(r){var n=r.store,o=r.context,i=r.children,a=r.serverState,u=(0,t.useMemo)((function(){var t=x(n);return{store:n,subscription:t,getServerState:a?function(){return a}:void 0}}),[n,a]),c=(0,t.useMemo)((function(){return n.getState()}),[n]);P((function(){var t=u.subscription;return t.onStateChange=t.notifyNestedSubs,t.trySubscribe(),c!==n.getState()&&t.notifyNestedSubs(),function(){t.tryUnsubscribe(),t.onStateChange=void 0}}),[u,c]);var s=o||p;return e().createElement(s.Provider,{value:u},i)};function B(t,e){switch(t){case 0:return function(){return e.apply(this,arguments)};case 1:return function(t){return e.apply(this,arguments)};case 2:return function(t,r){return e.apply(this,arguments)};case 3:return function(t,r,n){return e.apply(this,arguments)};case 4:return function(t,r,n,o){return e.apply(this,arguments)};case 5:return function(t,r,n,o,i){return e.apply(this,arguments)};case 6:return function(t,r,n,o,i,a){return e.apply(this,arguments)};case 7:return function(t,r,n,o,i,a,u){return e.apply(this,arguments)};case 8:return function(t,r,n,o,i,a,u,c){return e.apply(this,arguments)};case 9:return function(t,r,n,o,i,a,u,c,s){return e.apply(this,arguments)};case 10:return function(t,r,n,o,i,a,u,c,s,f){return e.apply(this,arguments)};default:throw new Error("First argument to _arity must be a non-negative integer no greater than ten")}}function F(t){return null!=t&&"object"==typeof t&&!0===t["@@functional/placeholder"]}function H(t){return function e(r){return 0===arguments.length||F(r)?e:t.apply(this,arguments)}}s.useSyncExternalStoreWithSelector,function(t){C=t}(c.useSyncExternalStore),U=o.unstable_batchedUpdates,f=U;var $=H((function(t){var e,r=!1;return B(t.length,(function(){return r?e:(r=!0,e=t.apply(this,arguments))}))})),z=$;function J(t){return J="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},J(t)}function K(t,e,r){return(e=function(t){var e=function(t,e){if("object"!==J(t)||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!==J(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"===J(e)?e:String(e)}(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function Y(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function W(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Y(Object(r),!0).forEach((function(e){K(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Y(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function V(t){return"Minified Redux error #"+t+"; visit https://redux.js.org/Errors?code="+t+" for the full message or use the non-minified dev environment for full errors. "}var Z="function"==typeof Symbol&&Symbol.observable||"@@observable",Q=function(){return Math.random().toString(36).substring(7).split("").join(".")},X={INIT:"@@redux/INIT"+Q(),REPLACE:"@@redux/REPLACE"+Q(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+Q()}};function tt(t,e,r){var n;if("function"==typeof e&&"function"==typeof r||"function"==typeof r&&"function"==typeof arguments[3])throw new Error(V(0));if("function"==typeof e&&void 0===r&&(r=e,e=void 0),void 0!==r){if("function"!=typeof r)throw new Error(V(1));return r(tt)(t,e)}if("function"!=typeof t)throw new Error(V(2));var o=t,i=e,a=[],u=a,c=!1;function s(){u===a&&(u=a.slice())}function f(){if(c)throw new Error(V(3));return i}function l(t){if("function"!=typeof t)throw new Error(V(4));if(c)throw new Error(V(5));var e=!0;return s(),u.push(t),function(){if(e){if(c)throw new Error(V(6));e=!1,s();var r=u.indexOf(t);u.splice(r,1),a=null}}}function p(t){if(!function(t){if("object"!=typeof t||null===t)return!1;for(var e=t;null!==Object.getPrototypeOf(e);)e=Object.getPrototypeOf(e);return Object.getPrototypeOf(t)===e}(t))throw new Error(V(7));if(void 0===t.type)throw new Error(V(8));if(c)throw new Error(V(9));try{c=!0,i=o(i,t)}finally{c=!1}for(var e=a=u,r=0;r<e.length;r++)(0,e[r])();return t}return p({type:X.INIT}),(n={dispatch:p,subscribe:l,getState:f,replaceReducer:function(t){if("function"!=typeof t)throw new Error(V(10));o=t,p({type:X.REPLACE})}})[Z]=function(){var t,e=l;return t={subscribe:function(t){if("object"!=typeof t||null===t)throw new Error(V(11));function r(){t.next&&t.next(f())}return r(),{unsubscribe:e(r)}}},t[Z]=function(){return this},t},n}function et(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return 0===e.length?function(t){return t}:1===e.length?e[0]:e.reduce((function(t,e){return function(){return t(e.apply(void 0,arguments))}}))}function rt(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];return function(t){return function(){var r=t.apply(void 0,arguments),n=function(){throw new Error(V(15))},o={getState:r.getState,dispatch:function(){return n.apply(void 0,arguments)}},i=e.map((function(t){return t(o)}));return n=et.apply(void 0,i)(r.dispatch),W(W({},r),{},{dispatch:n})}}}function nt(t){return function(e){var r=e.dispatch,n=e.getState;return function(e){return function(o){return"function"==typeof o?o(r,n,t):e(o)}}}}var ot=nt();ot.withExtraArgument=nt;var it=ot,at=Array.isArray||function(t){return null!=t&&t.length>=0&&"[object Array]"===Object.prototype.toString.call(t)};function ut(t,e){return function(){var r=arguments.length;if(0===r)return e();var n=arguments[r-1];return at(n)||"function"!=typeof n[t]?e.apply(this,arguments):n[t].apply(n,Array.prototype.slice.call(arguments,0,r-1))}}function ct(t){return function e(r,n){switch(arguments.length){case 0:return e;case 1:return F(r)?e:H((function(e){return t(r,e)}));default:return F(r)&&F(n)?e:F(r)?H((function(e){return t(e,n)})):F(n)?H((function(e){return t(r,e)})):t(r,n)}}}var st=ct(ut("forEach",(function(t,e){for(var r=e.length,n=0;n<r;)t(e[n]),n+=1;return e}))),ft=st,lt=Number.isInteger||function(t){return t<<0===t};function pt(t){return"[object String]"===Object.prototype.toString.call(t)}var ht=ct((function(t,e){var r=t<0?e.length+t:t;return pt(e)?e.charAt(r):e[r]})),dt=ct((function(t,e){return t.map((function(t){for(var r,n=e,o=0;o<t.length;){if(null==n)return;r=t[o],n=lt(r)?ht(r,n):n[r],o+=1}return n}))})),yt=ct((function(t,e){return dt([t],e)[0]}));function vt(t,e){return Object.prototype.hasOwnProperty.call(e,t)}var bt=Object.prototype.toString,gt=function(){return"[object Arguments]"===bt.call(arguments)?function(t){return"[object Arguments]"===bt.call(t)}:function(t){return vt("callee",t)}}(),mt=gt,wt=!{toString:null}.propertyIsEnumerable("toString"),Ot=["constructor","valueOf","isPrototypeOf","toString","propertyIsEnumerable","hasOwnProperty","toLocaleString"],_t=function(){return arguments.propertyIsEnumerable("length")}(),Et=function(t,e){for(var r=0;r<t.length;){if(t[r]===e)return!0;r+=1}return!1},jt="function"!=typeof Object.keys||_t?H((function(t){if(Object(t)!==t)return[];var e,r,n=[],o=_t&&mt(t);for(e in t)!vt(e,t)||o&&"length"===e||(n[n.length]=e);if(wt)for(r=Ot.length-1;r>=0;)vt(e=Ot[r],t)&&!Et(n,e)&&(n[n.length]=e),r-=1;return n})):H((function(t){return Object(t)!==t?[]:Object.keys(t)}));function St(t){return"[object Object]"===Object.prototype.toString.call(t)}var xt=H((function(t){return null!=t&&"function"==typeof t["fantasy-land/empty"]?t["fantasy-land/empty"]():null!=t&&null!=t.constructor&&"function"==typeof t.constructor["fantasy-land/empty"]?t.constructor["fantasy-land/empty"]():null!=t&&"function"==typeof t.empty?t.empty():null!=t&&null!=t.constructor&&"function"==typeof t.constructor.empty?t.constructor.empty():at(t)?[]:pt(t)?"":St(t)?{}:mt(t)?function(){return arguments}():(e=t,"[object Uint8ClampedArray]"===(r=Object.prototype.toString.call(e))||"[object Int8Array]"===r||"[object Uint8Array]"===r||"[object Int16Array]"===r||"[object Uint16Array]"===r||"[object Int32Array]"===r||"[object Uint32Array]"===r||"[object Float32Array]"===r||"[object Float64Array]"===r||"[object BigInt64Array]"===r||"[object BigUint64Array]"===r?t.constructor.from(""):void 0);var e,r})),Pt=xt;function At(t){for(var e,r=[];!(e=t.next()).done;)r.push(e.value);return r}function kt(t,e,r){for(var n=0,o=r.length;n<o;){if(t(e,r[n]))return!0;n+=1}return!1}var Tt="function"==typeof Object.is?Object.is:function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e},Lt=H((function(t){return null===t?"Null":void 0===t?"Undefined":Object.prototype.toString.call(t).slice(8,-1)}));function It(t,e,r,n){var o=At(t);function i(t,e){return Rt(t,e,r.slice(),n.slice())}return!kt((function(t,e){return!kt(i,e,t)}),At(e),o)}function Rt(t,e,r,n){if(Tt(t,e))return!0;var o,i,a=Lt(t);if(a!==Lt(e))return!1;if("function"==typeof t["fantasy-land/equals"]||"function"==typeof e["fantasy-land/equals"])return"function"==typeof t["fantasy-land/equals"]&&t["fantasy-land/equals"](e)&&"function"==typeof e["fantasy-land/equals"]&&e["fantasy-land/equals"](t);if("function"==typeof t.equals||"function"==typeof e.equals)return"function"==typeof t.equals&&t.equals(e)&&"function"==typeof e.equals&&e.equals(t);switch(a){case"Arguments":case"Array":case"Object":if("function"==typeof t.constructor&&"Promise"===(o=t.constructor,null==(i=String(o).match(/^function (\w*)/))?"":i[1]))return t===e;break;case"Boolean":case"Number":case"String":if(typeof t!=typeof e||!Tt(t.valueOf(),e.valueOf()))return!1;break;case"Date":if(!Tt(t.valueOf(),e.valueOf()))return!1;break;case"Error":return t.name===e.name&&t.message===e.message;case"RegExp":if(t.source!==e.source||t.global!==e.global||t.ignoreCase!==e.ignoreCase||t.multiline!==e.multiline||t.sticky!==e.sticky||t.unicode!==e.unicode)return!1}for(var u=r.length-1;u>=0;){if(r[u]===t)return n[u]===e;u-=1}switch(a){case"Map":return t.size===e.size&&It(t.entries(),e.entries(),r.concat([t]),n.concat([e]));case"Set":return t.size===e.size&&It(t.values(),e.values(),r.concat([t]),n.concat([e]));case"Arguments":case"Array":case"Object":case"Boolean":case"Number":case"String":case"Date":case"Error":case"RegExp":case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"ArrayBuffer":break;default:return!1}var c=jt(t);if(c.length!==jt(e).length)return!1;var s=r.concat([t]),f=n.concat([e]);for(u=c.length-1;u>=0;){var l=c[u];if(!vt(l,e)||!Rt(e[l],t[l],s,f))return!1;u-=1}return!0}var Ct=ct((function(t,e){return Rt(t,e,[],[])})),Nt=H((function(t){return null!=t&&Ct(t,Pt(t))}));function Dt(t){return function e(r,n,o){switch(arguments.length){case 0:return e;case 1:return F(r)?e:ct((function(e,n){return t(r,e,n)}));case 2:return F(r)&&F(n)?e:F(r)?ct((function(e,r){return t(e,n,r)})):F(n)?ct((function(e,n){return t(r,e,n)})):H((function(e){return t(r,n,e)}));default:return F(r)&&F(n)&&F(o)?e:F(r)&&F(n)?ct((function(e,r){return t(e,r,o)})):F(r)&&F(o)?ct((function(e,r){return t(e,n,r)})):F(n)&&F(o)?ct((function(e,n){return t(r,e,n)})):F(r)?H((function(e){return t(e,n,o)})):F(n)?H((function(e){return t(r,e,o)})):F(o)?H((function(e){return t(r,n,e)})):t(r,n,o)}}}var Mt=Dt((function(t,e,r){var n,o={};for(n in r=r||{},e=e||{})vt(n,e)&&(o[n]=vt(n,r)?t(n,e[n],r[n]):e[n]);for(n in r)vt(n,r)&&!vt(n,o)&&(o[n]=r[n]);return o})),Ut=Mt,qt=Dt((function(t,e,r){return Ut((function(e,r,n){return t(r,n)}),e,r)})),Gt=qt,Bt=ct((function(t,e){return t.map((function(t){return yt([t],e)}))}));function Ft(t,e,r){for(var n=0,o=r.length;n<o;)e=t(e,r[n]),n+=1;return e}function Ht(t,e,r){return function(){if(0===arguments.length)return r();var n=arguments[arguments.length-1];if(!at(n)){for(var o=0;o<t.length;){if("function"==typeof n[t[o]])return n[t[o]].apply(n,Array.prototype.slice.call(arguments,0,-1));o+=1}if(function(t){return null!=t&&"function"==typeof t["@@transducer/step"]}(n))return e.apply(null,Array.prototype.slice.call(arguments,0,-1))(n)}return r.apply(this,arguments)}}function $t(t,e){for(var r=0,n=e.length,o=[];r<n;)t(e[r])&&(o[o.length]=e[r]),r+=1;return o}var zt=function(){return this.xf["@@transducer/init"]()},Jt=function(t){return this.xf["@@transducer/result"](t)},Kt=function(){function t(t,e){this.xf=e,this.f=t}return t.prototype["@@transducer/init"]=zt,t.prototype["@@transducer/result"]=Jt,t.prototype["@@transducer/step"]=function(t,e){return this.f(e)?this.xf["@@transducer/step"](t,e):t},t}();function Yt(t){return function(e){return new Kt(t,e)}}var Wt=ct(Ht(["fantasy-land/filter","filter"],Yt,(function(t,e){return St(e)?Ft((function(r,n){return t(e[n])&&(r[n]=e[n]),r}),{},jt(e)):$t(t,e)}))),Vt=H((function(t){return!!at(t)||!!t&&"object"==typeof t&&!pt(t)&&(0===t.length||t.length>0&&t.hasOwnProperty(0)&&t.hasOwnProperty(t.length-1))}));function Zt(t){return function e(r){for(var n,o,i,a=[],u=0,c=r.length;u<c;){if(Vt(r[u]))for(i=0,o=(n=t?e(r[u]):r[u]).length;i<o;)a[a.length]=n[i],i+=1;else a[a.length]=r[u];u+=1}return a}}var Qt=H(Zt(!0));function Xt(t,e){for(var r=0,n=e.length,o=Array(n);r<n;)o[r]=t(e[r]),r+=1;return o}var te=function(){function t(t,e){this.xf=e,this.f=t}return t.prototype["@@transducer/init"]=zt,t.prototype["@@transducer/result"]=Jt,t.prototype["@@transducer/step"]=function(t,e){return this.xf["@@transducer/step"](t,this.f(e))},t}(),ee=function(t){return function(e){return new te(t,e)}};function re(t,e,r){return function(){for(var n=[],o=0,i=t,a=0,u=!1;a<e.length||o<arguments.length;){var c;a<e.length&&(!F(e[a])||o>=arguments.length)?c=e[a]:(c=arguments[o],o+=1),n[a]=c,F(c)?u=!0:i-=1,a+=1}return!u&&i<=0?r.apply(this,n):B(Math.max(0,i),re(t,n,r))}}var ne=ct((function(t,e){return 1===t?H(e):B(t,re(t,[],e))})),oe=ne,ie=ct(Ht(["fantasy-land/map","map"],ee,(function(t,e){switch(Object.prototype.toString.call(e)){case"[object Function]":return oe(e.length,(function(){return t.call(this,e.apply(this,arguments))}));case"[object Object]":return Ft((function(r,n){return r[n]=t(e[n]),r}),{},jt(e));default:return Xt(t,e)}}))),ae=ie,ue="undefined"!=typeof Symbol?Symbol.iterator:"@@iterator";function ce(t,e,r){return function(n,o,i){if(Vt(i))return t(n,o,i);if(null==i)return o;if("function"==typeof i["fantasy-land/reduce"])return e(n,o,i,"fantasy-land/reduce");if(null!=i[ue])return r(n,o,i[ue]());if("function"==typeof i.next)return r(n,o,i);if("function"==typeof i.reduce)return e(n,o,i,"reduce");throw new TypeError("reduce: list must be array or iterable")}}function se(t,e,r){for(var n=0,o=r.length;n<o;){if((e=t["@@transducer/step"](e,r[n]))&&e["@@transducer/reduced"]){e=e["@@transducer/value"];break}n+=1}return t["@@transducer/result"](e)}var fe=ct((function(t,e){return B(t.length,(function(){return t.apply(e,arguments)}))})),le=fe;function pe(t,e,r){for(var n=r.next();!n.done;){if((e=t["@@transducer/step"](e,n.value))&&e["@@transducer/reduced"]){e=e["@@transducer/value"];break}n=r.next()}return t["@@transducer/result"](e)}function he(t,e,r,n){return t["@@transducer/result"](r[n](le(t["@@transducer/step"],t),e))}var de=ce(se,he,pe),ye=function(){function t(t){this.f=t}return t.prototype["@@transducer/init"]=function(){throw new Error("init not implemented on XWrap")},t.prototype["@@transducer/result"]=function(t){return t},t.prototype["@@transducer/step"]=function(t,e){return this.f(t,e)},t}();function ve(t){return new ye(t)}var be=Dt((function(t,e,r){return de("function"==typeof t?ve(t):t,e,r)})),ge=H((function(t){return null==t})),me=Dt((function t(e,r,n){if(0===e.length)return r;var o=e[0];if(e.length>1){var i=!ge(n)&&vt(o,n)&&"object"==typeof n[o]?n[o]:lt(e[1])?[]:{};r=t(Array.prototype.slice.call(e,1),r,i)}return function(t,e,r){if(lt(t)&&at(r)){var n=[].concat(r);return n[t]=e,n}var o={};for(var i in r)o[i]=r[i];return o[t]=e,o}(o,r,n)})),we=Dt((function(t,e,r){return me([t],e,r)}));function Oe(t){var e=Object.prototype.toString.call(t);return"[object Function]"===e||"[object AsyncFunction]"===e||"[object GeneratorFunction]"===e||"[object AsyncGeneratorFunction]"===e}function _e(t,e,r){var n,o;if("function"==typeof t.indexOf)switch(typeof e){case"number":if(0===e){for(n=1/e;r<t.length;){if(0===(o=t[r])&&1/o===n)return r;r+=1}return-1}if(e!=e){for(;r<t.length;){if("number"==typeof(o=t[r])&&o!=o)return r;r+=1}return-1}return t.indexOf(e,r);case"string":case"boolean":case"function":case"undefined":return t.indexOf(e,r);case"object":if(null===e)return t.indexOf(e,r)}for(;r<t.length;){if(Ct(t[r],e))return r;r+=1}return-1}function Ee(t,e){return _e(e,t,0)>=0}function je(t){return'"'+t.replace(/\\/g,"\\\\").replace(/[\b]/g,"\\b").replace(/\f/g,"\\f").replace(/\n/g,"\\n").replace(/\r/g,"\\r").replace(/\t/g,"\\t").replace(/\v/g,"\\v").replace(/\0/g,"\\0").replace(/"/g,'\\"')+'"'}var Se=function(t){return(t<10?"0":"")+t},xe="function"==typeof Date.prototype.toISOString?function(t){return t.toISOString()}:function(t){return t.getUTCFullYear()+"-"+Se(t.getUTCMonth()+1)+"-"+Se(t.getUTCDate())+"T"+Se(t.getUTCHours())+":"+Se(t.getUTCMinutes())+":"+Se(t.getUTCSeconds())+"."+(t.getUTCMilliseconds()/1e3).toFixed(3).slice(2,5)+"Z"},Pe=ct((function(t,e){return Wt((r=t,function(){return!r.apply(this,arguments)}),e);var r})),Ae=Pe;function ke(t,e){var r=function(r){var n=e.concat([t]);return Ee(r,n)?"<Circular>":ke(r,n)},n=function(t,e){return Xt((function(e){return je(e)+": "+r(t[e])}),e.slice().sort())};switch(Object.prototype.toString.call(t)){case"[object Arguments]":return"(function() { return arguments; }("+Xt(r,t).join(", ")+"))";case"[object Array]":return"["+Xt(r,t).concat(n(t,Ae((function(t){return/^\d+$/.test(t)}),jt(t)))).join(", ")+"]";case"[object Boolean]":return"object"==typeof t?"new Boolean("+r(t.valueOf())+")":t.toString();case"[object Date]":return"new Date("+(isNaN(t.valueOf())?r(NaN):je(xe(t)))+")";case"[object Map]":return"new Map("+r(Array.from(t))+")";case"[object Null]":return"null";case"[object Number]":return"object"==typeof t?"new Number("+r(t.valueOf())+")":1/t==-1/0?"-0":t.toString(10);case"[object Set]":return"new Set("+r(Array.from(t).sort())+")";case"[object String]":return"object"==typeof t?"new String("+r(t.valueOf())+")":je(t);case"[object Undefined]":return"undefined";default:if("function"==typeof t.toString){var o=t.toString();if("[object Object]"!==o)return o}return"{"+n(t,jt(t)).join(", ")+"}"}}var Te=H((function(t){return ke(t,[])})),Le=ct((function(t,e){if(at(t)){if(at(e))return t.concat(e);throw new TypeError(Te(e)+" is not an array")}if(pt(t)){if(pt(e))return t+e;throw new TypeError(Te(e)+" is not a string")}if(null!=t&&Oe(t["fantasy-land/concat"]))return t["fantasy-land/concat"](e);if(null!=t&&Oe(t.concat))return t.concat(e);throw new TypeError(Te(t)+' does not have a method named "concat" or "fantasy-land/concat"')}));function Ie(t){return t&&t["@@transducer/reduced"]?t:{"@@transducer/value":t,"@@transducer/reduced":!0}}var Re=function(){function t(t,e){this.xf=e,this.f=t,this.all=!0}return t.prototype["@@transducer/init"]=zt,t.prototype["@@transducer/result"]=function(t){return this.all&&(t=this.xf["@@transducer/step"](t,!0)),this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,e){return this.f(e)||(this.all=!1,t=Ie(this.xf["@@transducer/step"](t,!1))),t},t}();function Ce(t){return function(e){return new Re(t,e)}}var Ne=ct(Ht(["all"],Ce,(function(t,e){for(var r=0;r<e.length;){if(!t(e[r]))return!1;r+=1}return!0}))),De=Ne;function Me(t,e,r){var n,o=typeof t;switch(o){case"string":case"number":return 0===t&&1/t==-1/0?!!r._items["-0"]||(e&&(r._items["-0"]=!0),!1):null!==r._nativeSet?e?(n=r._nativeSet.size,r._nativeSet.add(t),r._nativeSet.size===n):r._nativeSet.has(t):o in r._items?t in r._items[o]||(e&&(r._items[o][t]=!0),!1):(e&&(r._items[o]={},r._items[o][t]=!0),!1);case"boolean":if(o in r._items){var i=t?1:0;return!!r._items[o][i]||(e&&(r._items[o][i]=!0),!1)}return e&&(r._items[o]=t?[!1,!0]:[!0,!1]),!1;case"function":return null!==r._nativeSet?e?(n=r._nativeSet.size,r._nativeSet.add(t),r._nativeSet.size===n):r._nativeSet.has(t):o in r._items?!!Ee(t,r._items[o])||(e&&r._items[o].push(t),!1):(e&&(r._items[o]=[t]),!1);case"undefined":return!!r._items[o]||(e&&(r._items[o]=!0),!1);case"object":if(null===t)return!!r._items.null||(e&&(r._items.null=!0),!1);default:return(o=Object.prototype.toString.call(t))in r._items?!!Ee(t,r._items[o])||(e&&r._items[o].push(t),!1):(e&&(r._items[o]=[t]),!1)}}var Ue=function(){function t(){this._nativeSet="function"==typeof Set?new Set:null,this._items={}}return t.prototype.add=function(t){return!Me(t,!0,this)},t.prototype.has=function(t){return Me(t,!1,this)},t}(),qe=ct((function(t,e){for(var r=[],n=0,o=t.length,i=e.length,a=new Ue,u=0;u<i;u+=1)a.add(e[u]);for(;n<o;)a.add(t[n])&&(r[r.length]=t[n]),n+=1;return r})),Ge=ct((function(t,e){if(t===e)return e;function r(t,e){if(t>e!=e>t)return e>t?e:t}var n=r(t,e);if(void 0!==n)return n;var o=r(typeof t,typeof e);if(void 0!==o)return o===typeof t?t:e;var i=Te(t),a=r(i,Te(e));return void 0!==a&&a===i?t:e})),Be=ct((function(t,e){if(null!=e)return lt(t)?ht(t,e):e[t]})),Fe=ct((function(t,e){return ae(Be(t),e)})),He=ct((function(t,e){return oe(be(Ge,0,Fe("length",e)),(function(){var r=arguments,n=this;return t.apply(n,Xt((function(t){return t.apply(n,r)}),e))}))})),$e=He,ze=H((function(t){return $e((function(){return Array.prototype.slice.call(arguments,0)}),t)})),Je=ze([Wt,Ae]),Ke=ct((function(t,e){var r={};for(var n in e)t(e[n],n,e)&&(r[n]=e[n]);return r})),Ye=ct((function(t,e){for(var r=0,n=Math.min(t.length,e.length),o={};r<n;)o[t[r]]=e[r],r+=1;return o})),We=Ye,Ve=r(357),Ze=r(924),Qe=r.n(Ze),Xe=ct((function(t,e){for(var r=jt(e),n=0;n<r.length;){var o=r[n];t(e[o],o,e),n+=1}return e})),tr=Xe,er=ct(Ee),rr=ct((function(t,e){for(var r=[],n=0,o=Math.min(t.length,e.length);n<o;)r[n]=[t[n],e[n]],n+=1;return r}));function nr(t){return t}var or=H(nr),ir=function(){function t(t,e){this.xf=e,this.f=t,this.set=new Ue}return t.prototype["@@transducer/init"]=zt,t.prototype["@@transducer/result"]=Jt,t.prototype["@@transducer/step"]=function(t,e){return this.set.add(this.f(e))?this.xf["@@transducer/step"](t,e):t},t}();function ar(t){return function(e){return new ir(t,e)}}var ur=ct(Ht([],ar,(function(t,e){for(var r,n,o=new Ue,i=[],a=0;a<e.length;)r=t(n=e[a]),o.add(r)&&i.push(n),a+=1;return i}))),cr=ur(or),sr=ct((function(t,e){for(var r=new Ue,n=0;n<t.length;n+=1)r.add(t[n]);return cr($t(r.has.bind(r),e))})),fr=H((function(t){for(var e=jt(t),r=e.length,n=[],o=0;o<r;)n[o]=t[e[o]],o+=1;return n})),lr=ct((function t(e,r){if(!St(r)&&!at(r))return r;var n,o,i,a=r instanceof Array?[]:{};for(o in r)i=typeof(n=e[o]),a[o]="function"===i?n(r[o]):n&&"object"===i?t(n,r[o]):r[o];return a}));function pr(t,e){var r;e=e||[];var n=(t=t||[]).length,o=e.length,i=[];for(r=0;r<n;)i[i.length]=t[r],r+=1;for(r=0;r<o;)i[i.length]=e[r],r+=1;return i}function hr(t,e,r){for(var n=r.next();!n.done;)e=t(e,n.value),n=r.next();return e}function dr(t,e,r,n){return r[n](t,e)}var yr=ce(Ft,dr,hr),vr=ct((function(t,e){return"function"==typeof e["fantasy-land/ap"]?e["fantasy-land/ap"](t):"function"==typeof t.ap?t.ap(e):"function"==typeof t?function(r){return t(r)(e(r))}:yr((function(t,r){return pr(t,ae(r,e))}),[],t)})),br=function(){function t(t,e){this.xf=e,this.f=t,this.idx=-1,this.found=!1}return t.prototype["@@transducer/init"]=zt,t.prototype["@@transducer/result"]=function(t){return this.found||(t=this.xf["@@transducer/step"](t,-1)),this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,e){return this.idx+=1,this.f(e)&&(this.found=!0,t=Ie(this.xf["@@transducer/step"](t,this.idx))),t},t}();function gr(t){return function(e){return new br(t,e)}}var mr=ct(Ht([],gr,(function(t,e){for(var r=0,n=e.length;r<n;){if(t(e[r]))return r;r+=1}return-1}))),wr=mr,Or="function"==typeof Object.assign?Object.assign:function(t){if(null==t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),r=1,n=arguments.length;r<n;){var o=arguments[r];if(null!=o)for(var i in o)vt(i,o)&&(e[i]=o[i]);r+=1}return e},_r=ct((function(t,e){return Or({},t,e)})),Er=function(){function t(t,e){this.xf=e,this.f=t,this.any=!1}return t.prototype["@@transducer/init"]=zt,t.prototype["@@transducer/result"]=function(t){return this.any||(t=this.xf["@@transducer/step"](t,!1)),this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,e){return this.f(e)&&(this.any=!0,t=Ie(this.xf["@@transducer/step"](t,!0))),t},t}();function jr(t){return function(e){return new Er(t,e)}}var Sr=ct(Ht(["any"],jr,(function(t,e){for(var r=0;r<e.length;){if(t(e[r]))return!0;r+=1}return!1}))),xr=Sr,Pr=function(){function t(t,e){this.xf=e,this.n=t,this.i=0}return t.prototype["@@transducer/init"]=zt,t.prototype["@@transducer/result"]=Jt,t.prototype["@@transducer/step"]=function(t,e){this.i+=1;var r=0===this.n?t:this.xf["@@transducer/step"](t,e);return this.n>=0&&this.i>=this.n?Ie(r):r},t}();function Ar(t){return function(e){return new Pr(t,e)}}var kr=Dt(ut("slice",(function(t,e,r){return Array.prototype.slice.call(r,t,e)}))),Tr=ct(Ht(["take"],Ar,(function(t,e){return kr(0,t<0?1/0:t,e)}))),Lr=ct((function(t,e){return Ct(Tr(t.length,e),t)})),Ir=ct((function(t,e){return"function"!=typeof e.indexOf||at(e)?_e(e,t,0):e.indexOf(t)})),Rr=Dt((function(t,e,r){t=t<r.length&&t>=0?t:r.length;var n=Array.prototype.slice.call(r,0);return n.splice(t,0,e),n})),Cr=function(){function t(t,e){this.xf=e,this.f=t,this.found=!1}return t.prototype["@@transducer/init"]=zt,t.prototype["@@transducer/result"]=function(t){return this.found||(t=this.xf["@@transducer/step"](t,void 0)),this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,e){return this.f(e)&&(this.found=!0,t=Ie(this.xf["@@transducer/step"](t,e))),t},t}();function Nr(t){return function(e){return new Cr(t,e)}}var Dr=ct(Ht(["find"],Nr,(function(t,e){for(var r=0,n=e.length;r<n;){if(t(e[r]))return e[r];r+=1}}))),Mr=Dr,Ur=Dt((function(t,e,r){return Ct(t,Be(e,r))})),qr=ct((function(t,e){if(0===t.length||ge(e))return!1;for(var r=e,n=0;n<t.length;){if(ge(r)||!vt(t[n],r))return!1;r=r[t[n]],n+=1}return!0})),Gr=ct((function(t,e){return qr([t],e)})),Br=ct((function(t,e){return pr(e,[t])})),Fr=ct((function(t,e){return null==e||e!=e?t:e})),Hr=Dt((function(t,e,r){return Fr(t,yt(e,r))}));function $r(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(void 0,"symbol"==typeof(o=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(n.key))?o:String(o)),n)}var o}function zr(t){return function(t){if(Array.isArray(t))return Kr(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||Jr(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Jr(t,e){if(t){if("string"==typeof t)return Kr(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Kr(t,e):void 0}}function Kr(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Yr(t){var e=Gr("url_base_pathname",t),r=Gr("requests_pathname_prefix",t);if("Object"!==Lt(t)||!e&&!r)throw new Error('\n            Trying to make an API request but neither\n            "url_base_pathname" nor "requests_pathname_prefix"\n            is in `config`. `config` is: ',t);var n=r?t.requests_pathname_prefix:t.url_base_pathname;return"/"===n.charAt(n.length-1)?n:n+"/"}var Wr=["props","children"],Vr=function t(e,r){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:[],o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0;if(Array.isArray(e))e.forEach((function(e,i){if(o){var a=wr((function(t){return er("{}",t)}),o);if(-1!==a){var u=kr(0,a,o),c=kr(a,o.length,o);if(u.length)t(yt(u,e),r,Le(n,Le([i],u)),c);else{var s,f=c.map((function(t){return t.replace("{}","")})).filter((function(t){return t})),l=Le([i],f);for(var p in s=f.length?yt(f,e):e){var h=s[p];t(h,r,Le(n,l.concat([p])))}}}else t(yt(o,e),r,Le(n,Le([i],o)))}else t(e,r,Br(i,n))}));else if("Object"===Lt(e)){r(e,n);var i=yt(Wr,e);if(i){var a=Le(n,Wr);t(i,r,a)}Hr([],[e.namespace,e.type],window.__dashprivate_childrenProps).forEach((function(o){if(o.includes("[]")){var i=function(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(t,e)||Jr(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(o.split("[]").map((function(t){return t.split(".").filter((function(t){return t}))})),2),a=i[0],u=i[1],c=Le(["props"],a),s=Le(n,c);t(yt(c,e),r,s,u)}else if(o.includes("{}")){for(var f=o.split("."),l=[],p=[],h=!1,d=0;d<f.length;d++){var y=f[d];!h&&y.includes("{}")?(h=!0,l.push(y.replace("{}",""))):h?p.push(y):l.push(y)}var v=Le(n,["props"].concat(l)),b=yt(["props"].concat(l),e);if(void 0!==b)for(var g in b){var m=b[g];p.length?t(yt(p,m),r,Le(v,[g].concat(p))):t(m,r,[].concat(zr(v),[g]))}}else{var w=Le(n,["props"].concat(zr(o.split("."))));t(yt(["props"].concat(zr(o.split("."))),e),r,w)}}))}},Zr=function(){function t(){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),this._ev={}}var e,r;return e=t,r=[{key:"on",value:function(t,e){var r=this;return(this._ev[t]=this._ev[t]||[]).push(e),function(){return r.removeListener(t,e)}}},{key:"removeListener",value:function(t,e){var r=this._ev[t];if(r){var n=r.indexOf(e);n>-1&&r.splice(n,1)}}},{key:"emit",value:function(t){for(var e=this,r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];var i=this._ev[t];i&&i.forEach((function(t){return t.apply(e,n)}))}},{key:"once",value:function(t,e){var r=this,n=this.on(t,(function(){n();for(var t=arguments.length,o=new Array(t),i=0;i<t;i++)o[i]=arguments[i];e.apply(r,o)}))}}],r&&$r(e.prototype,r),Object.defineProperty(e,"prototype",{writable:!1}),t}();function Qr(t,e,r,n){var o=r||{strs:{},objs:{}},i=o.strs,a=o.objs,u=function(t){return e.some((function(e,r){return t[r]!==e}))},c=e.length,s=c?Wt(u,i):{},f={};return c&&tr((function(t,e){var r=Wt((function(t){var e=t.path;return u(e)}),t);r.length&&(f[e]=r)}),a),Vr(t,(function(t,r){var n=yt(["props","id"],t);if(n)if("object"==typeof n){var o=Object.keys(n).sort(),i=Bt(o,n),u=o.join(","),c=f[u]=f[u]||[],l=a[u]||[],p={values:i,path:Le(e,r)},h=Ir(p,l);-1===h?c.push(p):f[u]=Rr(h,p,c)}else s[n]=Le(e,r)})),{strs:s,objs:f,events:n||r.events}}function Xr(t,e){if("object"==typeof e){var r=Object.keys(e).sort(),n=r.join(","),o=t.objs[n];if(!o)return!1;var i=Bt(r,e),a=Mr(Ur(i,"values"),o);return a&&a.path}return t.strs[e]}var tn={resolve:function(t){var e=t.type,r=t.namespace,n=window[r];if(n){if(n[e])return n[e];throw new Error("Component ".concat(e," not found in ").concat(r))}throw new Error("".concat(r," was not found."))}};function en(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function rn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?en(Object(r),!0).forEach((function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):en(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function nn(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(t,e)||on(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function on(t,e){if(t){if("string"==typeof t)return an(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?an(t,e):void 0}}function an(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var un=function(t){return t.startsWith("..")},cn={wild:"ALL",multi:1},sn={wild:"MATCH"},fn={wild:"ALLSMALLER",multi:1,expand:1},ln={ALL:cn,MATCH:sn,ALLSMALLER:fn},pn={Output:{ALL:cn,MATCH:sn},Input:ln,State:ln},hn=["string","number","boolean"],dn=[".","{"],yn=function(t){return t.startsWith("{")};function vn(t){var e=t.lastIndexOf(".");return{id:bn(t.substr(0,e)),property:t.substr(e+1)}}function bn(t){return yn(t)?function(t){return ae((function(t){return Array.isArray(t)&&ln[t[0]]||t}),JSON.parse(t))}(t):t}function gn(t){return"object"!=typeof t?t:"{"+Object.keys(t).sort().map((function(e){return JSON.stringify(e)+":"+((r=t[e])&&r.wild||JSON.stringify(r));var r})).join(",")+"}"}function mn(t,e){var r=Qe()(e);if(Qe()(t)){if(r){var n=Number(t),o=Number(e);return n>o?1:n<o?-1:0}return-1}if(r)return 1;var i="boolean"==typeof t;return i!==("boolean"==typeof e)?i?-1:1:t>e?1:t<e?-1:0}var wn=function(t){return Qe()(t)?t-1:0},On=function(t){return"string"==typeof t?t+"z":"z"};function _n(t,e,r,n){var o=t[e]=t[e]||{};(o[r]=o[r]||[]).push(n)}function En(t,e,r,n){for(var o=Object.keys(e).sort(),i=o.join(","),a=Bt(o,e),u=t[i]=t[i]||{},c=u[r]=u[r]||[],s=!1,f=0;f<c.length;f++)if(Ct(a,c[f].values)){s=c[f];break}s||(s={keys:o,values:a,callbacks:[]},c.push(s)),s.callbacks.push(n)}var jn=function(t){var e=nn(t,2),r=e[0],n=e[1],o=r&&r.wild,i=n&&n.wild;return o&&i?!(r===sn&&n===fn||r===fn&&n===sn):r===n||o||i};function Sn(t,e){var r,n=t.id,o=t.property,i=jt(n).sort(),a=Bt(i,n),u=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=on(t))){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){u=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw i}}}}(e);try{for(u.s();!(r=u.n()).done;){var c=r.value,s=c.id;if(c.property===o&&"string"!=typeof s&&Ct(jt(s).sort(),i)&&De(jn,rr(a,Bt(i,s))))return c}}catch(t){u.e(t)}finally{u.f()}return!1}function xn(t,e){var r=new Ve.f,n={},o=ae(lr({id:bn})),i=ae((function(t){var e,r=t.output,n=lr({inputs:o,state:o},t);return n.outputs=ae((function(t){return we("out",!0,vn(t))}),un(r)?(e=r).substr(2,e.length-4).split("..."):[r]),n}),t),a=!1;!function(t,e){var r={},n=[];t.forEach((function(t){var o=t.inputs,i=t.outputs,a=t.state,u=!0;1!==i.length||i[0].id||i[0].property||(u=!1,e("A callback is missing Outputs",["Please provide an output for this callback:",JSON.stringify(t,null,2)]));var c="In the callback for output(s):\n  "+i.map(Hn).join("\n  ");o.length||e("A callback is missing Inputs",[c,"there are no `Input` elements.","Without `Input` elements, it will never get called.","","Subscribing to `Input` components will cause the","callback to be called whenever their values change."]),[[i,"Output"],[o,"Input"],[a,"State"]].forEach((function(t){var r=nn(t,2),n=r[0],o=r[1];("Output"!==o||u)&&(Array.isArray(n)||e("Callback ".concat(o,"(s) must be an Array"),[c,"For ".concat(o,"(s) we found:"),JSON.stringify(n),"but we expected an Array."]),n.forEach((function(t,r){!function(t,e,r,n,o){var i=t.id,a=t.property;if("string"==typeof a&&a||o("Callback property error",[e,"".concat(r,"[").concat(n,"].property = ").concat(JSON.stringify(a)),"but we expected `property` to be a non-empty string."]),"object"==typeof i)Nt(i)&&o("Callback item missing ID",[e,"".concat(r,"[").concat(n,"].id = {}"),"Every item linked to a callback needs an ID"]),tr((function(t,i){i||o("Callback wildcard ID error",[e,"".concat(r,"[").concat(n,'].id has key "').concat(i,'"'),"Keys must be non-empty strings."]),"object"==typeof t&&t.wild?pn[r][t.wild]!==t&&o("Callback wildcard ID error",[e,"".concat(r,"[").concat(n,'].id["').concat(i,'"] = ').concat(t.wild),"Allowed wildcards for ".concat(r,"s are:"),jt(pn[r]).join(", ")]):er(typeof t,hn)||o("Callback wildcard ID error",[e,"".concat(r,"[").concat(n,'].id["').concat(i,'"] = ').concat(JSON.stringify(t)),"Wildcard callback ID values must be either wildcards","or constants of one of these types:",hn.join(", ")])}),i);else if("string"==typeof i){i||o("Callback item missing ID",[e,"".concat(r,"[").concat(n,'].id = "').concat(i,'"'),"Every item linked to a callback needs an ID"]);var u=dn.filter((function(t){return er(t,i)}));u.length&&o("Callback invalid ID string",[e,"".concat(r,"[").concat(n,"].id = '").concat(i,"'"),"characters '".concat(u.join("', '"),"' are not allowed.")])}else o("Callback ID type error",[e,"".concat(r,"[").concat(n,"].id = ").concat(JSON.stringify(i)),"IDs must be strings or wildcard-compatible objects."])}(t,c,o,r,e)})))})),function(t,e,r,n,o){var i={},a=[];t.forEach((function(t,u){var c=t.id,s=t.property;if("string"==typeof c){var f=Hn({id:c,property:s});i[f]?r("Duplicate callback Outputs",[e,"Output ".concat(u," (").concat(f,") is already used by this callback.")]):n[f]?r("Duplicate callback outputs",[e,"Output ".concat(u," (").concat(f,") is already in use."),"To resolve this, set `allow_duplicate=True` on","duplicate outputs, or combine the outputs into","one callback function, distinguishing the trigger","by using `dash.callback_context` if necessary."]):i[f]=1}else{var l={id:c,property:s},p=Sn(l,a),h=p||Sn(l,o);if(p||h){var d=Hn(l),y=Hn(p||h);r("Overlapping wildcard callback outputs",[e,"Output ".concat(u," (").concat(d,")"),"overlaps another output (".concat(y,")"),"used in ".concat(p?"this":"a different"," callback.")])}else a.push(l)}})),jt(i).forEach((function(t){n[t]=1})),a.forEach((function(t){o.push(t)}))}(i,c,e,r,n),function(t,e,r,n,o){var i=Pn(t[0].id).matchKeys;t.forEach((function(e,r){r&&!Ct(Pn(e.id).matchKeys,i)&&o("Mismatched `MATCH` wildcards across `Output`s",[n,"Output ".concat(r," (").concat(Hn(e),")"),"does not have MATCH wildcards on the same keys as","Output 0 (".concat(Hn(t[0]),")."),"MATCH wildcards must be on the same keys for all Outputs.","ALL wildcards need not match, only MATCH."])})),[[e,"Input"],[r,"State"]].forEach((function(e){var r=nn(e,2),a=r[0],u=r[1];a.forEach((function(e,r){var a=Pn(e.id),c=a.matchKeys,s=a.allsmallerKeys,f=c.concat(s),l=qe(f,i);l.length&&(l.sort(),o("`Input` / `State` wildcards not in `Output`s",[n,"".concat(u," ").concat(r," (").concat(Hn(e),")"),"has MATCH or ALLSMALLER on key(s) ".concat(l.join(", ")),"where Output 0 (".concat(Hn(t[0]),")"),"does not have a MATCH wildcard. Inputs and State do not","need every MATCH from the Output(s), but they cannot have","extras beyond the Output(s)."]))}))}))}(i,o,a,c,e)}))}(i,(function(t,r){a=!0,e(t,r)}));var u={},c={},s={},f={},l={MultiGraph:r,outputMap:u,inputMap:c,outputPatterns:s,inputPatterns:f,callbacks:i};if(a)return l;function p(t,e){var r=[{}];return tr((function(t,o){var i=n[o].vals,a=i.indexOf(e[o]),u=[t];t&&t.wild&&(u=t===fn?a>0?i.slice(0,a):[]:-1===a||t===cn?i:[e[o]]),r=vr(vr([we(o)],u),r)}),t),r}i.forEach((function(t){var e=t.outputs,r=t.inputs;e.concat(r).forEach((function(t){var e=t.id;"object"==typeof e&&tr((function(t,e){n[e]||(n[e]={exact:[],expand:0});var r=n[e];t&&t.wild?t.expand&&(r.expand+=1):-1===r.exact.indexOf(t)&&r.exact.push(t)}),e)}))})),tr((function(t){var e=t.exact,r=t.expand,n=e.slice().sort(mn);if(r)for(var o=0;o<r;o++)e.length?(n.splice(0,0,[wn(n[0])]),n.push(On(n[n.length-1]))):n.push(o);else e.length||n.push(0);t.vals=n}),n);var h="__output",d=[],y=[],v=[];function b(t,e){var n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];r.addNode(t),r.addDependency(t,e),n&&(y[y.length-1].push(t),v[v.length-1].push(e))}return i.forEach((function(t){var e=t.outputs,n=t.inputs;function o(t,e){r.addNode(e),n.forEach((function(r){var n=r.id,o=r.property;"object"==typeof n?p(n,t).forEach((function(t){b(Hn({id:t,property:o}),e)})):b(Hn(r),e)}))}y.push([]),v.push([]);var i=Pn(e[0].id).matchKeys,a=wr((function(t){return!Tn(t.id)}),e),l=_r({matchKeys:i,firstSingleOutput:a,outputs:e},t);e.forEach((function(t){var e=t.id,r=t.property,i=function(t,e){var r=t.id,n=t.property;return e.some((function(e){var o=e.id,i=e.property;if(n!==i||typeof r!=typeof o)return!1;if("string"==typeof r){if(r===o)return!0}else if(Sn(e,[t]))return!0;return!1}))}(t,n);if("object"==typeof e)p(e,{}).forEach((function(t){var e={id:t,property:r},n=Hn(e);i&&(d.push(e),n+=h),o(t,n)})),En(s,e,r,l);else{var a=Hn(t);i&&(d.push(t),a+=h),o({},a),_n(u,e,r,l)}})),n.forEach((function(t){var e=t.id,r=t.property;"object"==typeof e?En(f,e,r,l):_n(c,e,r,l)}))})),d.forEach((function(t){for(var e=Hn(t),r=e.concat(h),n=0;n<y.length;n++)y[n].some((function(t){return t===e}))&&(v[n].some((function(t){return t===r}))||v[n].forEach((function(t){b(r,t,!1)})))})),l}function Pn(t){var e=[],r=[];return"object"==typeof t&&(tr((function(t,n){t===sn?e.push(n):t===fn&&r.push(n)}),t),e.sort(),r.sort()),{matchKeys:e,allsmallerKeys:r}}function An(t,e,r,n,o,i){for(var a=0;a<t.length;a++){var u=e[a],c=r[a];if(c.wild){if(n&&c!==cn){var s=n.indexOf(t[a]),f=i[s];if(c===fn&&f===fn)throw new Error("invalid wildcard id pair: "+JSON.stringify({keys:t,patternVals:r,vals:e,refKeys:n,refPatternVals:i,refVals:o}));if(mn(u,o[s])!==(c===fn?-1:f===fn?1:0))return!1}}else if(u!==c)return!1}return!0}function kn(t,e){for(var r=[],n=0;n<t.length;n++)t[n]===sn&&r.push(e[n]);return r.length?JSON.stringify(r):""}function Tn(t){var e=t.id;return"object"==typeof e&&xr((function(t){return t.multi}),fr(e))}function Ln(t,e,r,n){var o,i,a="";if("string"==typeof r){var u=(t.outputMap[r]||{})[n];u&&(i=u[0],o=Qn())}else{var c=Object.keys(r).sort(),s=Bt(c,r),f=c.join(","),l=(t.outputPatterns[f]||{})[n];if(l)for(var p=0;p<l.length;p++){var h=l[p].values;if(An(c,s,h)){i=l[p].callbacks[0],o=Qn(c,s,h),a=kn(h,s);break}}}return!!o&&Vn(i,o,a)}function In(t,e,r,n){var o=Object.keys(e.id).sort(),i=Bt(o,e.id),a={};r.forEach((function(e){var r=e.id,u=Bt(o,r),c=Vn(t,Qn(o,u,i),kn(i,u)),s=c.resolvedId;a[s]||(n.push(c),a[s]=!0)}))}function Rn(t,e,r){return function(n){var o=n.matchKeys,i=n.firstSingleOutput,a=n.outputs;if(o.length){var u=a[i];if(u)In(n,u,t(e)(u),r);else{var c={};a.forEach((function(i){var a=t(e)(i).filter((function(t){var e=JSON.stringify(Bt(o,t.id));return!c[e]&&(c[e]=1,!0)}));In(n,i,a,r)}))}}else{var s=Vn(n,t,"");Qt(s.getOutputs(e)).length&&r.push(s)}}}function Cn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Nn(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Cn(Object(r),!0).forEach((function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Cn(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Dn(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(t,e)||Un(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Mn(t){return function(t){if(Array.isArray(t))return qn(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||Un(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Un(t,e){if(t){if("string"==typeof t)return qn(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?qn(t,e):void 0}}function qn(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Gn=2,Bn=1,Fn=Gt(Math.max),Hn=function(t){var e=t.id,r=t.property;return"".concat(gn(e),".").concat(r)};function $n(t,e,r,n,o){var i=!(arguments.length>5&&void 0!==arguments[5])||arguments[5],a=[],u=Hn({id:r,property:n});if("string"==typeof r){var c=(t.inputMap[r]||{})[n];if(!c)return[];c.forEach(Rn(Qn(),e,a))}else{var s=Object.keys(r).sort(),f=Bt(s,r),l=s.join(","),p=(t.inputPatterns[l]||{})[n];if(!p)return[];p.forEach((function(t){An(s,f,t.values)&&t.callbacks.forEach(Rn(Qn(s,f,t.values),e,a))}))}return a.forEach((function(r){r.changedPropIds[u]=o||Gn,i&&(r.priority=zn(t,e,r))})),a}function zn(t,e,r){for(var n=[r],o={},i={},a=[];n.length;){n=Wt((function(t){var e=i[t.resolvedId];return i[t.resolvedId]=!0,e}),n);var u=Wt((function(t){return!o[Hn(t)]}),Qt(ae((function(t){return Qt(t.getOutputs(e))}),n)));u.forEach((function(t){return o[Hn(t)]=!0})),(n=Qt(ae((function(r){var n=r.id,o=r.property;return $n(t,e,n,o,Bn,!1)}),u))).length&&a.push(n.length)}return a.unshift(a.length),ae((function(t){return Math.min(t,35).toString(36)}),a).join("")}var Jn=function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:e,n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};if(!e.length)return[];var o=ae(Hn,be((function(e,r){return Le(e,Qt(r.getOutputs(t)))}),[],r)),i={};if(o.forEach((function(t){return i[t]=!0})),Object.keys(n).length){var a=Qt(ae((function(e){return function(t,e,r){for(var n=[r],o={};n.length;){var i=Wt((function(t){return!o[Hn(t)]}),Qt(ae((function(t){return Qt(t.getOutputs(e))}),n)));o=be((function(t,e){return we(Hn(e),!0,t)}),o,i),n=Qt(ae((function(r){var n=r.id,o=r.property;return $n(t,e,n,o,Bn,!1)}),i))}return o}(n,t,e)}),r));a.length>0&&(i=Object.assign.apply(Object,[a[0]].concat(Mn(a))))}return Wt((function(e){return De((function(t){return!i[Hn(t)]}),qe(Qt(e.getInputs(t)),Qt(e.getOutputs(t))))}),e)},Kn=function(t,e,r,n){for(var o=[],i=function(t,e,r,n){var o=n.outputsOnly,i=n.removedArrayInputsOnly,a=n.newPaths,u=n.chunkPath,c={},s=[];function f(t){if(t){var e=c[t.resolvedId];if(void 0!==e){var r=s[e];r.changedPropIds=Fn(r.changedPropIds,t.changedPropIds),t.initialCall&&(r.initialCall=!0)}else c[t.resolvedId]=s.length,s.push(t)}}function l(r,n,c){if(n)for(var s in n){var l=Ln(t,0,r,s);l&&(l.callback.prevent_initial_call||(l.initialCall=!0,f(l)))}if(!o&&c){var p=i?(y=gn(r),function(t){return t.getInputs(e).some((function(e){return!(!Array.isArray(e)||!e.some((function(t){return gn(t.id)===y}))||(Qt(t.getOutputs(a)).length&&(t.initialCall=!0,t.changedPropIds={},f(t)),0))}))}):f,h=p;for(var d in u&&(h=function(t){De(Lr(u),Fe("path",Qt(t.getOutputs(e))))||p(t)}),c)$n(t,e,r,d,Bn).forEach(h)}var y}return Vr(r,(function(e){var r=yt(["props","id"],e);if(r)if("string"!=typeof r||i){var n=Object.keys(r).sort().join(",");l(r,!i&&t.outputPatterns[n],t.inputPatterns[n])}else l(r,t.outputMap[r],t.inputMap[r])})),ae((function(r){return rn(rn({},r),{},{priority:zn(t,e,r)})}),s)}(t,e,r,n);;){var a=Dn(Je((function(t){var r=t.callback.inputs,n=t.getInputs;return De(Tn,r)||!Nt(qe(ae(Hn,Qt(n(e))),o))}),i),2),u=a[0],c=a[1];if(!c.length)break;i=u,o=Le(o,ae(Hn,Qt(ae((function(t){return(0,t.getOutputs)(e)}),c))))}if(n.filterRoot){var s=yt(["props","id"],r);s&&(s=gn(s),i=i.filter((function(t){return t.callback.inputs.reduce((function(t,e){return t||gn(e.id)==s&&n.filterRoot.includes(e.property)}),!1)})))}var f=Math.random().toString(16);return ae((function(t){return Nn(Nn({},t),{},{executionGroup:f})}),i)},Yn=function(t){var e=t.anyVals,r=t.callback,n=r.inputs,o=r.outputs,i=r.state;return Le(ae(Hn,[].concat(Mn(n),Mn(o),Mn(i))),Array.isArray(e)?e:""===e?[]:[e]).join(",")};function Wn(t,e,r,n){return Qt(ae((function(e){return $n(r,n,t,e)}),jt(e)))}var Vn=function(t,e,r){return{callback:t,anyVals:r,resolvedId:t.output+r,getOutputs:function(r){return t.outputs.map(e(r))},getInputs:function(r){return t.inputs.map(e(r))},getState:function(r){return t.state.map(e(r))},changedPropIds:{},initialCall:!1}};function Zn(t,e){var r=Dn(Je((function(t){var r=t.getOutputs,n=t.callback.outputs;return Qt(r(e)).length===n.length}),t),2)[1],n=Dn(Je((function(t){var r=t.getOutputs;return!Qt(r(e)).length}),r),2)[1];return{added:ae((function(t){return we("changedPropIds",Ke((function(t,r){return Xr(e,vn(r).id)}),t.changedPropIds),t)}),n),removed:r}}function Qn(t,e,r){return function(n){return function(o){var i=o.id,a=o.property;if("string"==typeof i){var u=Xr(n,i);return u?[{id:i,property:a,path:u}]:[]}var c=Object.keys(i).sort(),s=Bt(c,i),f=c.join(","),l=n.objs[f];if(!l)return[];var p=[];return l.forEach((function(n){var o=n.values,i=n.path;An(c,o,s,t,e,r)&&p.push({id:We(c,o),property:a,path:i})})),p}}}var Xn={ON_PROP_CHANGE:1,SET_REQUEST_QUEUE:1,SET_GRAPHS:1,SET_PATHS:1,SET_LAYOUT:1,SET_APP_LIFECYCLE:1,SET_CONFIG:1,ADD_HTTP_HEADERS:1,ON_ERROR:1,SET_HOOKS:1},to=function(t){if(Xn[t])return t;throw new Error("".concat(t," is not defined."))},eo=1;function ro(t){var e={STARTED:"STARTED",HYDRATED:"HYDRATED",DESTROYED:"DESTROYED"};if(e[t])return e[t];throw new Error("".concat(t," is not a valid app state."))}var no,oo,io=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:ro("STARTED"),e=arguments.length>1?arguments[1]:void 0;return e.type===to("SET_APP_LIFECYCLE")?ro(e.payload):t};function ao(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function uo(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ao(Object(r),!0).forEach((function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ao(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}!function(t){t.AddBlocked="Callbacks.AddBlocked",t.AddExecuted="Callbacks.AddExecuted",t.AddExecuting="Callbacks.AddExecuting",t.AddPrioritized="Callbacks.AddPrioritized",t.AddRequested="Callbacks.AddRequested",t.AddStored="Callbacks.AddStored",t.AddWatched="Callbacks.AddWatched",t.RemoveBlocked="Callbacks.RemoveBlocked",t.RemoveExecuted="Callbacks.RemoveExecuted",t.RemoveExecuting="Callbacks.RemoveExecuting",t.RemovePrioritized="Callbacks.RemovePrioritized",t.RemoveRequested="Callbacks.RemoveRequested",t.RemoveStored="Callbacks.RemoveStored",t.RemoveWatched="Callbacks.RemoveWatched"}(no||(no={})),function(t){t.AddCompleted="Callbacks.Completed",t.Aggregate="Callbacks.Aggregate"}(oo||(oo={}));var co={blocked:[],executed:[],executing:[],prioritized:[],requested:[],stored:[],watched:[],completed:0},so={[no.AddBlocked]:Le,[no.AddExecuted]:Le,[no.AddExecuting]:Le,[no.AddPrioritized]:Le,[no.AddRequested]:Le,[no.AddStored]:Le,[no.AddWatched]:Le,[no.RemoveBlocked]:qe,[no.RemoveExecuted]:qe,[no.RemoveExecuting]:qe,[no.RemovePrioritized]:qe,[no.RemoveRequested]:qe,[no.RemoveStored]:qe,[no.RemoveWatched]:qe},fo={[no.AddBlocked]:"blocked",[no.AddExecuted]:"executed",[no.AddExecuting]:"executing",[no.AddPrioritized]:"prioritized",[no.AddRequested]:"requested",[no.AddStored]:"stored",[no.AddWatched]:"watched",[no.RemoveBlocked]:"blocked",[no.RemoveExecuted]:"executed",[no.RemoveExecuting]:"executing",[no.RemovePrioritized]:"prioritized",[no.RemoveRequested]:"requested",[no.RemoveStored]:"stored",[no.RemoveWatched]:"watched"},lo=function(){var t=arguments.length>1?arguments[1]:void 0;return be((function(t,e){return null===e?t:e.type===oo.AddCompleted?function(t,e){return uo(uo({},t),{},{completed:t.completed+e.payload})}(t,e):function(t,e){var r=so[e.type],n=fo[e.type];return r&&n&&0!==e.payload.length?uo(uo({},t),{},{[n]:r(t[n],e.payload)}):t}(t,e)}),arguments.length>0&&void 0!==arguments[0]?arguments[0]:co,t.type===oo.Aggregate?t.payload:[t])},po=Dt((function t(e,r,n){return Ut((function(r,n,o){return St(n)&&St(o)?t(e,n,o):e(r,n,o)}),r,n)})),ho=po,yo=ct((function(t,e){return ho((function(t,e,r){return r}),t,e)}));function vo(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,e=arguments.length>1?arguments[1]:void 0;return e.type===to("SET_CONFIG")?(window.__dashprivate_childrenProps=yo(window.__dashprivate_childrenProps||{},e.payload.children_props),e.payload):e.type===to("ADD_HTTP_HEADERS")?yo(t,{fetch:{headers:e.payload}}):t}var bo={},go=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:bo,e=arguments.length>1?arguments[1]:void 0;return"SET_GRAPHS"===e.type?e.payload:t};function mo(t){return function(t){if(Array.isArray(t))return wo(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return wo(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?wo(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function wo(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Oo={frontEnd:[],backEnd:[],backEndConnected:!0};function _o(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Oo,e=arguments.length>1?arguments[1]:void 0;switch(e.type){case"ON_ERROR":var r=t.frontEnd,n=t.backEnd,o=t.backEndConnected;return console.error(e.payload.error),"frontEnd"===e.payload.type?{frontEnd:[_r(e.payload,{timestamp:new Date})].concat(mo(r)),backEnd:n,backEndConnected:o}:"backEnd"===e.payload.type?{frontEnd:r,backEnd:[_r(e.payload,{timestamp:new Date})].concat(mo(n)),backEndConnected:o}:t;case"SET_CONNECTION_STATUS":return _r(t,{backEndConnected:e.payload});default:return t}}function Eo(t){return function(t){if(Array.isArray(t))return jo(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return jo(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?jo(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function jo(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var So={past:[],present:{},future:[]},xo=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:So;switch((arguments.length>1?arguments[1]:void 0).type){case"UNDO":var e=t.past,r=t.present,n=t.future,o=e[e.length-1];return{past:e.slice(0,e.length-1),present:o,future:[r].concat(Eo(n))};case"REDO":var i=t.past,a=t.present,u=t.future,c=u[0],s=u.slice(1);return{past:[].concat(Eo(i),[a]),present:c,future:s};case"REVERT":var f=t.past,l=t.future,p=f[f.length-1];return{past:f.slice(0,f.length-1),present:p,future:Eo(l)};default:return t}},Po=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{layout_pre:null,layout_post:null,request_pre:null,request_post:null,callback_resolved:null,request_refresh_jwt:null,bear:!1},e=arguments.length>1?arguments[1]:void 0;return"SET_HOOKS"===e.type?e.payload:t};function Ao(t,e,r){if(r||(r=new ko),o=typeof(n=t),null==n||"object"!=o&&"function"!=o)return t;var n,o,i,a=function(n){var o=r.get(t);if(o)return o;for(var i in r.set(t,n),t)Object.prototype.hasOwnProperty.call(t,i)&&(n[i]=e?Ao(t[i],!0,r):t[i]);return n};switch(Lt(t)){case"Object":return a(Object.create(Object.getPrototypeOf(t)));case"Array":return a([]);case"Date":return new Date(t.valueOf());case"RegExp":return i=t,new RegExp(i.source,i.flags?i.flags:(i.global?"g":"")+(i.ignoreCase?"i":"")+(i.multiline?"m":"")+(i.sticky?"y":"")+(i.unicode?"u":"")+(i.dotAll?"s":""));case"Int8Array":case"Uint8Array":case"Uint8ClampedArray":case"Int16Array":case"Uint16Array":case"Int32Array":case"Uint32Array":case"Float32Array":case"Float64Array":case"BigInt64Array":case"BigUint64Array":return t.slice();default:return t}}var ko=function(){function t(){this.map={},this.length=0}return t.prototype.set=function(t,e){var r=this.hash(t),n=this.map[r];n||(this.map[r]=n=[]),n.push([t,e]),this.length+=1},t.prototype.hash=function(t){var e=[];for(var r in t)e.push(Object.prototype.toString.call(t[r]));return e.join()},t.prototype.get=function(t){if(this.length<=180)for(var e in this.map)for(var r=this.map[e],n=0;n<r.length;n+=1){var o=r[n];if(o[0]===t)return o[1]}else{var i=this.hash(t),a=this.map[i];if(a)for(var u=0;u<a.length;u+=1){var c=a[u];if(c[0]===t)return c[1]}}},t}(),To=H((function(t){return null!=t&&"function"==typeof t.clone?t.clone():Ao(t,!0)})),Lo="JWT Expired",Io={OK:200,PREVENT_UPDATE:204,BAD_REQUEST:400,UNAUTHORIZED:401,CLIENTSIDE_ERROR:"CLIENTSIDE_ERROR",NO_RESPONSE:"NO_RESPONSE"},Ro={[Io.OK]:"SUCCESS",[Io.PREVENT_UPDATE]:"NO_UPDATE"},Co=["__dash_client","__dash_server","__dash_upload","__dash_download"];var No,Do={count:0,total:0,compute:0,network:{time:0,upload:0,download:0},resources:{},status:{latest:null},result:{}},Mo={updated:[],resources:{},callbacks:{},graphLayout:null},Uo=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Mo,e=arguments.length>1?arguments[1]:void 0;if("UPDATE_RESOURCE_USAGE"===e.type){var r=e.payload,n=r.id,o=r.usage,i=r.status,a=Ro[i]||i,u={updated:[n],resources:t.resources,callbacks:t.callbacks,graphLayout:t.graphLayout};u.callbacks[n]=u.callbacks[n]||To(Do);var c=u.callbacks[n],s=c.resources,f=u.resources;if(c.count+=1,c.status.latest=a,c.status[a]=(c.status[a]||0)+1,c.result=e.payload.result,c.inputs=e.payload.inputs,c.state=e.payload.state,o){var l=o.__dash_client,p=o.__dash_server,h=o.__dash_upload,d=o.__dash_download,y=function(t,e){if(null==t)return{};var r,n,o=function(t,e){if(null==t)return{};var r,n,o={},i=Object.keys(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||(o[r]=t[r]);return o}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(n=0;n<i.length;n++)r=i[n],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(o[r]=t[r])}return o}(o,Co);for(var v in c.total+=l,c.compute+=p,c.network.time+=l-p,c.network.upload+=h,c.network.download+=d,y)y.hasOwnProperty(v)&&(s[v]=(s[v]||0)+y[v],f[v]=(f[v]||0)+y[v])}return u}return t},qo={id:null,props:{}},Go=function(){return arguments.length>0&&void 0!==arguments[0]?arguments[0]:qo};!function(t){t.Set="IsLoading.Set"}(No||(No={}));var Bo,Fo=function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0],e=arguments.length>1?arguments[1]:void 0;return e.type===No.Set?e.payload:t},Ho=function(t){return{value:t,"fantasy-land/map":function(){return this}}},$o=ct((function(t,e){return t(Ho)(e).value})),zo=ct((function(t,e){return function(r){return function(n){return ae((function(t){return e(t,n)}),r(t(n)))}}})),Jo=H((function(t){return zo(yt(t),me(t))})),Ko=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;if(e.type===to("SET_LAYOUT"))return e.payload;if(er(e.type,["UNDO_PROP_CHANGE","REDO_PROP_CHANGE",to("ON_PROP_CHANGE")])){var r=Br("props",e.payload.itempath),n=$o(Jo(r),t),o=_r(n,e.payload.props);return me(r,o,t)}return t};!function(t){t.Set="LoadingMap.Set"}(Bo||(Bo={}));var Yo={},Wo=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Yo,e=arguments.length>1?arguments[1]:void 0;return e.type===Bo.Set?e.payload:t},Vo={strs:{},objs:{}},Zo=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Vo,e=arguments.length>1?arguments[1]:void 0;return e.type===to("SET_PATHS")?e.payload:t},Qo=Dt((function(t,e,r){var n=Array.prototype.slice.call(r,0);return n.splice(t,e),n})),Xo=ct((function t(e,r){if(null==r)return r;switch(e.length){case 0:return r;case 1:return function(t,e){if(null==e)return e;if(lt(t)&&at(e))return Qo(t,1,e);var r={};for(var n in e)r[n]=e[n];return delete r[t],r}(e[0],r);default:var n=e[0],o=Array.prototype.slice.call(e,1);return null==r[n]?function(t,e){if(lt(t)&&at(e))return[].concat(e);var r={};for(var n in e)r[n]=e[n];return r}(n,r):we(n,t(o,r[n]),r)}})),ti=ct((function(t,e){return Xo([t],e)})),ei=function(t,e){return we(t.jobId,t,e)},ri=function(t,e){return ti(t,e)},ni=function(t,e){return me([t,"outdated"],!0,e)};function oi(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0;switch(e.type){case"ADD_CALLBACK_JOB":return ei(e.payload,t);case"REMOVE_CALLBACK_JOB":return ri(e.payload.jobId,t);case"CALLBACK_JOB_OUTDATED":return ni(e.payload.jobId,t);default:return t}}function ii(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var ai=["dependenciesRequest","layoutRequest","reloadRequest","loginRequest"];function ui(){var t={appLifecycle:io,callbacks:lo,config:vo,error:_o,graphs:go,history:xo,hooks:Po,profile:Uo,changed:Go,isLoading:Fo,layout:Ko,loadingMap:Wo,paths:Zo};return ft((function(e){var r;t[e]=(r=e,function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=arguments.length>1?arguments[1]:void 0,n=t;if(e.type===r){var o=e.payload,i=o.id,a={status:o.status,content:o.content};n=Array.isArray(i)?me(i,a,t):i?we(i,a,t):_r(t,a)}return n})}),ai),t.callbackJobs=oi,function(t){for(var e=Object.keys(t),r={},n=0;n<e.length;n++){var o=e[n];"function"==typeof t[o]&&(r[o]=t[o])}var i,a=Object.keys(r);try{!function(t){Object.keys(t).forEach((function(e){var r=t[e];if(void 0===r(void 0,{type:X.INIT}))throw new Error(V(12));if(void 0===r(void 0,{type:X.PROBE_UNKNOWN_ACTION()}))throw new Error(V(13))}))}(r)}catch(t){i=t}return function(t,e){if(void 0===t&&(t={}),i)throw i;for(var n=!1,o={},u=0;u<a.length;u++){var c=a[u],s=r[c],f=t[c],l=s(f,e);if(void 0===l)throw e&&e.type,new Error(V(14));o[c]=l,n=n||l!==f}return(n=n||a.length!==Object.keys(t).length)?o:t}}(t)}function ci(t,e,r){var n,o=e.graphs,i=e.paths,a=e.layout,u=t.itempath,c=t.props,s=yt(u.concat(["props"]),a)||{},f=s.id;return f&&(r&&(e.changed={id:f,props:c}),n={id:f,props:{}},jt(c).forEach((function(t){$n(o,i,f,t).length&&(n.props[t]=s[t])}))),n}function si(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,pi(n.key),n)}}function fi(t,e,r){return e&&si(t.prototype,e),r&&si(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function li(t,e,r){return(e=pi(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function pi(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}var hi=fi((function t(e){var r=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),li(this,"_store",void 0),li(this,"_unsubscribe",void 0),li(this,"_observers",[]),li(this,"observe",(function(t,e){if("function"==typeof t){if(!Array.isArray(e))throw new Error("inputs must be an array");return r.add(t,e),function(){return r.remove(t)}}return r.add(t.observer,t.inputs),function(){return r.remove(t.observer)}})),li(this,"setStore",(function(t){r.__finalize__(),r.__init__(t)})),li(this,"__finalize__",(function(){var t;return null===(t=r._unsubscribe)||void 0===t?void 0:t.call(r)})),li(this,"__init__",(function(t){r._store=t,t&&(r._unsubscribe=t.subscribe(r.notify)),r._observers.forEach((function(t){t.lastState=null}))})),li(this,"add",(function(t,e){return r._observers.push({inputPaths:ae((function(t){return t.split(".")}),e),lastState:null,observer:t,triggered:!1})})),li(this,"notify",(function(){var t=r._store;if(t){var e=t.getState(),n=Wt((function(t){return!t.triggered&&xr((function(r){return yt(r,e)!==yt(r,t.lastState)}),t.inputPaths)}),r._observers);n.forEach((function(t){t.triggered=!0})),n.forEach((function(e){e.lastState=t.getState(),e.observer(t),e.triggered=!1}))}})),li(this,"remove",(function(t){return r._observers.splice(r._observers.findIndex((function(e){return t===e.observer}),r._observers),1)})),this.__init__(e)})),di=function(t){var e=t(),r=e.config,n=e.isLoading,o=null==r?void 0:r.update_title;o&&(n?document.title!==o&&(yi.title=document.title,document.title=o):document.title===o?document.title=yi.title:yi.title=document.title)},yi={inputs:["isLoading"],mutationObserver:void 0,observer:function(t){var e=t.getState,r=e().config;if(yi.config!==r){var n;yi.config=r,null===(n=yi.mutationObserver)||void 0===n||n.disconnect(),yi.mutationObserver=new MutationObserver((function(){return di(e)}));var o=document.querySelector("title");o&&yi.mutationObserver.observe(o,{subtree:!0,childList:!0,attributes:!0,characterData:!0})}di(e)}},vi=yi,bi=H((function(t){var e=[];for(var r in t)vt(r,t)&&(e[e.length]=[r,t[r]]);return e})),gi=ct((function(t,e){for(var r={},n=0;n<t.length;)t[n]in e&&(r[t[n]]=e[t[n]]),n+=1;return r})),mi=r(143),wi=r.n(mi),Oi=function(t){return"function"==typeof t},_i=function(t){return t},Ei=function(t){return null===t};function ji(t,e,r){void 0===e&&(e=_i),wi()(Oi(e)||Ei(e),"Expected payloadCreator to be a function, undefined or null");var n=Ei(e)||e===_i?_i:function(t){for(var r=arguments.length,n=new Array(r>1?r-1:0),o=1;o<r;o++)n[o-1]=arguments[o];return t instanceof Error?t:e.apply(void 0,[t].concat(n))},o=Oi(r),i=t.toString(),a=function(){var e=n.apply(void 0,arguments),i={type:t};return e instanceof Error&&(i.error=!0),void 0!==e&&(i.payload=e),o&&(i.meta=r.apply(void 0,arguments)),i};return a.toString=function(){return i},a}var Si=r(489);function xi(){xi=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:x(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="suspendedYield",d="executing",y="completed",v={};function b(){}function g(){}function m(){}var w={};s(w,a,(function(){return this}));var O=Object.getPrototypeOf,_=O&&O(O(L([])));_&&_!==r&&n.call(_,a)&&(w=_);var E=m.prototype=b.prototype=Object.create(w);function j(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(o,i,a,u){var c=l(t[o],t,i);if("throw"!==c.type){var s=c.arg,f=s.value;return f&&"object"==typeof f&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function x(e,r,n){var o=p;return function(i,a){if(o===d)throw new Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=P(u,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=d;var s=l(e,r,n);if("normal"===s.type){if(o=n.done?y:h,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=y,n.method="throw",n.arg=s.arg)}}}function P(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=l(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function L(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return g.prototype=m,o(E,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=s(m,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,s(t,c,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},j(S.prototype),s(S.prototype,u,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new S(f(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),s(E,c,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=L,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return u.type="throw",u.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),k(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;k(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:L(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function Pi(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}var Ai=ji(to("ON_ERROR")),ki=ji(to("SET_APP_LIFECYCLE")),Ti=ji(to("SET_CONFIG")),Li=ji(to("ADD_HTTP_HEADERS")),Ii=ji(to("SET_GRAPHS")),Ri=ji(to("SET_HOOKS")),Ci=ji(to("SET_LAYOUT")),Ni=ji(to("SET_PATHS")),Di=(ji(to("SET_REQUEST_QUEUE")),ji(to("ON_PROP_CHANGE"))),Mi=function(t){return function(e,r){return t(Ai({type:"backEnd",error:{message:e,html:r.join("\n")}}))}};var Ui=z(console.warn);function qi(){try{return{"X-CSRFToken":Si.parse(document.cookie)._csrf_token}}catch(t){return Ui(t),{}}}var Gi=Hi("REDO"),Bi=Hi("UNDO"),Fi=Hi("REVERT");function Hi(t){return function(e,r){var n=r(),o=n.history,i=n.paths;e(ji(t)());var a=("REDO"===t?o.future[0]:o.past[o.past.length-1])||{},u=a.id,c=a.props;u&&(e(ji("UNDO_PROP_CHANGE")({itempath:Xr(i,u),props:c})),e($i({id:u,props:c})))}}function $i(t){var e=t.id,r=t.props;return function(){var t=function(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){Pi(i,n,o,a,u,"next",t)}function u(t){Pi(i,n,o,a,u,"throw",t)}a(void 0)}))}}(xi().mark((function t(n,o){var i,a,u;return xi().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:i=o(),a=i.graphs,u=i.paths,n(_a(Wn(e,r,a,u)));case 2:case"end":return t.stop()}}),t)})));return function(e,r){return t.apply(this,arguments)}}()}function zi(t,e,r){if(t&&"function"==typeof t.text)t.text().then((function(t){r(Ai({type:"backEnd",error:{message:e,html:t}}))}));else{var n=t instanceof Error?t:{message:e,html:t};r(Ai({type:"backEnd",error:n}))}}var Ji=ct((function(t,e){return e instanceof t||null!=e&&(e.constructor===t||"Object"===t.name&&"object"==typeof e)})),Ki=ct((function(t,e){return pr([t],e)})),Yi=H((function(t){return pt(t)?t.split("").reverse().join(""):Array.prototype.slice.call(t,0).reverse()}));function Wi(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Vi(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Wi(Object(r),!0).forEach((function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Wi(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Zi(t,e){return Ji(Number,t)&&t<0?e.length+t:t}function Qi(t,e){for(var r=[],n=0;n<t.length;n++){var o=Zi(t[n],yt(r,e));r.push(o)}return r}var Xi={Assign:function(t,e){var r=e.params,n=e.location;return me(n,r.value,t)},Merge:function(t,e){var r=yt(e.location,t);return me(e.location,Vi(Vi({},r),e.params.value),t)},Extend:function(t,e){var r=yt(e.location,t);return me(e.location,Le(r,e.params.value),t)},Delete:function(t,e){return Xo(e.location,t)},Insert:function(t,e){var r=yt(e.location,t);return me(e.location,Rr(Zi(e.params.index,r),e.params.value,r),t)},Append:function(t,e){var r=yt(e.location,t);return me(e.location,Br(e.params.value,r),t)},Prepend:function(t,e){var r=yt(e.location,t);return me(e.location,Ki(e.params.value,r),t)},Add:function(t,e){var r=yt(e.location,t);return me(e.location,r+e.params.value,t)},Sub:function(t,e){var r=yt(e.location,t);return me(e.location,r-e.params.value,t)},Mul:function(t,e){var r=yt(e.location,t);return me(e.location,r*e.params.value,t)},Div:function(t,e){var r=yt(e.location,t);return me(e.location,r/e.params.value,t)},Clear:function(t,e){var r=yt(e.location,t);return me(e.location,Pt(r),t)},Reverse:function(t,e){var r=yt(e.location,t);return me(e.location,Yi(r),t)},Remove:function(t,e){var r=yt(e.location,t);return me(e.location,r.filter((function(t){return!Ct(t,e.params.value)})),t)}};function ta(t,e){for(var r=t,n=0;n<e.operations.length;n++){var o=e.operations[n];o.location=Qi(o.location,r);var i=Xi[o.operation];if(!i)throw new Error("Invalid Operation ".concat(o.operation));r=i(r,o)}return r}function ea(){ea=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:x(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="suspendedYield",d="executing",y="completed",v={};function b(){}function g(){}function m(){}var w={};s(w,a,(function(){return this}));var O=Object.getPrototypeOf,_=O&&O(O(L([])));_&&_!==r&&n.call(_,a)&&(w=_);var E=m.prototype=b.prototype=Object.create(w);function j(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(o,i,a,u){var c=l(t[o],t,i);if("throw"!==c.type){var s=c.arg,f=s.value;return f&&"object"==typeof f&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function x(e,r,n){var o=p;return function(i,a){if(o===d)throw new Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=P(u,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=d;var s=l(e,r,n);if("normal"===s.type){if(o=n.done?y:h,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=y,n.method="throw",n.arg=s.arg)}}}function P(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=l(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function L(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return g.prototype=m,o(E,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=s(m,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,s(t,c,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},j(S.prototype),s(S.prototype,u,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new S(f(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),s(E,c,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=L,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return u.type="throw",u.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),k(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;k(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:L(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function ra(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}var na=z(console.warn),oa={GET:function(t,e){return fetch(t,yo(e,{method:"GET",headers:qi()}))},POST:function(t,e){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return fetch(t,yo(e,{method:"POST",headers:qi(),body:r?JSON.stringify(r):null}))}};function ia(t,e,r,n,o){return function(){var i=function(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){ra(i,n,o,a,u,"next",t)}function u(t){ra(i,n,o,a,u,"throw",t)}a(void 0)}))}}(ea().mark((function i(a,u){var c,s,f,l,p,h,d,y,v,b,g;return ea().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:h=function(t){u().error.backEndConnected!==t&&a({type:"SET_CONNECTION_STATUS",payload:t})},c=u(),s=c.config,f=c.hooks,l=null,p="".concat(Yr(s)).concat(t),a({type:r,payload:{id:n,status:"loading"}}),i.prev=5,y=0;case 7:if(!(y<=eo)){i.next=36;break}return i.prev=8,i.next=11,oa[e](p,s.fetch,o);case 11:d=i.sent,i.next=19;break;case 14:return i.prev=14,i.t0=i.catch(8),console.log("fetch error",d),h(!1),i.abrupt("return");case 19:if(d.status!==Io.UNAUTHORIZED&&d.status!==Io.BAD_REQUEST){i.next=32;break}if(!f.request_refresh_jwt){i.next=32;break}return i.next=23,d.text();case 23:if(!i.sent.includes(Lo)){i.next=32;break}return i.next=27,f.request_refresh_jwt(s.fetch.headers.Authorization.substr(7));case 27:if(!(v=i.sent)){i.next=32;break}return l={Authorization:"Bearer ".concat(v)},s=yo(s,{fetch:{headers:l}}),i.abrupt("continue",33);case 32:return i.abrupt("break",36);case 33:y++,i.next=7;break;case 36:if(b=d.headers.get("content-type"),l&&a(Li(l)),h(!0),!b||-1===b.indexOf("application/json")){i.next=41;break}return i.abrupt("return",d.json().then((function(t){return a({type:r,payload:{status:d.status,content:t,id:n}}),t})));case 41:return na("Response is missing header: content-type: application/json"),i.abrupt("return",a({type:r,payload:{id:n,status:d.status}}));case 45:i.prev=45,i.t1=i.catch(5),g="Error from API call: "+t,zi(i.t1,g,a);case 49:case"end":return i.stop()}}),i,null,[[5,45],[8,14]])})));return function(t,e){return i.apply(this,arguments)}}()}function aa(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ua(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?aa(Object(r),!0).forEach((function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):aa(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function ca(){ca=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:x(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="suspendedYield",d="executing",y="completed",v={};function b(){}function g(){}function m(){}var w={};s(w,a,(function(){return this}));var O=Object.getPrototypeOf,_=O&&O(O(L([])));_&&_!==r&&n.call(_,a)&&(w=_);var E=m.prototype=b.prototype=Object.create(w);function j(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(o,i,a,u){var c=l(t[o],t,i);if("throw"!==c.type){var s=c.arg,f=s.value;return f&&"object"==typeof f&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function x(e,r,n){var o=p;return function(i,a){if(o===d)throw new Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=P(u,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=d;var s=l(e,r,n);if("normal"===s.type){if(o=n.done?y:h,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=y,n.method="throw",n.arg=s.arg)}}}function P(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=l(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function L(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return g.prototype=m,o(E,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=s(m,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,s(t,c,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},j(S.prototype),s(S.prototype,u,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new S(f(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),s(E,c,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=L,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return u.type="throw",u.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),k(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;k(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:L(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function sa(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function fa(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?sa(Object(r),!0).forEach((function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):sa(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function la(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function pa(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){la(i,n,o,a,u,"next",t)}function u(t){la(i,n,o,a,u,"throw",t)}a(void 0)}))}}function ha(t){return function(t){if(Array.isArray(t))return va(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||ya(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function da(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(t,e)||ya(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ya(t,e){if(t){if("string"==typeof t)return va(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?va(t,e):void 0}}function va(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var ba=ji(no.AddBlocked),ga=ji(oo.AddCompleted),ma=ji(no.AddExecuted),wa=ji(no.AddExecuting),Oa=ji(no.AddPrioritized),_a=ji(no.AddRequested),Ea=ji(no.AddStored),ja=ji(no.AddWatched),Sa=ji(no.RemoveExecuted),xa=ji(no.RemoveBlocked),Pa=ji(no.RemoveExecuting),Aa=ji(no.RemovePrioritized),ka=ji(no.RemoveRequested),Ta=ji(no.RemoveStored),La=ji(no.RemoveWatched),Ia=ji(oo.Aggregate),Ra=ji("UPDATE_RESOURCE_USAGE"),Ca=ji("ADD_CALLBACK_JOB"),Na=ji("REMOVE_CALLBACK_JOB"),Da=ji("CALLBACK_JOB_OUTDATED");function Ma(t,e,r,n,o){var i="";if(Tn(r))return[e,i];if(1!==e.length)if(e.length)i="Multiple objects were found for an `"+o+"` of a callback that only takes one value. The id spec is "+JSON.stringify(r.id)+(n?" with MATCH values "+n:"")+" and the property is `"+r.property+"`. The objects we found are: "+JSON.stringify(ae(gi(["id","property"]),e));else{var a="string"==typeof r.id;i="A nonexistent object was used in an `"+o+"` of a Dash callback. The id of this object is "+(a?"`"+r.id+"`":JSON.stringify(r.id)+(n?" with MATCH values "+n:""))+" and the property is `"+r.property+(a?"`. The string ids in the current layout are: ["+jt(t.strs).join(", ")+"]":"`. The wildcard ids currently available are logged above.")}return[e[0],i]}function Ua(t,e,r,n,o){var i=arguments.length>5&&void 0!==arguments[5]&&arguments[5],a="Input"===o?r.getInputs:r.getState,u=[],c=0,s=a(t).map((function(i,a){var s=da(Ma(t,i.map((function(t){var r=t.id,n=t.property,o=t.path;return{id:r,property:n,value:yt([].concat(ha(o),["props",n]),e)}})),n[a],r.anyVals,o),2),f=s[0],l=s[1];return Tn(n[a])&&!f.length&&c++,l&&u.push(l),f}));if(u.length){if(i&&u.length+c===s.length)return null;qa(u,t)}return s}function qa(t,e){var r=t[0];throw-1!==r.indexOf("logged above")&&console.error(e.objs),new ReferenceError(r)}var Ga=function(t){return Array.isArray(t)?Fe("value",t):t.value},Ba=function(t,e){return Array.isArray(t)?rr(t,e):[[t,e]]};function Fa(t){return t.split("@")[0]}function Ha(t,e,r,n){return $a.apply(this,arguments)}function $a(){return $a=pa(ca().mark((function t(e,r,n,o){var i,a,u,c,s,f,l,p,h,d,y,v,b,g,m,w,O;return ca().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if((i=window.dash_clientside=window.dash_clientside||{}).no_update||(Object.defineProperty(i,"no_update",{value:{description:"Return to prevent updating an Output."},writable:!1}),Object.defineProperty(i,"PreventUpdate",{value:{description:"Throw to prevent updating all Outputs."},writable:!1})),a=o.inputs,u=o.outputs,c=o.state,s=Date.now(),f=Ka(a),l=Ka(c),p={},h=Io.OK,t.prev=8,v=r.namespace,b=r.function_name,g=a.map(Ga),c&&(g=Le(g,c.map(Ga))),i.callback_context={},i.callback_context.triggered=o.changedPropIds.map((function(t){return{prop_id:t,value:f[t]}})),i.callback_context.inputs_list=a,i.callback_context.inputs=f,i.callback_context.states_list=c,i.callback_context.states=l,m=(d=i[v])[b].apply(d,ha(g)),delete i.callback_context,"function"!=typeof(null===(y=m)||void 0===y?void 0:y.then)){t.next=24;break}return t.next=23,m;case 23:m=t.sent;case 24:Ba(u,m).forEach((function(t){var e=da(t,2),r=e[0],n=e[1];Ba(r,n).forEach((function(t){var e=da(t,2),r=e[0],n=e[1],o=r.id,a=r.property,u=gn(o),c=p[u]=p[u]||{};n!==i.no_update&&(c[Fa(a)]=n)}))})),t.next=35;break;case 27:if(t.prev=27,t.t0=t.catch(8),t.t0!==i.PreventUpdate){t.next=33;break}h=Io.PREVENT_UPDATE,t.next=35;break;case 33:throw h=Io.CLIENTSIDE_ERROR,t.t0;case 35:return t.prev=35,delete i.callback_context,w=Date.now()-s,O={__dash_server:w,__dash_client:w,__dash_upload:0,__dash_download:0},n.ui&&e(Ra({id:o.output,usage:O,status:h,result:p,inputs:a,state:c})),t.finish(35);case 41:return t.abrupt("return",p);case 42:case"end":return t.stop()}}),t,null,[[8,27,35,41]])}))),$a.apply(this,arguments)}function za(t,e,r){bi(t).forEach((function(t){var n=da(t,2),o=n[0],i=n[1],a=da(o.split("."),2),u=a[0],c=a[1],s=r.strs[u];e(Di({props:{[c]:i},itempath:s})),e($i({id:u,props:{[c]:i}}))}))}function Ja(t,e,r,n,o,i,a,u,c){e.request_pre&&e.request_pre(n);var s,f,l,p,h=Date.now(),d=JSON.stringify(n),y=a;return new Promise((function(a,v){var b=function(y){var b=y.status;if(f){var g=u().callbackJobs[f];if(null!=g&&g.outdated)return t(Na({jobId:f})),a({})}function w(e){if(r.ui){var o={__dash_server:0,__dash_client:Date.now()-h,__dash_upload:d.length,__dash_download:Number(y.headers.get("Content-Length"))};(y.headers.get("Server-Timing")||"").split(",").forEach((function(t){var e=t.split(";")[0],r=t.match(/;dur=[0-9.]+/);r&&(o[e]=Number(r[0].slice(5)))})),t(Ra({id:n.output,usage:o,status:b,result:e,inputs:n.inputs,state:n.state}))}}var O=function(){f&&t(Na({jobId:f})),l&&za(l,t,o),p&&za(p,t,o)};b===Io.OK?y.json().then((function(r){if(!s&&r.cacheKey&&(s=r.cacheKey),!f&&r.job){var u={jobId:r.job,cacheKey:r.cacheKey,cancelInputs:r.cancel,progressDefault:r.progressDefault,output:c};t(Ca(u)),f=r.job}r.progress&&za(r.progress,t,o),r.running&&za(r.running,t,o),!l&&r.runningOff&&(l=r.runningOff),!p&&r.progressDefault&&(p=r.progressDefault),i&&void 0===r.response?setTimeout(m,void 0!==i.interval?i.interval:500):(O(),function(t){var r,o=t.multi,i=t.response;if(e.request_post&&e.request_post(n,i),o)r=i;else{var u=n.output;r={[u.substr(0,u.lastIndexOf("."))]:i.props}}w(r),a(r)}(r))})):b===Io.PREVENT_UPDATE?(O(),w({}),a({})):(O(),v(y))},g=function(){r.ui&&t(Ra({id:n.output,status:Io.NO_RESPONSE,result:{},inputs:n.inputs,state:n.state})),v(new Error("Callback failed: the server did not respond."))},m=function(){var t,e,n;(t=qi(),e="".concat(Yr(r),"_dash-update-component"),n=function(t,r){var n="?";e.includes("?")&&(n="&"),e="".concat(e).concat(n).concat(t,"=").concat(r)},s&&n("cacheKey",s),f&&n("job",f),y&&(y.forEach((function(t){var e=da(t,2),r=e[0],o=e[1];return n(r,o)})),y=y.filter((function(t){var e=da(t,3);return e[0],e[1],!e[2]}))),fetch(e,yo(r.fetch,{method:"POST",headers:t,body:d}))).then(b,g)};m()}))}function Ka(t){if(!t)return{};for(var e={},r=0;r<t.length;r++){var n;if(Array.isArray(t[r]))for(var o=t[r],i=0;i<o.length;i++){var a;e["".concat(gn(o[i].id),".").concat(o[i].property)]=null!==(a=o[i].value)&&void 0!==a?a:null}else e["".concat(gn(t[r].id),".").concat(t[r].property)]=null!==(n=t[r].value)&&void 0!==n?n:null}return e}function Ya(t,e,r,n,i,a,u,c){var s=a.allOutputs,f=t.callback,l=f.output,p=f.inputs,h=f.state,d=f.clientside_function,y=f.long,v=f.dynamic_creator;try{var b=Ua(n,i,t,p,"Input",!0);if(null===b)return fa(fa({},t),{},{executionPromise:null});var g=[],m=[];if(s.forEach((function(e,r){var o=da(Ma(n,ae(gi(["id","property"]),e),t.callback.outputs[r],t.anyVals,"Output"),2),i=o[0],a=o[1];g.push(i),a&&m.push(a)})),m.length)return Qt(b).length&&qa(m,n),fa(fa({},t),{},{executionPromise:null});var w=function(){var a=pa(ca().mark((function a(){var s,f,p,m,w,O,_,E,j;return ca().wrap((function(a){for(;;)switch(a.prev=a.next){case 0:if(a.prev=0,s={output:l,outputs:un(l)?g:g[0],inputs:b,changedPropIds:jt(t.changedPropIds),state:t.callback.state.length?Ua(n,i,t,h,"State"):void 0},!d){a.next=13;break}return a.prev=3,a.next=6,Ha(u,d,e,s);case 6:return f=a.sent,a.abrupt("return",{data:f,payload:s});case 10:return a.prev=10,a.t0=a.catch(3),a.abrupt("return",{error:a.t0,payload:s});case 13:p=e,m=null,O=[],fr(c().callbackJobs).forEach((function(e){t.callback.output===e.output&&(O.push(["oldJob",e.jobId,!0]),u(Da({jobId:e.jobId}))),e.cancelInputs&&sr(e.cancelInputs,t.callback.inputs).length&&(O.push(["cancelJob",e.jobId]),e.progressDefault&&za(e.progressDefault,u,n))})),_=ca().mark((function i(){var a,f,l,h;return ca().wrap((function(i){for(;;)switch(i.prev=i.next){case 0:return i.prev=0,i.next=3,Ja(u,r,p,s,n,y,O.length?O:void 0,c,t.callback.output);case 3:return a=i.sent,m&&u(Li(m)),f=c().layout,Qt(g).forEach((function(t){var e=Fa(t.property),r=Xr(n,t.id),o=yt(r.concat(["props",e]),f),i=[gn(t.id),e],u=yt(i,a);if(Gr("__dash_patch_update",u)){if(void 0===o)throw new Error("Cannot patch undefined");a=me(i,ta(o,u),a)}})),v&&setTimeout((function(){return u((function(t,e){(0,o.unstable_batchedUpdates)((function(){var r=e().graphs;t(Ii(ua(ua({},r),{},{reset:!0}))),t(ia("_dash-dependencies","GET","dependenciesRequest"))}))}))}),0),i.abrupt("return",{v:{data:a,payload:s}});case 11:if(i.prev=11,i.t0=i.catch(0),w=i.t0,!(j<=eo)||i.t0.status!==Io.UNAUTHORIZED&&i.t0.status!==Io.BAD_REQUEST){i.next=29;break}return i.next=17,i.t0.text();case 17:if(!i.sent.includes(Lo)){i.next=29;break}if(null===r.request_refresh_jwt){i.next=29;break}return l=null,e.fetch.headers.Authorization&&(l=e.fetch.headers.Authorization.substr(7)),i.next=24,r.request_refresh_jwt(l);case 24:if(!(h=i.sent)){i.next=29;break}return m={Authorization:"Bearer ".concat(h)},p=yo(e,{fetch:{headers:m}}),i.abrupt("return",0);case 29:return i.abrupt("return",1);case 30:case"end":return i.stop()}}),i,null,[[0,11]])})),j=0;case 19:if(!(j<=eo)){a.next=31;break}return a.delegateYield(_(),"t1",21);case 21:if(0!==(E=a.t1)){a.next=24;break}return a.abrupt("continue",28);case 24:if(1!==E){a.next=26;break}return a.abrupt("break",31);case 26:if(!E){a.next=28;break}return a.abrupt("return",E.v);case 28:j++,a.next=19;break;case 31:return a.abrupt("return",{error:w,payload:null});case 34:return a.prev=34,a.t2=a.catch(0),a.abrupt("return",{error:a.t2,payload:null});case 37:case"end":return a.stop()}}),a,null,[[0,34],[3,10]])})));return function(){return a.apply(this,arguments)}}();return fa(fa({},t),{},{executionPromise:w()})}catch(e){return fa(fa({},t),{},{executionPromise:{error:e,payload:null}})}}var Wa=H((function(t){return function(){return t}})),Va=function t(e){return{value:e,map:function(r){return t(r(e))}}},Za=Dt((function(t,e,r){return t((function(t){return Va(e(t))}))(r).value})),Qa=Dt((function(t,e,r){return Za(t,Wa(e),r)}));function Xa(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return tu(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?tu(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tu(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function eu(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function ru(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(void 0,"symbol"==typeof(o=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(n.key))?o:String(o)),n)}var o}function nu(t,e,r){return e&&ru(t.prototype,e),r&&ru(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}var ou="_dash_persistence.";function iu(t){var e="string"==typeof t?new Error(t):t;return ji("ON_ERROR")({type:"frontEnd",error:e})}function au(t,e){var r=t+e,n=r.length;return function(e){return e===t||e.substr(0,n)===r}}var uu=function(t){return"U"===t?void 0:JSON.parse(t||null)},cu=function(t){return void 0===t?"U":JSON.stringify(t)},su=function(){function t(e){eu(this,t),this._name=e,this._storage=window[e]}return nu(t,[{key:"hasItem",value:function(t){return null!==this._storage.getItem(ou+t)}},{key:"getItem",value:function(t){return uu(this._storage.getItem(ou+t))}},{key:"_setItem",value:function(t,e){this._storage.setItem(ou+t,cu(e))}},{key:"setItem",value:function(t,e,r){try{this._setItem(t,e)}catch(e){r(iu("".concat(t," failed to save in ").concat(this._name,". Persisted props may be lost.")))}}},{key:"removeItem",value:function(t){this._storage.removeItem(ou+t)}},{key:"clear",value:function(t){for(var e=this,r=au(ou+(t||""),t?".":""),n=[],o=0;o<this._storage.length;o++){var i=this._storage.key(o);r(i)&&n.push(i)}ft((function(t){return e._storage.removeItem(t)}),n)}}]),t}(),fu=16,lu={memory:new(function(){function t(){eu(this,t),this._data={}}return nu(t,[{key:"hasItem",value:function(t){return t in this._data}},{key:"getItem",value:function(t){return uu(this._data[t])}},{key:"setItem",value:function(t,e){this._data[t]=cu(e)}},{key:"removeItem",value:function(t){delete this._data[t]}},{key:"clear",value:function(t){var e=this;t?ft((function(t){return delete e._data[t]}),Wt(au(t,"."),jt(this._data))):this._data={}}}]),t}())},pu={local:"localStorage",session:"sessionStorage"};function hu(t,e){return lu[t]||(lu[t]=function(t,e){var r=new su(t),n=lu.memory,o=function(){for(var t="Spam",e=2;e<fu;e++)t+=t;return t}(),i=ou+"x.x";try{return r._setItem(i,o),r.getItem(i)!==o?(e(iu("".concat(t," init failed set/get, falling back to memory"))),n):(r.removeItem(i),r)}catch(r){e(iu("".concat(t," init first try failed; clearing and retrying")))}try{if(r.clear(),r._setItem(i,o),r.getItem(i)!==o)throw new Error("nope");return r.removeItem(i),e(iu("".concat(t," init set/get succeeded after clearing!"))),r}catch(r){return e(iu("".concat(t," init still failed, falling back to memory"))),n}}(pu[t],e)),lu[t]}var du={extract:function(t){return t},apply:function(t,e){return t}},yu=function(t,e,r){return t.persistenceTransforms&&t.persistenceTransforms[e]?r?t.persistenceTransforms[e][r]:t.persistenceTransforms[e]:du},vu=function(t,e,r){return"".concat(gn(t),".").concat(e,".").concat(JSON.stringify(r))},bu=function(t){var e=t.props,r=t.type,n=t.namespace;if(!r||!n)return{props:e};var o=e.id,i=e.persistence,a=tn.resolve(t),u=function(t){return e[t]||(a.defaultProps||{})[t]},c=u("persisted_props"),s=u("persistence_type");return{canPersist:o&&c&&s,id:o,props:e,element:a,persistence:i,persisted_props:c,persistence_type:s}};function gu(t,e){return"Object"===Lt(t)&&t.props?wu(t,t,[],e):t}function mu(t,e,r,n,o,i,a){if(e.hasItem(t)){var u=Xa(e.getItem(t),2),c=u[0],s=u[1],f=a?c:s,l=a?s:c,p=Xa(o.split("."),2),h=p[0],d=p[1],y=yu(r,h,d);Ct(f,y.extract(n[h]))?i[h]=y.apply(l,h in i?i[h]:n[h]):e.removeItem(t)}}function wu(t,e,r,n){var o=bu(e),i=o.canPersist,a=o.id,u=o.props,c=o.element,s=o.persistence,f=o.persisted_props,l=o.persistence_type,p=t;if(i&&s){var h=hu(l,n),d={};for(var y in ft((function(t){return mu(vu(a,t,s),h,c,u,t,d)}),f),d)p=Qa(Jo(r.concat("props",y)),d[y],p)}var v=u.children;return Array.isArray(v)?v.forEach((function(t,e){"Object"===Lt(t)&&t.props&&(p=wu(p,t,r.concat("props","children",e),n))})):"Object"===Lt(v)&&v.props&&(p=wu(p,v,r.concat("props","children"),n)),p}function Ou(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function _u(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Ou(Object(r),!0).forEach((function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Ou(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Eu(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return ju(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ju(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ju(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Su={observer:function(t){var e=t.dispatch,r=t.getState,n=r().callbacks.executed;var o=[],i=[];n.forEach((function(t){var n,a=Le(null!==(n=t.predecessors)&&void 0!==n?n:[],[t.callback]),u=t.callback,c=u.clientside_function,s=u.output,f=t.executionResult;if(!ge(f)){var l=f.data,p=f.error,h=f.payload;if(void 0!==l&&(Object.entries(l).forEach((function(t){var n=Eu(t,2),i=n[0],u=n[1],c=bn(i),s=r(),f=s.graphs,l=s.layout,p=s.paths,h=function(t,n){var o=r(),i=o.layout,a=Xr(o.paths,t);if(!a)return!1;n=function(t,e,r){var n=bu(t),o=n.canPersist,i=n.id,a=n.props,u=n.persistence,c=n.persisted_props,s=n.persistence_type,f=n.element,l=function(t,r){return t in e?e[t]:r},p=l("persistence",u);if(!o||!u&&!p)return e;var h=l("persistence_type",s),d=l("persisted_props",c),y=p!==u||h!==s||d!==c,v=function(t){return!(t.split(".")[0]in e)},b={},g=a;if(y&&u){var m=hu(s,r);ft((function(t){return mu(vu(i,t,u),m,f,a,t,b,!0)}),Wt(v,c)),g=_r(a,b)}if(p){var w=hu(h,r);y&&ft((function(t){return mu(vu(i,t,p),w,f,g,t,b)}),Wt(v,d));var O=f.persistenceTransforms||{};for(var _ in e){var E=O[_];if(E)for(var j in E)w.removeItem(vu(i,"".concat(_,".").concat(j),p));else w.removeItem(vu(i,_,p))}}return y?_r(e,b):e}(yt(a,i),n,e);var u=gu({props:n},e).props;return e(Di({itempath:a,props:u,source:"response"})),u}(c,u);o=Le(o,Qt(ae((function(t){return $n(f,p,c,t,!0)}),jt(u))).map((function(t){return _u(_u({},t),{},{predecessors:a})})));var d=Xr(p,c);if(d){var y=yt(d,l),v=Hr("defaultValue",[y.namespace,y.type],window.__dashprivate_childrenProps),b=function(t,n,i){var u=arguments.length>3&&void 0!==arguments[3]&&arguments[3],c=Qr(t,i,r().paths);e(Ni(c)),o=Le(o,Kn(f,c,t,{chunkPath:i,filterRoot:u}).map((function(t){return _u(_u({},t),{},{predecessors:a})}))),o=Le(o,Kn(f,p,n,{removedArrayInputsOnly:!0,newPaths:c,chunkPath:i,filterRoot:u}).map((function(t){return _u(_u({},t),{},{predecessors:a})})))},g=!1;["children"].concat(v).forEach((function(t){if(!g)if(t.includes("[]")){var e=Eu(t.split("[]").map((function(t){return t.split(".").filter((function(t){return t}))})),1)[0];if(!yt(e,h))return;b(_u(_u({},y),{},{props:_u(_u({},y.props),h)}),y,d,jt(h)),g=!0}else{var r=t.split("."),n=yt(r,h);if(!n)return;var o=Le(Xr(p,c),["props"].concat(r)),i=yt(o,l);b(n,i,o)}}));var m=Ke((function(t,e){return!(e in u)}),h);if(!Nt(m)){var w=r(),O=w.graphs,_=w.paths;o=Le(o,Wn(i,m,O,_).map((function(t){return _u(_u({},t),{},{predecessors:a})})))}}})),i.push(_u(_u({},t),{},{executionMeta:{allProps:ae(Hn,Qt(t.getOutputs(r().paths))),updatedProps:Qt(ae((function(t){var e=Eu(t,2),r=e[0],n=e[1];return ae((function(t){return Hn({id:r,property:t})}),jt(n))}),bi(l)))}}))),void 0!==p){var d=h?ae(Hn,Qt([h.outputs])).join(", "):s,y="Callback error updating ".concat(d);if(c){var v=c.namespace,b=c.function_name;y+=" via clientside function ".concat(v,".").concat(b)}zi(p,y,e),i.push(_u(_u({},t),{},{executionMeta:{allProps:ae(Hn,Qt(t.getOutputs(r().paths))),updatedProps:[]}}))}}})),e(Ia([n.length?Sa(n):null,n.length?ga(n.length):null,i.length?Ea(i):null,o.length?_a(o):null]))},inputs:["callbacks.executed"]},xu=Su;function Pu(){Pu=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:x(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="suspendedYield",d="executing",y="completed",v={};function b(){}function g(){}function m(){}var w={};s(w,a,(function(){return this}));var O=Object.getPrototypeOf,_=O&&O(O(L([])));_&&_!==r&&n.call(_,a)&&(w=_);var E=m.prototype=b.prototype=Object.create(w);function j(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(o,i,a,u){var c=l(t[o],t,i);if("throw"!==c.type){var s=c.arg,f=s.value;return f&&"object"==typeof f&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function x(e,r,n){var o=p;return function(i,a){if(o===d)throw new Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=P(u,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=d;var s=l(e,r,n);if("normal"===s.type){if(o=n.done?y:h,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=y,n.method="throw",n.arg=s.arg)}}}function P(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=l(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function L(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return g.prototype=m,o(E,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=s(m,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,s(t,c,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},j(S.prototype),s(S.prototype,u,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new S(f(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),s(E,c,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=L,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return u.type="throw",u.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),k(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;k(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:L(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function Au(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function ku(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Au(Object(r),!0).forEach((function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Au(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Tu(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function Lu(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Iu={observer:function(t){var e,r,n=t.dispatch,o=t.getState,i=o().callbacks.executing,a=(e=Je((function(t){return t.executionPromise instanceof Promise}),i),r=2,function(t){if(Array.isArray(t))return t}(e)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(e,r)||function(t,e){if(t){if("string"==typeof t)return Lu(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Lu(t,e):void 0}}(e,r)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),u=a[0],c=a[1];n(Ia([i.length?Pa(i):null,u.length?ja(u):null,c.length?ma(c.map((function(t){return we("executionResult",t.executionPromise,t)}))):null])),u.forEach(function(){var t=function(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){Tu(i,n,o,a,u,"next",t)}function u(t){Tu(i,n,o,a,u,"throw",t)}a(void 0)}))}}(Pu().mark((function t(e){var r,i,a,u,c,s;return Pu().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.executionPromise;case 2:if(r=t.sent,i=o(),a=i.callbacks.watched,u=i.appLifecycle,c=i.hooks.callback_resolved,u===ro("HYDRATED")){t.next=6;break}return t.abrupt("return");case 6:if(c&&c(e.callback,r),s=Mr((function(t){return t===e||t.executionPromise===e.executionPromise}),a)){t.next=10;break}return t.abrupt("return");case 10:n(Ia([La([s]),ma([ku(ku({},s),{},{executionResult:r})])]));case 11:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},inputs:["callbacks.executing"]},Ru=Iu,Cu=ct((function(t,e){for(var r={},n={},o=0,i=t.length;o<i;)n[t[o]]=1,o+=1;for(var a in e)n.hasOwnProperty(a)||(r[a]=e[a]);return r}));function Nu(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Du=function(t){var e,r;return(e=Array()).concat.apply(e,function(t){if(Array.isArray(t))return Nu(t)}(r=fr(Cu(["stored","completed"],t)))||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(r)||function(t,e){if(t){if("string"==typeof t)return Nu(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Nu(t,e):void 0}}(r)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())},Mu=ji(No.Set),Uu={observer:function(t){var e=t.dispatch,r=(0,t.getState)(),n=r.callbacks,o=r.isLoading,i=Du(n),a=Boolean(i.length);o!==a&&e(Mu(a))},inputs:["callbacks"]},qu=Uu,Gu=ji(Bo.Set);function Bu(t){return function(t){if(Array.isArray(t))return Fu(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return Fu(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Fu(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Fu(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var Hu={observer:function(t){var e=t.dispatch,r=(0,t.getState)(),n=r.callbacks,o=n.executing,i=n.watched,a=n.executed,u=r.loadingMap,c=r.paths,s=Qt(ae((function(t){return t.getOutputs(c)}),[].concat(Bu(o),Bu(i),Bu(a)))),f=Nt(s)?null:be((function(t,e){var r=e.id,n=e.property,o=e.path,i=t,a={id:r,property:n};return i.__dashprivate__idprops__=i.__dashprivate__idprops__||[],i.__dashprivate__idprops__.push(a),o.forEach((function(t,e){var r;(i=i[t]=null!==(r=i[t])&&void 0!==r?r:"children"===t&&"number"==typeof o[e+1]?[]:{}).__dashprivate__idprops__=i.__dashprivate__idprops__||[],i.__dashprivate__idprops__.push(a)})),i.__dashprivate__idprop__=i.__dashprivate__idprop__||a,t}),{},s);Ct(f,u)||e(Gu(f))},inputs:["callbacks.executing","callbacks.watched","callbacks.executed"]},$u=Hu,zu=ct((function(t,e){return Array.prototype.slice.call(e,0).sort(t)})),Ju=r(800),Ku=function(t,e,r){if(!r.length)return!0;var n=[],o=e.events,i=new Promise((function(t){o.once("rendered",t)}));return r.forEach((function(r){var o=Xr(e,r);if(o){var a=yt(o,t);if(a){var u=tn.resolve(a),c=(0,Ju.isReady)(u);c&&"function"==typeof c.then&&n.push(Promise.race([c,i.then((function(){return document.getElementById(gn(r))&&c}))]))}}})),!n.length||Promise.all(n)};function Yu(){Yu=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:x(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="suspendedYield",d="executing",y="completed",v={};function b(){}function g(){}function m(){}var w={};s(w,a,(function(){return this}));var O=Object.getPrototypeOf,_=O&&O(O(L([])));_&&_!==r&&n.call(_,a)&&(w=_);var E=m.prototype=b.prototype=Object.create(w);function j(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(o,i,a,u){var c=l(t[o],t,i);if("throw"!==c.type){var s=c.arg,f=s.value;return f&&"object"==typeof f&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function x(e,r,n){var o=p;return function(i,a){if(o===d)throw new Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=P(u,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=d;var s=l(e,r,n);if("normal"===s.type){if(o=n.done?y:h,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=y,n.method="throw",n.arg=s.arg)}}}function P(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=l(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function L(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return g.prototype=m,o(E,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=s(m,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,s(t,c,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},j(S.prototype),s(S.prototype,u,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new S(f(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),s(E,c,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=L,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return u.type="throw",u.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),k(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;k(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:L(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function Wu(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Vu(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Wu(Object(r),!0).forEach((function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Wu(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Zu(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function Qu(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){Zu(i,n,o,a,u,"next",t)}function u(t){Zu(i,n,o,a,u,"throw",t)}a(void 0)}))}}function Xu(t){return function(t){if(Array.isArray(t))return ec(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||tc(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function tc(t,e){if(t){if("string"==typeof t)return ec(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ec(t,e):void 0}}function ec(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var rc=function(t,e){var r,n;return(null!==(r=t.priority)&&void 0!==r?r:"")>(null!==(n=e.priority)&&void 0!==n?n:"")?-1:1},nc=function(t,e){var r=(0,t.getOutputs)(e),n=Qt(r),o=[],i={};return n.forEach((function(t){var e=t.id,r=t.property,n=gn(e);(i[n]=i[n]||[]).push(r),o.push(Hn({id:n,property:r}))})),{allOutputs:r,allPropIds:o}},oc=function(t,e){return cr(Fe("id",[].concat(Xu(Qt(t.getInputs(e))),Xu(Qt(t.getState(e))))))},ic={observer:function(){var t=Qu(Yu().mark((function t(e){var r,n,o,i,a,u,c,s,f,l,p,h,d,y,v,b,g,m,w,O,_;return Yu().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r=e.dispatch,n=e.getState,o=n(),i=o.callbacks,a=i.executing,u=i.watched,c=o.config,s=o.hooks,f=o.layout,l=o.paths,p=o.appLifecycle,h=n(),d=h.callbacks.prioritized,p===ro("HYDRATED")){t.next=5;break}return t.abrupt("return");case 5:y=Math.max(0,12-a.length-u.length),d=zu(rc,d),v=Je((function(t){return!0===Ku(f,l,oc(t,l))}),d),j=2,b=function(t){if(Array.isArray(t))return t}(E=v)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(E,j)||tc(E,j)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}(),g=b[0],m=b[1],w=g.slice(0,y),O=m.slice(0,y-w.length),w.length&&r(Ia([Aa(w),wa(ae((function(t){return Ya(t,c,s,l,f,nc(t,l),r,n)}),w))])),O.length&&(_=ae((function(t){return Vu(Vu(Vu({},t),nc(t,l)),{},{isReady:Ku(f,l,oc(t,l))})}),O),r(Ia([Aa(O),ba(_)])),_.forEach(function(){var t=Qu(Yu().mark((function t(e){var o,i,a;return Yu().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.isReady;case 2:if(o=n(),i=o.callbacks.blocked,Mr((function(t){return t===e||t.isReady===e.isReady}),i)){t.next=6;break}return t.abrupt("return");case 6:a=Ya(e,c,s,l,f,e,r,n),r(Ia([xa([e]),wa([a])]));case 8:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()));case 12:case"end":return t.stop()}var E,j}),t)})));return function(e){return t.apply(this,arguments)}}(),inputs:["callbacks.prioritized","callbacks.completed"]},ac=ic,uc=function(){function t(t,e,r,n){this.valueFn=t,this.valueAcc=e,this.keyFn=r,this.xf=n,this.inputs={}}return t.prototype["@@transducer/init"]=zt,t.prototype["@@transducer/result"]=function(t){var e;for(e in this.inputs)if(vt(e,this.inputs)&&(t=this.xf["@@transducer/step"](t,this.inputs[e]))["@@transducer/reduced"]){t=t["@@transducer/value"];break}return this.inputs=null,this.xf["@@transducer/result"](t)},t.prototype["@@transducer/step"]=function(t,e){var r=this.keyFn(e);return this.inputs[r]=this.inputs[r]||[r,Ao(this.valueAcc,!1)],this.inputs[r][1]=this.valueFn(this.inputs[r][1],e),t},t}();function cc(t,e,r){return function(n){return new uc(t,e,r,n)}}var sc=ct(ut("groupBy",re(4,[],Ht([],cc,(function(t,e,r,n){var o=ve((function(n,o){var i=r(o),a=t(vt(i,n)?n[i]:Ao(e,!1),o);return a&&a["@@transducer/reduced"]?Ie(n):(n[i]=a,n)}));return de(o,{},n)})))((function(t,e){return t.push(e),t}),[]))),fc=ct((function(t,e){return Or({},e,t)}));function lc(){lc=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:x(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="suspendedYield",d="executing",y="completed",v={};function b(){}function g(){}function m(){}var w={};s(w,a,(function(){return this}));var O=Object.getPrototypeOf,_=O&&O(O(L([])));_&&_!==r&&n.call(_,a)&&(w=_);var E=m.prototype=b.prototype=Object.create(w);function j(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(o,i,a,u){var c=l(t[o],t,i);if("throw"!==c.type){var s=c.arg,f=s.value;return f&&"object"==typeof f&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function x(e,r,n){var o=p;return function(i,a){if(o===d)throw new Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=P(u,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=d;var s=l(e,r,n);if("normal"===s.type){if(o=n.done?y:h,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=y,n.method="throw",n.arg=s.arg)}}}function P(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=l(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function L(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return g.prototype=m,o(E,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=s(m,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,s(t,c,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},j(S.prototype),s(S.prototype,u,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new S(f(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),s(E,c,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=L,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return u.type="throw",u.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),k(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;k(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:L(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function pc(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}var hc=function(){var t=function(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){pc(i,n,o,a,u,"next",t)}function u(t){pc(i,n,o,a,u,"throw",t)}a(void 0)}))}}(lc().mark((function t(e){var r,n;return lc().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return n=new Promise((function(t){return r=t})),setTimeout(r,e),t.abrupt("return",n);case 3:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}();function dc(){dc=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:x(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="suspendedYield",d="executing",y="completed",v={};function b(){}function g(){}function m(){}var w={};s(w,a,(function(){return this}));var O=Object.getPrototypeOf,_=O&&O(O(L([])));_&&_!==r&&n.call(_,a)&&(w=_);var E=m.prototype=b.prototype=Object.create(w);function j(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(o,i,a,u){var c=l(t[o],t,i);if("throw"!==c.type){var s=c.arg,f=s.value;return f&&"object"==typeof f&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function x(e,r,n){var o=p;return function(i,a){if(o===d)throw new Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=P(u,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=d;var s=l(e,r,n);if("normal"===s.type){if(o=n.done?y:h,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=y,n.method="throw",n.arg=s.arg)}}}function P(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=l(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function L(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return g.prototype=m,o(E,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=s(m,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,s(t,c,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},j(S.prototype),s(S.prototype,u,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new S(f(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),s(E,c,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=L,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return u.type="throw",u.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),k(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;k(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:L(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function yc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function vc(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?yc(Object(r),!0).forEach((function(e){var n,o,i;n=t,o=e,i=r[e],(o=function(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):yc(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function bc(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}var gc={observer:function(){var t=function(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){bc(i,n,o,a,u,"next",t)}function u(t){bc(i,n,o,a,u,"throw",t)}a(void 0)}))}}(dc().mark((function t(e){var r,n,o,i,a,u,c,s,f,l,p,h,d,y,v,b,g,m,w,O,_,E,j,S,x,P,A,k,T,L,I,R,C,N,D,M,U,q,G,B,F,H,$,z,J,K,Y;return dc().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return r=e.dispatch,n=e.getState,t.next=3,hc(0);case 3:if(o=n(),i=o.callbacks,a=o.callbacks,u=a.prioritized,c=a.blocked,s=a.executing,f=a.watched,l=a.stored,p=o.paths,h=o.graphs,d=n(),y=d.callbacks.requested,v=y.slice(0),b=Du(i),g=Wt((function(t){var e;return er(t.callback,null!==(e=t.predecessors)&&void 0!==e?e:[])}),y),y=qe(y,g),m=[],w=[],fr(sc(Yn,y)).forEach((function(t){if(1===t.length)w.push(t[0]);else{var e=t.find((function(t){return t.initialCall}));e&&m.push(e);var r=t.filter((function(t){return t!==e}));1===r.length?w.push(r[0]):(m=Le(m,r),w.push(fc({changedPropIds:be(Gt(Math.max),{},Fe("changedPropIds",r)),executionGroup:Wt((function(t){return Boolean(t)}),Fe("executionGroup",r)).slice(-1)[0]},r.slice(-1)[0])))}})),O=Qt(ae((function(t){return t.slice(0,-1)}),fr(sc(Yn,Le(u,y=w))))),_=Qt(ae((function(t){return t.slice(0,-1)}),fr(sc(Yn,Le(c,y))))),E=Qt(ae((function(t){return t.slice(0,-1)}),fr(sc(Yn,Le(s,y))))),j=Qt(ae((function(t){return t.slice(0,-1)}),fr(sc(Yn,Le(f,y))))),S=Zn(y,p),x=S.added,P=S.removed,A=Zn(u,p),k=A.added,T=A.removed,L=Zn(c,p),I=L.added,R=L.removed,C=Zn(s,p),N=C.added,D=C.removed,M=Zn(f,p),U=M.added,q=M.removed,y=Le(qe(y,P),x),G=Jn(p,y,b,h),B=[],F=[],G.length||!y.length||y.length!==b.length){t.next=33;break}H=y.slice(0),$=dc().mark((function t(){var e,r,n;return dc().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e=H[0],G.push(e),H=H.slice(1),H=Jn(p,H,G),r=qe(H,H),n=Wt((function(t){return!t.predecessors||!er(e.callback,t.predecessors)}),r),B=Le(B,n),F=Le(F,n.map((function(t){var r;return vc(vc({},t),{},{predecessors:Le(null!==(r=t.predecessors)&&void 0!==r?r:[],[e.callback])})})));case 8:case"end":return t.stop()}}),t)}));case 29:if(!H.length){t.next=33;break}return t.delegateYield($(),"t0",31);case 31:t.next=29;break;case 33:y=Le(qe(y,B),F),z=sc((function(t){return t.executionGroup}),Wt((function(t){return!ge(t.executionGroup)}),l)),J=Wt((function(t){if(!t.executionGroup||!z[t.executionGroup]||!z[t.executionGroup].length)return!1;var e=ae(Hn,Qt(t.getInputs(p))),r=Qt(ae((function(t){return t.executionMeta.allProps}),z[t.executionGroup])),n=Qt(ae((function(t){return t.executionMeta.updatedProps}),z[t.executionGroup]));return Nt(sr(e,n))&&Nt(qe(e,r))&&!De(Tn,t.callback.inputs)}),G),y=qe(y,J),G=qe(G,J),y=qe(y,G),K=qe(y,v),Y=qe(v,y),r(Ia([K.length?_a(K):null,Y.length?ka(Y):null,O.length?Aa(O):null,_.length?xa(_):null,E.length?Pa(E):null,j.length?La(j):null,T.length?Aa(T):null,k.length?Oa(k):null,R.length?xa(R):null,I.length?ba(I):null,D.length?Pa(D):null,N.length?wa(N):null,q.length?La(q):null,U.length?ja(U):null,G.length?Oa(G):null]));case 42:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}(),inputs:["callbacks.requested","callbacks.completed"]},mc=gc;function wc(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(t,e)||function(t,e){if(t){if("string"==typeof t)return Oc(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Oc(t,e):void 0}}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Oc(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var _c={observer:function(t){var e=t.dispatch,r=t.getState,n=r().callbacks,o=Du(n),i=r().callbacks.stored,a=wc(Je((function(t){return ge(t.executionGroup)}),i),2),u=a[0],c=a[1],s=sc((function(t){return t.executionGroup}),c),f=sc((function(t){return t.executionGroup}),Wt((function(t){return!ge(t.executionGroup)}),o)),l=be((function(t,e){var r=wc(e,2),n=r[0],o=r[1];return f[n]?t:Le(t,o)}),[],bi(s));e(Ia([u.length?Ta(u):null,l.length?Ta(l):null]))},inputs:["callbacks.stored","callbacks.completed"]},Ec=_c;function jc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,xc(n.key),n)}}function Sc(t,e,r){return(e=xc(e))in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function xc(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}var Pc=function(){function t(){var e=this;!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),Sc(this,"__store",void 0),Sc(this,"storeObserver",new hi),Sc(this,"setObservers",z((function(){var t=e.storeObserver.observe;t(vi),t(qu),t($u),t(mc),t(ac),t(Ru),t(xu),t(Ec)}))),Sc(this,"createAppStore",(function(t,r){e.__store=tt(t,r),e.storeObserver.setStore(e.__store),e.setObservers()})),Sc(this,"initializeStore",(function(t){if(e.__store&&!t)return e.__store;var r=function(){return function(t){return function(e,r){var n=e||{},o=n.history,i=n.config,a=n.hooks,u=e;return"RELOAD"===r.type?u={history:o,config:i,hooks:a}:"SET_CONFIG"===r.type&&(u={hooks:a}),t(u,r)}}((t=ui(),function(e,r){var n=r.type,o=r.payload;if("ON_PROP_CHANGE"===n){var i=ci(o,e,!0);i&&!Nt(i.props)&&(e.history.present=i)}var a,u=t(e,r);if("ON_PROP_CHANGE"===n&&"response"!==o.source){var c=ci(o,u);c&&!Nt(c.props)&&(u.history={past:[].concat((a=u.history.past,function(t){if(Array.isArray(t))return ii(t)}(a)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(a)||function(t,e){if(t){if("string"==typeof t)return ii(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?ii(t,e):void 0}}(a)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),[e.history.present]),present:c,future:[]})}return u}));var t}();return e.createAppStore(r,rt(it)),t||(window.store=e.__store),e.__store})),this.__store=this.initializeStore()}var e,r;return e=t,(r=[{key:"store",get:function(){return this.__store}}])&&jc(e.prototype,r),Object.defineProperty(e,"prototype",{writable:!1}),t}(),Ac=H((function(t){return oe(t.length,(function(){var e=0,r=arguments[0],n=arguments[arguments.length-1],o=Array.prototype.slice.call(arguments,0);return o[0]=function(){var t=r.apply(this,pr(arguments,[e,n]));return e+=1,t},t.apply(this,o)}))})),kc=Ac,Tc=ct((function(t,e){return Ft((function(r,n){return r[n]=t(e[n],n,e),r}),{},jt(e))})),Lc=Tc,Ic=Dt((function(t,e,r){return Fr(t,Be(e,r))})),Rc=["String","Number","Null","Boolean"],Cc=function(t){return er(Lt(t),Rc)};function Nc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(void 0,"symbol"==typeof(o=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(n.key))?o:String(o)),n)}var o}function Dc(t,e){return Dc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Dc(t,e)}function Mc(t){return Mc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Mc(t)}var Uc=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Dc(t,e)}(u,t);var e,r,n,o,i,a=(o=u,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,e=Mc(o);if(i){var r=Mc(this).constructor;t=Reflect.construct(e,arguments,r)}else t=e.apply(this,arguments);return function(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,t)});function u(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),(e=a.call(this,t)).state={myID:t.componentId,oldChildren:null,hasError:!1},e}return e=u,r=[{key:"componentDidCatch",value:function(t,e){var r=this.props.dispatch;r(Ai({myID:this.state.myID,type:"frontEnd",error:t,info:e})),r(Fi)}},{key:"componentDidUpdate",value:function(t,e){var r=t.children;this.state.hasError||r===e.oldChildren||r===this.props.children||this.setState({oldChildren:r})}},{key:"render",value:function(){var t=this.state,e=t.hasError,r=t.oldChildren;return e?r:this.props.children}}],n=[{key:"getDerivedStateFromError",value:function(t){return{hasError:!0}}}],r&&Nc(e.prototype,r),n&&Nc(e,n),Object.defineProperty(e,"prototype",{writable:!1}),u}(t.Component);Uc.propTypes={children:u().object,componentId:u().string,error:u().object,dispatch:u().func};var qc=Uc,Gc=r(414),Bc=r.n(Gc),Fc=!1;function Hc(t,e,r){var n;if(!r)return Fc;var o=yt(e,r);if(!o)return Fc;var i=o.__dashprivate__idprop__;if(i)return{is_loading:!0,prop_name:i.property,component_name:gn(i.id)};var a,u=null===(n=o.__dashprivate__idprops__)||void 0===n?void 0:n[0];return u&&(zc(a=t),tn.resolve(a)._dashprivate_isLoadingComponent)?{is_loading:!0,prop_name:u.property,component_name:gn(u.id)}:Fc}var $c=function(t,e){var r,n;return(null!==(r=e&&(null===(n=yt(t,e))||void 0===n?void 0:n.__dashprivate__idprops__))&&void 0!==r?r:[]).map((function(t){var e=t.id,r=t.property;return"".concat(e,".").concat(r)})).join(",")};function zc(t){if("Array"===Lt(t))throw new Error("The children property of a component is a list of lists, instead of just a list. Check the component that has the following contents, and remove one of the levels of nesting: \n"+JSON.stringify(t,null,2));if("Object"===Lt(t)&&!(Gr("namespace",t)&&Gr("type",t)&&Gr("props",t)))throw new Error("An object was provided as `children` instead of a component, string, or number (or list of those). Check the children property that looks something like:\n"+JSON.stringify(t,null,2))}function Jc(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Kc(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Jc(Object(r),!0).forEach((function(e){var n,o,i;n=t,o=e,i=r[e],(o=Wc(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Jc(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Yc(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,Wc(n.key),n)}}function Wc(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}function Vc(t,e){return Vc=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},Vc(t,e)}function Zc(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Qc(t){return Qc=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Qc(t)}function Xc(){return Xc=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},Xc.apply(this,arguments)}function ts(t){return function(t){if(Array.isArray(t))return es(t)}(t)||function(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}(t)||function(t,e){if(t){if("string"==typeof t)return es(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?es(t,e):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function es(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var rs={is_loading:!1};function ns(t){var e=t.element,r=t.extraProps,n=t.props,o=t.children,i=t.type,a=function(t,e,r,n){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:null,i=[];for(var a in t)if(t.hasOwnProperty(a)){var u=void 0;try{"function"!=typeof t[a]?(u=Error((n||"React class")+": "+r+" type `"+a+"` is invalid; it must be a function, usually from the `prop-types` package, but received `"+typeof t[a]+"`.")).name="Invariant Violation":u=t[a](e,a,n,r,null,Bc())}catch(t){u=t}if(!u||u instanceof Error||i.push((n||"React class")+": type specification of "+r+" `"+a+"` is invalid; the type checker function must return `null` or an `Error` but returned a "+typeof u+". You may have forgotten to pass an argument to the type checker creator (arrayOf, instanceOf, objectOf, oneOf, oneOfType, and shape all require an argument)."),u instanceof Error){var c=o&&o()||"";i.push("Failed "+r+" type: "+u.message+c)}}return i.join("\n\n")}(e.propTypes,n,"component prop",e);return a&&function(t,e,r){var n,o=t.split("`");if(er("is marked as required",t)){var i=o[1];n="".concat(i," in ").concat(r),e.id&&(n+=' with ID "'.concat(e.id,'"')),n+=" is required but it was not provided."}else if(er("Bad object",t))n=t.split("supplied to ")[0]+"supplied to ".concat(r)+".\nBad"+t.split(".\nBad")[1];else{if(!er("Invalid ",t)||!er(" supplied to ",t))throw new Error(t);var a=o[1];if(n="Invalid argument `".concat(a,"` passed into ").concat(r),e.id&&(n+=' with ID "'.concat(e.id,'"')),n+=".",er(", expected ",t)){var u=t.split(", expected ")[1];n+="\nExpected ".concat(u)}if(er(" of type `",t)){var c=t.split(" of type `")[1].split("`")[0];n+="\nWas supplied type `".concat(c,"`.")}if(Gr(a,e)){var s=JSON.stringify(e[a],null,2);s&&(er("\n",s)?n+="\nValue provided: \n".concat(s):n+="\nValue provided: ".concat(s))}}throw new Error(n)}(a,n,i),os(e,n,r,o)}function os(t,r,n,o){var i=_r(r,n);return Array.isArray(o)?e().createElement.apply(e(),[t,i].concat(ts(o))):e().createElement(t,i,o)}function is(t){return"Object"===Lt(t)&&Gr("type",t)&&Gr("namespace",t)&&Gr("props",t)}ns.propTypes={children:u().any,element:u().any,layout:u().any,props:u().any,extraProps:u().any,id:u().string};var as=function(r){var n=(0,t.useContext)(gs);return e().createElement(cs,Xc({},n.fn(),r,{_dashprivate_path:JSON.parse(r._dashprivate_path)}))},us=(0,t.memo)(as),cs=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&Vc(t,e)}(c,t);var r,n,i,a,u=(i=c,a=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,e=Qc(i);if(a){var r=Qc(this).constructor;t=Reflect.construct(e,arguments,r)}else t=e.apply(this,arguments);return function(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return Zc(t)}(this,t)});function c(t){var e;return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,c),(e=u.call(this,t)).setProps=e.setProps.bind(Zc(e)),e}return r=c,n=[{key:"createContainer",value:function(t,r,n){var o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:void 0;return Cc(r)?r:e().createElement(us,{key:r&&r.props&&gn(r.props.id)||o,_dashprivate_error:t._dashprivate_error,_dashprivate_layout:r,_dashprivate_loadingState:Hc(r,n,t._dashprivate_loadingMap),_dashprivate_loadingStateHash:$c(n,t._dashprivate_loadingMap),_dashprivate_path:JSON.stringify(n)})}},{key:"setProps",value:function(t){var e=this.props,r=e._dashprivate_dispatch,n=e._dashprivate_path,i=e._dashprivate_layout,a=this.getLayoutProps(),u=a.id,c=Ke((function(t,e){return!Ct(t,a[e])}),t);Nt(c)||r((function(e,r){var a=r().graphs,s=function(t,e,r){if(!(t&&r&&e.length))return[];if("string"==typeof t){var n=r.inputMap[t];return n?e.filter((function(t){return n[t]})):[]}var o=Object.keys(t).sort(),i=Bt(o,t),a=o.join(","),u=r.inputPatterns[a];return u?e.filter((function(t){var e=u[t];return e&&e.some((function(t){return An(o,i,t.values)}))})):[]}(u,jt(c),a);(0,o.unstable_batchedUpdates)((function(){!function(t,e,r){var n=bu(t),o=n.canPersist,i=n.id,a=n.props,u=n.element,c=n.persistence,s=n.persisted_props,f=n.persistence_type;o&&c&&ft((function(t){var n=Xa(t.split("."),2),o=n[0],s=n[1];if(void 0!==e[o]){var l=hu(f,r),p=yu(u,o,s).extract,h=vu(i,t,c),d=p(a[o]),y=p(e[o]);if(d!==y){l.hasItem(h)&&(d=l.getItem(h)[1]);var v=void 0===d?[y]:[y,d];l.setItem(h,v,r)}}}),s)}(i,t,e),s.length&&e($i({id:u,props:gi(s,c)})),e(Di({props:c,itempath:n}))}))}))}},{key:"getChildren",value:function(t,e){var r=this;return ge(t)?null:Array.isArray(t)?kc(ae)((function(t,n){return r.createContainer(r.props,t,Le(e,["props","children",n]))}),t):this.createContainer(this.props,t,Le(e,["props","children"]))}},{key:"wrapChildrenProp",value:function(t,e){var r=this;return Array.isArray(t)?t.map((function(t,n){return is(t)?r.createContainer(r.props,t,Le(r.props._dashprivate_path,["props"].concat(ts(e),[n])),n):t})):is(t)?this.createContainer(this.props,t,Le(this.props._dashprivate_path,["props"].concat(ts(e)))):t}},{key:"getComponent",value:function(t,r,n,o){var i=this,a=this.props,u=a._dashprivate_config,c=a._dashprivate_dispatch,s=a._dashprivate_error;if(Nt(t))return null;if(Cc(t))return t;zc(t);for(var f=tn.resolve(t),l=Hr([],["children_props",t.namespace,t.type],u),p=ti("children",t.props),h=function(){var t=l[d],e=function(t,e){return Lc((function(t,r){return i.wrapChildrenProp(t,[].concat(ts(e),[r]))}),t)};if(t.includes(".")){var r,n,o=t.split(".");if(t.includes("[]")){var a=[],u=[],c=!1,s=!1;if(o.forEach((function(t){c?t.includes("{}")?(s=!0,u.push(t.replace("{}",""))):u.push(t):t.includes("[]")?(c=!0,t.includes("{}")?(s=!0,a.push(t.replace("{}","").replace("[]",""))):a.push(t.replace("[]",""))):t.includes("{}")?(s=!0,a.push(t.replace("{}",""))):a.push(t)})),void 0===(r=yt(a,p))||!r.length)return 0;if(!yt(u,r[0]))return 0;n=r.map((function(t,r){var n,o=Le(a,Le([r],u));return n=s?u.length?e(yt(u,t),o):e(t,o):i.wrapChildrenProp(yt(u,t),o),me(u,n,t)})),o=a}else if(t.includes("{}")){for(var f=[],h=[],y=!1,v=[],b=0;b<o.length;b++){var g=o[b];g.includes("{}")?(h=Le(f,[g.replace("{}","")]),b<o.length-1&&(y=!0)):y?v.push(g):f.push(g)}var m=yt(h,p);void 0!==m&&(n=Lc((function(t,e){return i.wrapChildrenProp(y?yt(v,t):t,Le(h,y?Le([e],v):[e]))}),m),o=h)}else{if(void 0===(r=yt(o,p)))return 0;n=i.wrapChildrenProp(r,o)}p=me(o,n,p)}else if(t.includes("{}")){var w=t.replace("{}",""),O=t.includes("[]");O&&(w=w.replace("[]",""));var _=p[w];if(void 0!==_)if(O)for(var E=0;E<_.length;E++){var j=Le([w],[E]);p=me(j,e(_[E],j),p)}else p=we(w,e(_,[w]),p)}else{var S=p[t];void 0!==S&&(p=we(t,i.wrapChildrenProp(S,[t]),p))}},d=0;d<l.length;d++)h();"Object"===Lt(p.id)&&(p.id=gn(p.id));var y={loading_state:n||rs,setProps:o};return e().createElement(qc,{componentType:t.type,componentId:p.id,key:p.id,dispatch:c,error:s},u.props_check?e().createElement(ns,{children:r,element:f,props:p,extraProps:y,type:t.type}):os(f,p,y,r))}},{key:"getLayoutProps",value:function(){return Ic({},"props",this.props._dashprivate_layout)}},{key:"render",value:function(){var t=this.props,e=t._dashprivate_layout,r=t._dashprivate_loadingState,n=t._dashprivate_path,o=this.getLayoutProps(),i=this.getChildren(o.children,n);return this.getComponent(e,i,r,this.setProps)}}],n&&Yc(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),c}(t.Component);us.propTypes={_dashprivate_error:u().any,_dashprivate_layout:u().object,_dashprivate_loadingState:u().oneOfType([u().object,u().bool]),_dashprivate_loadingStateHash:u().string,_dashprivate_path:u().string},cs.propTypes=Kc(Kc({},us.propTypes),{},{_dashprivate_config:u().object,_dashprivate_dispatch:u().func,_dashprivate_graphs:u().any,_dashprivate_loadingMap:u().any,_dashprivate_path:u().array});var ss=us;function fs(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(void 0,"symbol"==typeof(o=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(n.key))?o:String(o)),n)}var o}function ls(t,e){return ls=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},ls(t,e)}function ps(t){return ps=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},ps(t)}var hs=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ls(t,e)}(u,t);var r,n,o,i,a=(o=u,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,e=ps(o);if(i){var r=ps(this).constructor;t=Reflect.construct(e,arguments,r)}else t=e.apply(this,arguments);return function(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,t)});function u(t){return function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),a.call(this,t)}return r=u,(n=[{key:"render",value:function(){return e().createElement("div",{id:"_dash-app-content"},this.props.children)}}])&&fs(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),u}(t.Component);hs.propTypes={children:u().object};var ds=hs;function ys(){ys=function(){return e};var t,e={},r=Object.prototype,n=r.hasOwnProperty,o=Object.defineProperty||function(t,e,r){t[e]=r.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",u=i.asyncIterator||"@@asyncIterator",c=i.toStringTag||"@@toStringTag";function s(t,e,r){return Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}),t[e]}try{s({},"")}catch(t){s=function(t,e,r){return t[e]=r}}function f(t,e,r,n){var i=e&&e.prototype instanceof b?e:b,a=Object.create(i.prototype),u=new T(n||[]);return o(a,"_invoke",{value:x(t,r,u)}),a}function l(t,e,r){try{return{type:"normal",arg:t.call(e,r)}}catch(t){return{type:"throw",arg:t}}}e.wrap=f;var p="suspendedStart",h="suspendedYield",d="executing",y="completed",v={};function b(){}function g(){}function m(){}var w={};s(w,a,(function(){return this}));var O=Object.getPrototypeOf,_=O&&O(O(L([])));_&&_!==r&&n.call(_,a)&&(w=_);var E=m.prototype=b.prototype=Object.create(w);function j(t){["next","throw","return"].forEach((function(e){s(t,e,(function(t){return this._invoke(e,t)}))}))}function S(t,e){function r(o,i,a,u){var c=l(t[o],t,i);if("throw"!==c.type){var s=c.arg,f=s.value;return f&&"object"==typeof f&&n.call(f,"__await")?e.resolve(f.__await).then((function(t){r("next",t,a,u)}),(function(t){r("throw",t,a,u)})):e.resolve(f).then((function(t){s.value=t,a(s)}),(function(t){return r("throw",t,a,u)}))}u(c.arg)}var i;o(this,"_invoke",{value:function(t,n){function o(){return new e((function(e,o){r(t,n,e,o)}))}return i=i?i.then(o,o):o()}})}function x(e,r,n){var o=p;return function(i,a){if(o===d)throw new Error("Generator is already running");if(o===y){if("throw"===i)throw a;return{value:t,done:!0}}for(n.method=i,n.arg=a;;){var u=n.delegate;if(u){var c=P(u,n);if(c){if(c===v)continue;return c}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(o===p)throw o=y,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);o=d;var s=l(e,r,n);if("normal"===s.type){if(o=n.done?y:h,s.arg===v)continue;return{value:s.arg,done:n.done}}"throw"===s.type&&(o=y,n.method="throw",n.arg=s.arg)}}}function P(e,r){var n=r.method,o=e.iterator[n];if(o===t)return r.delegate=null,"throw"===n&&e.iterator.return&&(r.method="return",r.arg=t,P(e,r),"throw"===r.method)||"return"!==n&&(r.method="throw",r.arg=new TypeError("The iterator does not provide a '"+n+"' method")),v;var i=l(o,e.iterator,r.arg);if("throw"===i.type)return r.method="throw",r.arg=i.arg,r.delegate=null,v;var a=i.arg;return a?a.done?(r[e.resultName]=a.value,r.next=e.nextLoc,"return"!==r.method&&(r.method="next",r.arg=t),r.delegate=null,v):a:(r.method="throw",r.arg=new TypeError("iterator result is not an object"),r.delegate=null,v)}function A(t){var e={tryLoc:t[0]};1 in t&&(e.catchLoc=t[1]),2 in t&&(e.finallyLoc=t[2],e.afterLoc=t[3]),this.tryEntries.push(e)}function k(t){var e=t.completion||{};e.type="normal",delete e.arg,t.completion=e}function T(t){this.tryEntries=[{tryLoc:"root"}],t.forEach(A,this),this.reset(!0)}function L(e){if(e||""===e){var r=e[a];if(r)return r.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var o=-1,i=function r(){for(;++o<e.length;)if(n.call(e,o))return r.value=e[o],r.done=!1,r;return r.value=t,r.done=!0,r};return i.next=i}}throw new TypeError(typeof e+" is not iterable")}return g.prototype=m,o(E,"constructor",{value:m,configurable:!0}),o(m,"constructor",{value:g,configurable:!0}),g.displayName=s(m,c,"GeneratorFunction"),e.isGeneratorFunction=function(t){var e="function"==typeof t&&t.constructor;return!!e&&(e===g||"GeneratorFunction"===(e.displayName||e.name))},e.mark=function(t){return Object.setPrototypeOf?Object.setPrototypeOf(t,m):(t.__proto__=m,s(t,c,"GeneratorFunction")),t.prototype=Object.create(E),t},e.awrap=function(t){return{__await:t}},j(S.prototype),s(S.prototype,u,(function(){return this})),e.AsyncIterator=S,e.async=function(t,r,n,o,i){void 0===i&&(i=Promise);var a=new S(f(t,r,n,o),i);return e.isGeneratorFunction(r)?a:a.next().then((function(t){return t.done?t.value:a.next()}))},j(E),s(E,c,"Generator"),s(E,a,(function(){return this})),s(E,"toString",(function(){return"[object Generator]"})),e.keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}return t.done=!0,t}},e.values=L,T.prototype={constructor:T,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=t,this.done=!1,this.delegate=null,this.method="next",this.arg=t,this.tryEntries.forEach(k),!e)for(var r in this)"t"===r.charAt(0)&&n.call(this,r)&&!isNaN(+r.slice(1))&&(this[r]=t)},stop:function(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var r=this;function o(n,o){return u.type="throw",u.arg=e,r.next=n,o&&(r.method="next",r.arg=t),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],u=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var c=n.call(a,"catchLoc"),s=n.call(a,"finallyLoc");if(c&&s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(t,e){for(var r=this.tryEntries.length-1;r>=0;--r){var o=this.tryEntries[r];if(o.tryLoc<=this.prev&&n.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===t||"continue"===t)&&i.tryLoc<=e&&e<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=t,a.arg=e,i?(this.method="next",this.next=i.finallyLoc,v):this.complete(a)},complete:function(t,e){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&e&&(this.next=e),v},finish:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.finallyLoc===t)return this.complete(r.completion,r.afterLoc),k(r),v}},catch:function(t){for(var e=this.tryEntries.length-1;e>=0;--e){var r=this.tryEntries[e];if(r.tryLoc===t){var n=r.completion;if("throw"===n.type){var o=n.arg;k(r)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(e,r,n){return this.delegate={iterator:L(e),resultName:r,nextLoc:n},"next"===this.method&&(this.arg=t),v}},e}function vs(t,e,r,n,o,i,a){try{var u=t[i](a),c=u.value}catch(t){return void r(t)}u.done?e(c):Promise.resolve(c).then(n,o)}function bs(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var gs=(0,t.createContext)({}),ms=function(r){var n,o,i=r.appLifecycle,a=r.config,u=r.dependenciesRequest,c=r.error,s=r.layoutRequest,f=r.layout,l=r.loadingMap,p=(n=(0,t.useState)(!1),o=2,function(t){if(Array.isArray(t))return t}(n)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(n,o)||function(t,e){if(t){if("string"==typeof t)return bs(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?bs(t,e):void 0}}(n,o)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()),h=p[0],d=p[1],y=(0,t.useRef)(null);y.current||(y.current=new Zr);var v=(0,t.useRef)(!1),b=(0,t.useRef)({});b.current=r;var g,m=(0,t.useRef)({fn:function(){return{_dashprivate_config:b.current.config,_dashprivate_dispatch:b.current.dispatch,_dashprivate_graphs:b.current.graphs,_dashprivate_loadingMap:b.current.loadingMap}}});return(0,t.useEffect)(ws.bind(null,r,y,d)),(0,t.useEffect)((function(){v.current&&function(t){return function(){var e=this,r=arguments;return new Promise((function(n,o){var i=t.apply(e,r);function a(t){vs(i,n,o,a,u,"next",t)}function u(t){vs(i,n,o,a,u,"throw",t)}a(void 0)}))}}(ys().mark((function t(){return ys().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return v.current=!1,t.next=3,hc(0);case 3:y.current.emit("rendered");case 4:case"end":return t.stop()}}),t)})))()})),(0,t.useEffect)((function(){a.serve_locally?window._dashPlotlyJSURL="".concat(a.requests_pathname_prefix,"_dash-component-suites/plotly/package_data/plotly.min.js"):window._dashPlotlyJSURL=a.plotlyjs_url}),[]),s.status&&!er(s.status,[Io.OK,"loading"])?g=e().createElement("div",{className:"_dash-error"},"Error loading layout"):h||u.status&&!er(u.status,[Io.OK,"loading"])?g=e().createElement("div",{className:"_dash-error"},"Error loading dependencies"):i===ro("HYDRATED")?(v.current=!0,g=e().createElement(gs.Provider,{value:m.current},e().createElement(ss,{_dashprivate_error:c,_dashprivate_layout:f,_dashprivate_loadingState:Hc(f,[],l),_dashprivate_loadingStateHash:$c([],l),_dashprivate_path:JSON.stringify([])}))):g=e().createElement("div",{className:"_dash-loading"},"Loading..."),a&&!0===a.ui?e().createElement(ds,null,g):g};function ws(t,e,r){var n=t.appLifecycle,i=t.dependenciesRequest,a=t.dispatch,u=t.error,c=t.graphs,s=t.hooks,f=t.layout,l=t.layoutRequest;(0,o.unstable_batchedUpdates)((function(){if(Nt(l))"function"==typeof s.layout_pre&&s.layout_pre(),a(ia("_dash-layout","GET","layoutRequest"));else if(l.status===Io.OK&&Nt(f)){"function"==typeof s.layout_post&&s.layout_post(l.content);var t=gu(l.content,a);a(Ni(Qr(t,[],null,e.current))),a(Ci(t))}if(Nt(i)?a(ia("_dash-dependencies","GET","dependenciesRequest")):i.status===Io.OK&&(Nt(c)||c.reset)&&a(Ii(xn(i.content,Mi(a)))),i.status===Io.OK&&!Nt(c)&&l.status===Io.OK&&!Nt(f)&&n===ro("STARTED")){var o=!1;try{a((Mi(a),function(t,e){!function(t,e){var r,n,o=t.config,i=t.graphs,a=t.layout,u=t.paths,c=!o.suppress_callback_exceptions;c&&o.validation_layout?(r=o.validation_layout,n=Qr(r,[],null,u.events)):(r=a,n=u);var s=i.outputMap,f=i.inputMap,l=i.outputPatterns,p=i.inputPatterns;function h(t){return"This ID was used in the callback(s) for Output(s):\n  "+t.map((function(t){return t.outputs.map(Hn).join(", ")})).join("\n  ")}function d(t,r,n){e("ID not found in layout",["Attempting to connect a callback ".concat(r," item to component:"),'  "'.concat(gn(t),'"'),"but no components with that id exist in the layout.","","If you are assigning callbacks to components that are","generated by other callbacks (and therefore not in the","initial layout), you can suppress this exception by setting","`suppress_callback_exceptions=True`.",h(n)])}function y(t,n,o,i,a){var u=o.split("@")[0],c=yt(n,r),s=tn.resolve(c);if(s&&s.propTypes&&!s.propTypes[u]){for(var f in s.propTypes){var l=f.length-1;if("*"===f.charAt(l)&&u.substr(0,l)===f.substr(0,l))return}var p=c.type,d=c.namespace;e("Invalid prop for this component",['Property "'.concat(u,'" was used with component ID:'),"  ".concat(JSON.stringify(t)),"in one of the ".concat(i," items of a callback."),"This ID is assigned to a ".concat(d,".").concat(p," component"),"in the layout, which does not support this property.",h(a)])}}function v(t,e,r,o){Qn()(n)({id:t,property:e}).forEach((function(t){y(t.id,t.path,e,r,o)}))}var b={};function g(t){var e=t.state,r=t.output;if(!b[r]){b[r]=1;var o="State";e.forEach((function(e){var r=e.id,i=e.property;if("string"==typeof r){var a=Xr(n,r);a?y(r,a,i,o,[t]):c&&d(r,o,[t])}else sr([sn,fn],fr(r)).length||v(r,i,o,[t])}))}}function m(t,e,r){for(var o in t){var i=t[o],a=Xr(n,o);if(a)for(var u in i){var s=i[u];y(o,a,u,e,s),r&&s.forEach(g)}else c&&d(o,e,Qt(fr(i)))}}function w(t,e,r){for(var n in t){var o=t[n],i=function(t){o[t].forEach((function(n){var o=n.keys,i=n.values,a=n.callbacks;v(We(o,i),t,e,a),r&&a.forEach(g)}))};for(var a in o)i(a)}}m(s,"Output",!0),m(f,"Input"),w(l,"Output",!0),w(p,"Input")}(e(),Mi(t)),function(t,e){var r=e(),n=r.graphs,o=r.paths,i=r.layout;try{n.MultiGraph.overallOrder()}catch(e){t(Ai({type:"backEnd",error:{message:"Circular Dependencies",html:e.toString()}}))}t(_a(Kn(n,o,i,{outputsOnly:!0})))}(t,e),t(ki(ro("HYDRATED")))}))}catch(t){u.frontEnd.length||u.backEnd.length||a(Ai({type:"backEnd",error:t})),o=!0}finally{r(o)}}}))}ms.propTypes={appLifecycle:u().oneOf([ro("STARTED"),ro("HYDRATED"),ro("DESTROYED")]),dispatch:u().func,dependenciesRequest:u().object,graphs:u().object,hooks:u().object,layoutRequest:u().object,layout:u().object,loadingMap:u().any,history:u().any,error:u().object,config:u().object};var Os=q((function(t){return{appLifecycle:t.appLifecycle,dependenciesRequest:t.dependenciesRequest,hooks:t.hooks,layoutRequest:t.layoutRequest,layout:t.layout,loadingMap:t.loadingMap,graphs:t.graphs,history:t.history,error:t.error,config:t.config}}),(function(t){return{dispatch:t}}))(ms);function _s(t){return t.isLoading?e().createElement("div",{className:"_dash-loading-callback"}):null}_s.propTypes={isLoading:u().bool.isRequired};var Es=q((function(t){return{isLoading:t.isLoading}}))(_s),js=r(379),Ss=r.n(js),xs=r(795),Ps=r.n(xs),As=r(569),ks=r.n(As),Ts=r(565),Ls=r.n(Ts),Is=r(216),Rs=r.n(Is),Cs=r(589),Ns=r.n(Cs),Ds=r(789),Ms={};function Us(t){var r=t.dispatch,n=t.history,o=e().createElement("span",{key:"undoLink",className:"_dash-undo-redo-link",onClick:function(){return r(Bi)}},e().createElement("div",{className:"_dash-icon-undo"},"↺"),e().createElement("div",{className:"_dash-undo-redo-label"},"undo")),i=e().createElement("span",{key:"redoLink",className:"_dash-undo-redo-link",onClick:function(){return r(Gi)}},e().createElement("div",{className:"_dash-icon-redo"},"↻"),e().createElement("div",{className:"_dash-undo-redo-label"},"redo"));return e().createElement("div",{className:"_dash-undo-redo"},e().createElement("div",null,n.past.length>0?o:null,n.future.length>0?i:null))}Ms.styleTagTransform=Ns(),Ms.setAttributes=Ls(),Ms.insert=ks().bind(null,"head"),Ms.domAPI=Ps(),Ms.insertStyleElement=Rs(),Ss()(Ds.Z,Ms),Ds.Z&&Ds.Z.locals&&Ds.Z.locals,Us.propTypes={history:u().object,dispatch:u().func};var qs=q((function(t){return{history:t.history}}),(function(t){return{dispatch:t}}))(Us),Gs=H((function(t){return function(e,r){return t(e,r)?-1:t(r,e)?1:0}})),Bs=ct((function(t,e){return t<e}));function Fs(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}function Hs(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(void 0,"symbol"==typeof(o=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(n.key))?o:String(o)),n)}var o}function $s(t,e){return $s=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},$s(t,e)}function zs(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function Js(t){return Js=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},Js(t)}var Ks=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&$s(t,e)}(u,t);var e,r,n,o,i,a=(o=u,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,e=Js(o);if(i){var r=Js(this).constructor;t=Reflect.construct(e,arguments,r)}else t=e.apply(this,arguments);return function(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return zs(t)}(this,t)});function u(t){var e;if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),e=a.call(this,t),t.config.hot_reload){var r=t.config.hot_reload,n=r.interval,o=r.max_retry;e.state={interval:n,disabled:!1,intervalId:null,packages:null,max_retry:o}}else e.state={disabled:!0};return e._retry=0,e._head=document.querySelector("head"),e.clearInterval=e.clearInterval.bind(zs(e)),e}return e=u,r=[{key:"clearInterval",value:function(){window.clearInterval(this.state.intervalId),this.setState({intervalId:null})}},{key:"componentDidUpdate",value:function(t,e){var r=this.state.reloadRequest,n=this.props.dispatch;if(r&&Gr("reloadRequest",e))if(200===r.status&&yt(["content","reloadHash"],r)!==yt(["reloadRequest","content","reloadHash"],e))if(!r.content.hard&&Ct(r.content.packages.length,Hr([],["reloadRequest","content","packages"],e).length)&&Ct(zu(Gs(Bs),r.content.packages),zu(Gs(Bs),Hr([],["reloadRequest","content","packages"],e))))n({type:"RELOAD"});else{var o,i=!1,a=function(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=function(t,e){if(t){if("string"==typeof t)return Fs(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?Fs(t,e):void 0}}(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var n=0,o=function(){};return{s:o,n:function(){return n>=t.length?{done:!0}:{done:!1,value:t[n++]}},e:function(t){throw t},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,u=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return a=t.done,t},e:function(t){u=!0,i=t},f:function(){try{a||null==r.return||r.return()}finally{if(u)throw i}}}}(r.content.files);try{for(a.s();!(o=a.n()).done;){var u=o.value;if(!u.is_css){i=!1;break}i=!0;for(var c=[],s=document.evaluate('//link[contains(@href, "'.concat(u.url,'")]'),this._head),f=s.iterateNext();f;)c.push(f),f=s.iterateNext();if(ft((function(t){return t.setAttribute("disabled","disabled")}),c),u.modified>0){var l=document.createElement("link");l.href="".concat(u.url,"?m=").concat(u.modified),l.type="text/css",l.rel="stylesheet",this._head.appendChild(l)}}}catch(t){a.e(t)}finally{a.f()}i||window.location.reload()}else null!==this.state.intervalId&&500===r.status&&(this._retry>this.state.max_retry&&(this.clearInterval(),window.alert("Hot reloading is disabled after failing ".concat(this._retry," times. ")+"Please check your application for errors, then refresh the page.")),this._retry++)}},{key:"componentDidMount",value:function(){var t=this.props,e=t.dispatch,r=t.reloadRequest,n=this.state,o=n.disabled,i=n.interval;if(!o&&!this.state.intervalId){var a=window.setInterval((function(){"loading"!==r.status&&e(ia("_reload-hash","GET","reloadRequest"))}),i);this.setState({intervalId:a})}}},{key:"componentWillUnmount",value:function(){!this.state.disabled&&this.state.intervalId&&this.clearInterval()}},{key:"render",value:function(){return null}}],n=[{key:"getDerivedStateFromProps",value:function(t){return Nt(t.reloadRequest)||"loading"===t.reloadRequest.status?null:{reloadRequest:t.reloadRequest}}}],r&&Hs(e.prototype,r),n&&Hs(e,n),Object.defineProperty(e,"prototype",{writable:!1}),u}(e().Component);Ks.defaultProps={},Ks.propTypes={id:u().string,config:u().object,reloadRequest:u().object,dispatch:u().func,interval:u().number};var Ys=q((function(t){return{config:t.config,reloadRequest:t.reloadRequest}}),(function(t){return{dispatch:t}}))(Ks),Ws=ct((function(t,e){var r={};return B(e.length,(function(){var n=t.apply(this,arguments);return vt(n,r)||(r[n]=e.apply(this,arguments)),r[n]}))})),Vs=Ws;function Zs(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function Qs(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?Zs(Object(r),!0).forEach((function(e){var n,o,i;n=t,o=e,i=r[e],(o=tf(o))in n?Object.defineProperty(n,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):n[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):Zs(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function Xs(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,tf(n.key),n)}}function tf(t){var e=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(t);return"symbol"==typeof e?e:String(e)}function ef(t,e){return ef=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(t,e){return t.__proto__=e,t},ef(t,e)}function rf(t){return rf=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},rf(t)}var nf=function(t){!function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&ef(t,e)}(u,t);var r,n,o,i,a=(o=u,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}(),function(){var t,e=rf(o);if(i){var r=rf(this).constructor;t=Reflect.construct(e,arguments,r)}else t=e.apply(this,arguments);return function(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t)}(this,t)});function u(t){var e;if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,u),e=a.call(this,t),null!==t.hooks.layout_pre||null!==t.hooks.layout_post||null!==t.hooks.request_pre||null!==t.hooks.request_post||null!==t.hooks.callback_resolved||null!==t.hooks.request_refresh_jwt){var r=t.hooks;r.request_refresh_jwt&&(r=Qs(Qs({},r),{},{request_refresh_jwt:Vs(or,r.request_refresh_jwt)})),t.dispatch(Ri(r))}return e}return r=u,n=[{key:"UNSAFE_componentWillMount",value:function(){var t,e=this.props.dispatch,r=(t=document.getElementById("_dash-config"),JSON.parse(null!=t&&t.textContent?null==t?void 0:t.textContent:"{}"));r.fetch={credentials:"same-origin",headers:{Accept:"application/json","Content-Type":"application/json"}},e(Ti(r))}},{key:"render",value:function(){var t=this.props.config;if("Null"===Lt(t))return e().createElement("div",{className:"_dash-loading"},"Loading...");var r=t.show_undo_redo;return e().createElement(e().Fragment,null,r?e().createElement(qs,null):null,e().createElement(Os,null),e().createElement(Es,null),e().createElement(Ys,null))}}],n&&Xs(r.prototype,n),Object.defineProperty(r,"prototype",{writable:!1}),u}(e().Component);nf.propTypes={hooks:u().object,dispatch:u().func,config:u().object};var of=q((function(t){return{history:t.history,config:t.config}}),(function(t){return{dispatch:t}}))(nf);function af(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,n=new Array(e);r<e;r++)n[r]=t[r];return n}var uf=function(r){var n,o,i=r.hooks,a=(n=(0,t.useState)((function(){return new Pc})),o=1,function(t){if(Array.isArray(t))return t}(n)||function(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var n,o,i,a,u=[],c=!0,s=!1;try{if(i=(r=r.call(t)).next,0===e){if(Object(r)!==r)return;c=!1}else for(;!(c=(n=i.call(r)).done)&&(u.push(n.value),u.length!==e);c=!0);}catch(t){s=!0,o=t}finally{try{if(!c&&null!=r.return&&(a=r.return(),Object(a)!==a))return}finally{if(s)throw o}}return u}}(n,o)||function(t,e){if(t){if("string"==typeof t)return af(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?af(t,e):void 0}}(n,o)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}())[0].store;return e().createElement(G,{store:a},e().createElement(of,{hooks:i}))};uf.propTypes={hooks:u().shape({layout_pre:u().func,layout_post:u().func,request_pre:u().func,request_post:u().func,callback_resolved:u().func,request_refresh_jwt:u().func})},uf.defaultProps={hooks:{layout_pre:null,layout_post:null,request_pre:null,request_post:null,callback_resolved:null,request_refresh_jwt:null}};var cf=uf;function sf(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,(void 0,"symbol"==typeof(o=function(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,"string");if("object"!=typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(t)}(n.key))?o:String(o)),n)}var o}function ff(t,e,r){return e&&sf(t.prototype,e),r&&sf(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}var lf=ff((function t(r){!function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t);var n=document.getElementById("react-entry-point");i().createRoot?i().createRoot(n).render(e().createElement(cf,{hooks:r})):i().render(e().createElement(cf,{hooks:r}),n)}));window.DashRenderer=lf}(),window.dash_renderer=n}();