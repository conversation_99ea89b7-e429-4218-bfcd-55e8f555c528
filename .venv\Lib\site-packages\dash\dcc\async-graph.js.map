{"version": 3, "file": "async-graph.js", "mappings": "40DAyBA,IAAMA,EAAoB,CACtBC,UAAU,EACVC,YAAQC,EACRC,WAAOD,GAGLE,EAAc,CAAC,EAEfC,EAAsB,CACxBL,UAAU,GAURM,EAAoB,CACtBC,YAAY,GAGVC,EAAc,CAAC,EAEfC,EAAsB,CACxBF,YAAY,GAGVG,EAAkB,SAACC,EAAIC,EAAWC,GACpC,IAAIC,EACJ,IAAIC,EAAAA,EAAAA,KAASF,EAAO,CAAC,QAAS,QAAS,aAAc,CACjD,IAAMG,EAAS,GAEf,IAAIC,EAAAA,EAAAA,KAAML,GACN,OAAO,KAaX,IAFA,IAAMM,EAAOP,EAAGO,KAAKC,EAAA,WAGjB,IAAMC,EAAYR,EAAUI,OAAOK,GAC7BC,GAAYC,EAAAA,EAAAA,MAAO,SAAUC,GAC/B,QAAQT,EAAAA,EAAAA,MAASU,EAAAA,EAAAA,KAAKD,GAAI,CAAC,SAAU,SACzC,GAAGJ,IAGCM,EAAAA,EAAAA,KAAI,OAAQN,KACZE,EAAUK,KAAOP,EAAUO,OAI3BD,EAAAA,EAAAA,KAAI,cAAeN,KACnBM,EAAAA,EAAAA,KAAI,aAAcR,EAAKI,EAAUM,iBAE7BF,EAAAA,EAAAA,KAAI,cAAeN,GACkB,iBAA1BA,EAAUS,YACjBP,EAAUQ,WACNZ,EAAKI,EAAUM,aAAaE,WACxBV,EAAUS,cAGjBT,EAAUS,aACXT,EAAUF,KAAKa,KAAKhB,SAAS,WAE7BO,EAAUQ,WACNZ,EAAKI,EAAUM,aAAaE,aAE7BJ,EAAAA,EAAAA,KAAI,eAAgBN,KAC3BE,EAAUQ,WAAaV,EAAUY,aAAaC,KAAI,SAC9CC,GAEA,OAAOhB,EAAKI,EAAUM,aAAaE,WAAWI,EAClD,OAKJR,EAAAA,EAAAA,KAAI,eAAgBN,KACpBE,EAAUU,aAAeZ,EAAUY,cAGvChB,EAAOK,GAAKC,CAChB,EA3CSD,EAAI,EAAGA,EAAIT,EAAUI,OAAOmB,OAAQd,IAAGF,IA4ChDL,EAAoB,CAACE,SACzB,KAAqB,aAAVH,GAAkC,YAAVA,IAO/BC,EAAoBF,GAQxB,OANIc,EAAAA,EAAAA,KAAI,QAASd,KACbE,EAAkBsB,MAAQxB,EAAUwB,QAEpCV,EAAAA,EAAAA,KAAI,cAAed,KACnBE,EAAkBuB,YAAczB,EAAUyB,aAEvCvB,CACX,EAQMwB,EAAW,SAAAC,I,qRAAAC,CAAAF,EAAAC,GAAA,I,QAAAE,G,EAAAH,E,gkBACb,SAAAA,EAAYI,GAAO,IAAAC,EAgB4B,O,4FAhB5BC,CAAA,KAAAN,IACfK,EAAAF,EAAAI,KAAA,KAAMH,IACD/B,GAAKmC,IAAAA,YACVH,EAAKI,aAAc,EACnBJ,EAAKK,QAAU,KACfL,EAAKM,OAASC,QAAQC,UAEtBR,EAAKS,WAAaT,EAAKS,WAAWC,KAAIC,EAAAX,IACtCA,EAAKY,UAAYZ,EAAKY,UAAUF,KAAIC,EAAAX,IACpCA,EAAKa,kBAAoBb,EAAKa,kBAAkBH,KAAIC,EAAAX,IACpDA,EAAKc,UAAYd,EAAKc,UAAUJ,KAAIC,EAAAX,IACpCA,EAAKe,kBAAoBf,EAAKe,kBAAkBL,KAAIC,EAAAX,IACpDA,EAAKgB,YAAchB,EAAKgB,YAAYN,KAAIC,EAAAX,IACxCA,EAAKiB,aAAejB,EAAKiB,aAAaP,KAAIC,EAAAX,IAC1CA,EAAKkB,YAAclB,EAAKkB,YAAYR,KAAIC,EAAAX,IAExCA,EAAKmB,MAAQ,CAACC,SAAU,CAAC,EAAGC,UAAW,CAAC,GAAGrB,CAC/C,CA2XC,O,EA3XAL,E,EAAA,EAAA2B,IAAA,OAAAC,MAED,SAAKxB,GAAO,IAAAyB,EAAA,KACHC,EAAkB1B,EAAlB0B,OAAQC,EAAU3B,EAAV2B,OACNC,EAAmD5B,EAAnD4B,QAASC,EAA0C7B,EAA1C6B,kBAAmBhE,EAAuBmC,EAAvBnC,WAAYiE,EAAW9B,EAAX8B,QAEzC7D,EAAK8D,KAAK9D,GAAG+D,QACnBN,EAAS1B,EAAMiC,6BAA6BP,EAAQzD,GACpD0D,EAAS3B,EAAMkC,6BAA6BP,EAAQ1D,GAEpD,IAAMkE,EAAcJ,KAAKlB,UAAUc,EAAQ9D,GAE3CsE,EAAYC,YAAcN,EAE1B,IAAMO,EAAc,CAChB7D,KAAMkD,EAAOlD,KACb8D,OAAQP,KAAKhB,UAAUW,EAAOY,OAAQzE,GACtC0E,OAAQb,EAAOa,OACfZ,OAAQQ,GAGZ,OACIP,GACAG,KAAK1B,aACLqB,EAAOlD,KAAKiB,SAAWxB,EAAGO,KAAKiB,OAI3BiC,EAAOa,OACAC,OAAOC,aAAaxE,GACtByE,MAAK,kBAAMF,OAAOG,UAAU1E,EAAIyD,EAAOa,OAAO,IAC9CG,MAAK,kBACFF,OAAOZ,QAAQ3D,EAAIoE,EAAaR,EAAkB,IAGvDW,OAAOZ,QAAQ3D,EAAIoE,EAAaR,IAG3C5D,EAAG2E,UAAUC,IAAI,wBAEVC,EAAAA,EAAAA,GAAgBhB,GAClBY,MAAK,WACF,IAAMzE,EAAKwD,EAAKxD,GAAG+D,QACnB,OAAO/D,GAAMuE,OAAOO,MAAM9E,EAAIoE,EAClC,IACCK,MAAK,WACF,IAAMzE,EAAKwD,EAAKxD,GAAG+D,QAGd/D,IAILA,EAAG2E,UAAUI,OAAO,uBAGhBvB,EAAKpB,aAAepC,IAAOwD,EAAKnB,UAC5BmB,EAAKnB,SAAWmB,EAAKnB,QAAQ2C,qBAC7BxB,EAAKnB,QAAQ2C,qBACbT,OAAOU,MAAMzB,EAAKnB,UAEtBmB,EAAKpB,aAAc,GAGlBoB,EAAKpB,cACNoB,EAAKf,aACLe,EAAKR,aAAY,GACjBQ,EAAKpB,aAAc,EACnBoB,EAAKnB,QAAUrC,GAEvB,IACR,GAAC,CAAAsD,IAAA,cAAAC,MAED,SAAY2B,EAAGC,EAAUC,GACrB,IAAoBC,EAAqCF,EAAlDG,YAAqCC,EAAaJ,EAAzBK,WACZC,EAAqCL,EAAlDE,YAAqCI,EAAaN,EAAzBI,WAC1BG,EAAQ7B,KAEd,SAAS8B,EAAY7D,EAAO8D,EAASC,GACjC,IAAMC,EAAahE,EAAMgE,WACnBC,EAAYjE,EAAM8D,GAEpBI,EAAK1D,QAAQC,UAqCjB,OAnCAwD,EAAUE,SAAQ,SAAA3F,GACd,IAAI4F,EAAYC,EAAcC,E,IAC9B,GAAIC,MAAMC,QAAQhG,IAA4B,iBAAZA,EAAK,GAAiB,KAAAiG,G,EACR,E,4CAAJjG,I,o2BAAvC4F,EAAUK,EAAA,GAAEJ,EAAYI,EAAA,GAAEH,EAASG,EAAA,EACxC,MACIL,EAAa5F,EAGZ6F,IAUDA,EALA,SAAyB7F,GACrB,OAAO+F,MAAMG,KACTH,MANR,SAAsB/F,GAClB,OAAOA,EAAKmG,OAAOC,KAAKpG,GAAM,GAClC,CAIcqG,CAAarG,GAAMiB,QAAQmF,OAEzC,CACeE,CAAgBV,IAGnCF,EAAKA,EAAGxB,MAAK,WACT,IAAMzE,EAAK2F,EAAM3F,GAAG+D,QACpB,OACI/D,GACAuE,OAAOuB,GACH9F,EACAmG,EACAC,EACAC,EAGZ,GACJ,IAEOJ,EAAGxB,MAAK,kBAAMsB,EAAWF,EAAQ,GAC5C,CAEA,IAAIiB,GAAW,EAsBf,OApBIrB,SAAAA,EAAYjE,QAAU6D,IAAeI,IACrCqB,GAAW,EACX5B,EAAIA,EAAET,MAAK,kBACPmB,EAAYR,EAAU,cAAe,gBAAgB,KAIzDM,SAAAA,EAAWlE,QAAU+D,IAAcG,IACnCoB,GAAW,EACX5B,EAAIA,EAAET,MAAK,kBACPmB,EAAYR,EAAU,aAAc,eAAe,KAIvD0B,IACA5B,EAAIA,EAAET,MAAK,kBACPW,EAAS2B,8BAA8B3B,EAAS3B,OAAO,KAIxDyB,CACX,GAAC,CAAA5B,IAAA,YAAAC,MAED,SAAUG,EAAQ9D,GACd,OAAOoH,EAAAA,EAAAA,KAAetD,EAAQI,KAAKjB,kBAAkBjD,GACzD,GAAC,CAAA0D,IAAA,YAAAC,MAED,SAAUc,EAAQzE,GACd,IAAKyE,EACD,OAAOA,EAEX,IAAMjB,EAAWU,KAAKf,kBAAkBnD,GACxCqH,EAA6DnD,KAAKX,MAAjD+D,EAAaD,EAAvB7D,SAAoC+D,EAAcF,EAAzB5D,UAE1BA,EAAY,CAAC,EACnB,IAAK,IAAMC,KAAOF,EACViB,EAAOf,KAAS4D,EAAc5D,GAC9BD,EAAUC,GAAOe,EAAOf,GACjB6D,EAAeC,eAAe9D,KACrCD,EAAUC,GAAO6D,EAAe7D,IAKxC,IAAK,IAAMA,KAFXQ,KAAKuD,SAAS,CAACjE,WAAUC,cAEP8D,EACV9C,EAAOf,KAAS4D,EAAc5D,KAC9Be,EAAOf,GAAO6D,EAAe7D,IAIrC,IAAK,IAAMA,KAAOF,EACdiB,EAAOf,GAAOF,EAASE,GAE3B,OAAOe,CACX,GAAC,CAAAf,IAAA,oBAAAC,MAED,SAAkB3D,GACd,OAAQA,GACJ,KAAK,EACD,OAAOE,EACX,KAAK,EACD,OAAOH,EACX,QACI,OAAOE,EAEnB,GAAC,CAAAyD,IAAA,oBAAAC,MAED,SAAkB3D,GACd,OAAQA,GACJ,KAAK,EACD,OAAOF,EACX,KAAK,EACD,OAAON,EACX,QACI,OAAOK,EAEnB,GAAC,CAAA6D,IAAA,eAAAC,MAED,SAAaxB,GACT,IAAO2B,EAA8B3B,EAA9B2B,OAAQD,EAAsB1B,EAAtB0B,OAAQ7D,EAAcmC,EAAdnC,WAEvB,MAAyB,aAArBkB,EAAAA,EAAAA,KAAKlB,GACEA,EAGJ0H,QACH5D,EAAO9D,cACD6D,EAAOY,SACHZ,EAAOY,OAAOhF,WACZiB,EAAAA,EAAAA,KAAMmD,EAAOY,OAAOhF,cACnBiB,EAAAA,EAAAA,KAAMmD,EAAOY,OAAO/E,UACjBgB,EAAAA,EAAAA,KAAMmD,EAAOY,OAAO7E,SAE5C,GAAC,CAAA8D,IAAA,cAAAC,MAED,WACI,GADagE,UAAA/F,OAAA,QAAAjC,IAAAgI,UAAA,IAAAA,UAAA,IACEzD,KAAKb,aAAaa,KAAK/B,OAAtC,CAIA,IAAM/B,EAAK8D,KAAK9D,GAAG+D,QACd/D,IAILA,EAAG2E,UAAUC,IAAI,uBAEjBL,OAAOiD,MAAMC,OAAOzH,GACf0H,OAAM,WAAO,IACbC,SAAQ,kBAAM3H,EAAG2E,UAAUI,OAAO,sBAAsB,IAX7D,CAYJ,GAAC,CAAAzB,IAAA,aAAAC,MAED,WACI,IAAAqE,EAOI9D,KAAK/B,MANL8F,EAAQD,EAARC,SACAC,EAAgBF,EAAhBE,iBACAC,EAAYH,EAAZG,aACAC,EAAWJ,EAAXI,YACAC,EAASL,EAATK,UACAC,EAAYN,EAAZM,aAGElI,EAAK8D,KAAK9D,GAAG+D,QAEnB/D,EAAGmI,GAAG,gBAAgB,SAAAlI,GAClB,IAAMmI,EAAYrI,EAAgBC,EAAIC,EAAW,UAC5CK,EAAAA,EAAAA,KAAM8H,IACPP,EAAS,CAACO,aAElB,IACApI,EAAGmI,GAAG,0BAA0B,SAAAlI,GAC5B,IAAMoI,GAAsBC,EAAAA,EAAAA,KACxB,CAAC,QAAS,kBACVrI,GAEJ4H,EAAS,CAACQ,uBACd,IACArI,EAAGmI,GAAG,gBAAgB,SAAAlI,GAClB,IAAMsI,EAAQxI,EAAgBC,EAAIC,EAAW,UACxCK,EAAAA,EAAAA,KAAMiI,KAAWC,EAAAA,EAAAA,KAAOD,EAAON,IAChCJ,EAAS,CAACI,UAAWM,GAE7B,IACAvI,EAAGmI,GAAG,mBAAmB,SAAAlI,GACrB,IAAMwI,EAAW1I,EAAgBC,EAAIC,EAAW,aAC3CK,EAAAA,EAAAA,KAAMmI,KAAcD,EAAAA,EAAAA,KAAOC,EAAUP,IACtCL,EAAS,CAACK,aAAcO,GAEhC,IACAzI,EAAGmI,GAAG,mBAAmB,WACrBN,EAAS,CAACK,aAAc,MAC5B,IACAlI,EAAGmI,GAAG,mBAAmB,SAAAlI,GACrB,IAAMyI,EAAW3I,EAAgBC,EAAIC,EAAW,aAC3CK,EAAAA,EAAAA,KAAMoI,KAAcF,EAAAA,EAAAA,KAAOE,EAAUX,IACtCF,EAAS,CAACE,aAAcW,GAEhC,IACA1I,EAAGmI,GAAG,kBAAkB,SAAAlI,GACpB,IAAM0I,EAAU5I,EAAgBC,EAAIC,EAAW,YAC1CK,EAAAA,EAAAA,KAAMqI,KAAaH,EAAAA,EAAAA,KAAOG,EAASX,IACpCH,EAAS,CAACG,YAAaW,GAE/B,IACA3I,EAAGmI,GAAG,kBAAkB,WAChBL,GACAD,EAAS,CAACI,UAAW,MAE7B,GACJ,GAAC,CAAA3E,IAAA,oBAAAC,MAED,WACI,IAAM2B,EAAIpB,KAAK8E,KAAK9E,KAAK/B,OACzB+B,KAAKxB,OAASwB,KAAKZ,YAAYgC,EAAG,CAAC,EAAGpB,KAAK/B,MAC/C,GAAC,CAAAuB,IAAA,uBAAAC,MAED,WACI,IAAMvD,EAAK8D,KAAK9D,GAAG+D,QACf/D,GAAMA,EAAGgF,qBACThF,EAAGgF,qBACClB,KAAK1B,aACLmC,OAAOU,MAAMjF,GAGzB,GAAC,CAAAsD,IAAA,wBAAAC,MAED,SAAsBsF,GAClB,OACI/E,KAAK/B,MAAM+G,KAAOD,EAAUC,IAC5BC,KAAKC,UAAUlF,KAAK/B,MAAMkH,SACtBF,KAAKC,UAAUH,EAAUI,QAC7BF,KAAKC,UAAUlF,KAAK/B,MAAMmH,iBACtBH,KAAKC,UAAUH,EAAUK,cAErC,GAAC,CAAA5F,IAAA,mCAAAC,MAED,SAAiCsF,GAAW,IAAAM,EAAA,KAExC,GADkBrF,KAAK/B,MAAM+G,KAAOD,EAAUC,GAC9C,CAQA,IAAI5D,EAAIpB,KAAKxB,OAGTwB,KAAK/B,MAAM8B,UAAYgF,EAAUhF,SACjCC,KAAK/B,MAAM0B,SAAWoF,EAAUpF,QAChCK,KAAK/B,MAAMkC,+BACP4E,EAAU5E,8BACdH,KAAK/B,MAAMiC,+BACP6E,EAAU7E,+BAEdkB,EAAIA,EAAET,MAAK,kBAAM0E,EAAKP,KAAKC,EAAU,KAGzC/E,KAAKxB,OAASwB,KAAKZ,YAAYgC,EAAGpB,KAAK/B,MAAO8G,EAf9C,CAgBJ,GAAC,CAAAvF,IAAA,qBAAAC,MAED,SAAmB6F,GAAW,IAAAC,EAAA,KAEtBD,EAAUN,KAAOhF,KAAK/B,MAAM+G,IAC5BM,EAAUvF,UAAYC,KAAK/B,MAAM8B,UAEjCC,KAAKxB,OAASwB,KAAKxB,OAAOmC,MAAK,kBAAM4E,EAAKT,KAAKS,EAAKtH,MAAM,IAElE,GAAC,CAAAuB,IAAA,SAAAC,MAED,WACI,IAAA+F,EAA8CxF,KAAK/B,MAA5CwH,EAASD,EAATC,UAAWT,EAAEQ,EAAFR,GAAIG,EAAKK,EAALL,MAAOC,EAAaI,EAAbJ,cAE7B,OACI/G,IAAAA,cAAA,OACI2G,GAAIA,EACJxF,IAAKwF,EACL,uBACKI,GAAiBA,EAAcM,iBAAejK,EAEnDgK,UAAWA,EACXN,MAAOA,GAEP9G,IAAAA,cAACsH,EAAAA,GAAc,CACXC,cAAc,EACdC,aAAa,EACbC,YAAY,WACZC,eAAgB,CAACC,UAAU,GAC3BC,YAAa,GACbC,SAAUlG,KAAKd,cAEnBb,IAAAA,cAAA,OAAK8H,IAAKnG,KAAK9D,GAAIiJ,MAAO,CAAC3J,OAAQ,OAAQE,MAAO,UAG9D,I,uEAACmC,CAAA,CA7YY,CAASuI,EAAAA,WAgZ1BvI,EAAYwI,UAASC,EAAAA,EAAA,GACdC,EAAAA,IAAc,IACjB/E,YAAagF,IAAAA,QACTA,IAAAA,UAAoB,CAACA,IAAAA,MAAiBA,IAAAA,UAE1C9E,WAAY8E,IAAAA,QACRA,IAAAA,UAAoB,CAACA,IAAAA,MAAiBA,IAAAA,UAE1CvE,WAAYuE,IAAAA,KAAeC,aAG/B5I,EAAY6I,aAAYJ,EAAAA,EAAA,GACjBK,EAAAA,IAAiB,IACpBnF,YAAa,GACbE,WAAY,KAGhB,W,wBC/iBsE,IAAIkF,EAA8B,EAAQ,OAA4BvI,EAAM,EAAQ,OAASwI,EAAS,EAAQ,OAAa,SAASC,EAAsBC,GAAG,OAAOA,GAAc,iBAAJA,GAAc,YAAYA,EAAEA,EAAE,CAAC,QAAUA,EAAE,CAAC,SAASC,EAAkBD,GAAG,GAAGA,GAAGA,EAAEE,WAAW,OAAOF,EAAE,IAAIG,EAAEtE,OAAOuE,OAAO,MAAgN,OAAvMJ,GAAGnE,OAAOC,KAAKkE,GAAG3E,SAAQ,SAASgF,GAAG,GAAO,YAAJA,EAAc,CAAC,IAAIC,EAAEzE,OAAO0E,yBAAyBP,EAAEK,GAAGxE,OAAO2E,eAAeL,EAAEE,EAAEC,EAAEG,IAAIH,EAAE,CAACI,YAAW,EAAKD,IAAI,WAAW,OAAOT,EAAEK,EAAE,GAAI,CAAC,IAAGF,EAAW,QAAEH,EAASnE,OAAO8E,OAAOR,EAAE,CAAC,IAAIS,EAAoDb,EAAsBF,GAAmCgB,EAA8BZ,EAAkB3I,GAgBnvBwJ,EAAgB,SAASR,EAAGS,GAI5B,OAHAD,EAAgBjF,OAAOmF,gBAClB,CAAEC,UAAW,cAAgBxF,OAAS,SAAU6E,EAAGS,GAAKT,EAAEW,UAAYF,CAAG,GAC1E,SAAUT,EAAGS,GAAK,IAAK,IAAI1G,KAAK0G,EAAOlF,OAAOqF,UAAU3E,eAAelF,KAAK0J,EAAG1G,KAAIiG,EAAEjG,GAAK0G,EAAE1G,GAAI,EAC7FyG,EAAcR,EAAGS,EAC5B,EA+BC,IAAII,EAAuC,oBAAfC,WAA6BA,WAA+B,oBAAXC,OAAyBA,YAA2B,IAAX,EAAAC,EAAyB,EAAAA,EAAyB,oBAATC,KAAuBA,KAAO,CAAC,EA+B3LC,EALJ,SAAoB9I,GAClB,IAAIzC,SAAcyC,EAClB,OAAgB,MAATA,IAA0B,UAARzC,GAA4B,YAARA,EAC/C,EAMmCwL,EAFS,iBAAlBN,GAA8BA,GAAkBA,EAAetF,SAAWA,QAAUsF,EAK1GO,EAA0B,iBAARH,MAAoBA,MAAQA,KAAK1F,SAAWA,QAAU0F,KAGxEI,EAASF,GAAcC,GAAYE,SAAS,cAATA,GAEhBC,EAAXF,EAwBRG,EAAe,KAoBfC,EAAc,OAoBdC,EAhEQL,EA8DQM,OAEOC,EAAWF,EAGlCG,EAAgBtG,OAAOqF,UAGvB3E,EAAiB4F,EAAc5F,eAO/B6F,EAAyBD,EAAcE,SAGvCC,EAAmBJ,EAAWA,EAASK,iBAAc7N,EAsCrD8N,EAPc3G,OAAOqF,UAOcmB,SAcnCI,EA3CJ,SAAqB/J,GACnB,IAAIgK,EAAQnG,EAAelF,KAAKqB,EAAO4J,GACnCK,EAAMjK,EAAM4J,GAEhB,IACE5J,EAAM4J,QAAoB5N,EAC1B,IAAIkO,GAAW,CACjB,CAAE,MAAO5C,GAAI,CAEb,IAAI6C,EAAST,EAAuB/K,KAAKqB,GAQzC,OAPIkK,IACEF,EACFhK,EAAM4J,GAAoBK,SAEnBjK,EAAM4J,IAGVO,CACT,EAiCIC,EATgDd,IASfO,iBAAc7N,EA+CXqO,EAtCxC,SAAsBrK,GACpB,OAAa,MAATA,OACehE,IAAVgE,EAdQ,qBADL,gBAiBJoK,GAAkBA,KAAkBjH,OAAOnD,GAC/C+J,EAAU/J,GA3BhB,SAA0BA,GACxB,OAAO8J,EAAqBnL,KAAKqB,EACnC,CA0BMsK,CAAetK,EACrB,EA2DgCuK,EAlKhC,SAAoBC,GAClB,OAAOA,EACHA,EAAOC,MAAM,EArBnB,SAA2BD,GAGzB,IAFA,IAAIE,EAAQF,EAAOvM,OAEZyM,KAAWtB,EAAauB,KAAKH,EAAOI,OAAOF,MAClD,OAAOA,CACT,CAgBsBG,CAAgBL,GAAU,GAAGM,QAAQzB,EAAa,IAClEmB,CACN,EA+JIO,EAAajC,EAObkC,EAAa,qBAGbC,EAAa,aAGbC,EAAY,cAGZC,EAAeC,SA8CaC,EAAavC,EACzCwC,EArQQ,WACV,OAAOnC,EAAOoC,KAAKD,KACrB,EAoQIE,EAvBJ,SAAoBxL,GAClB,GAAoB,iBAATA,EACT,OAAOA,EAET,GAnDF,SAAoBA,GAClB,MAAuB,iBAATA,GA5BhB,SAAwBA,GACtB,OAAgB,MAATA,GAAiC,iBAATA,CACjC,CA2BKyL,CAAazL,IArBF,mBAqBYqK,EAAWrK,EACvC,CAgDM0L,CAAS1L,GACX,OA1CM,IA4CR,GAAI+K,EAAW/K,GAAQ,CACrB,IAAI2L,EAAgC,mBAAjB3L,EAAM4L,QAAwB5L,EAAM4L,UAAY5L,EACnEA,EAAQ+K,EAAWY,GAAUA,EAAQ,GAAMA,CAC7C,CACA,GAAoB,iBAAT3L,EACT,OAAiB,IAAVA,EAAcA,GAASA,EAEhCA,EAAQuK,EAASvK,GACjB,IAAI6L,EAAWZ,EAAWN,KAAK3K,GAC/B,OAAQ6L,GAAYX,EAAUP,KAAK3K,GAC/BmL,EAAanL,EAAMyK,MAAM,GAAIoB,EAAW,EAAI,GAC3Cb,EAAWL,KAAK3K,GAvDb,KAuD6BA,CACvC,EAUI8L,EAAYC,KAAKC,IACjBC,EAAYF,KAAKG,IAqLjBC,EA7HJ,SAAoBC,EAAMC,EAAMC,GAC9B,IAAIC,EACAC,EACAC,EACAtC,EACAuC,EACAC,EACAC,EAAiB,EACjBC,GAAU,EACVC,GAAS,EACTvG,GAAW,EAEf,GAAmB,mBAAR6F,EACT,MAAM,IAAIW,UAzEU,uBAmFtB,SAASC,EAAWC,GAClB,IAAIC,EAAOX,EACPY,EAAUX,EAKd,OAHAD,EAAWC,OAAWxQ,EACtB4Q,EAAiBK,EACjB9C,EAASiC,EAAKgB,MAAMD,EAASD,EAE/B,CAqBA,SAASG,EAAaJ,GACpB,IAAIK,EAAoBL,EAAON,EAM/B,YAAyB3Q,IAAjB2Q,GAA+BW,GAAqBjB,GACzDiB,EAAoB,GAAOR,GANJG,EAAOL,GAM8BH,CACjE,CAEA,SAASc,IACP,IAAIN,EAAO3B,IACX,GAAI+B,EAAaJ,GACf,OAAOO,EAAaP,GAGtBP,EAAUe,WAAWF,EA3BvB,SAAuBN,GACrB,IAEIS,EAAcrB,GAFMY,EAAON,GAI/B,OAAOG,EACHb,EAAUyB,EAAajB,GAJDQ,EAAOL,IAK7Bc,CACN,CAmBqCC,CAAcV,GACnD,CAEA,SAASO,EAAaP,GAKpB,OAJAP,OAAU1Q,EAINuK,GAAYgG,EACPS,EAAWC,IAEpBV,EAAWC,OAAWxQ,EACfmO,EACT,CAcA,SAASyD,IACP,IAAIX,EAAO3B,IACPuC,EAAaR,EAAaJ,GAM9B,GAJAV,EAAWvI,UACXwI,EAAWjM,KACXoM,EAAeM,EAEXY,EAAY,CACd,QAAgB7R,IAAZ0Q,EACF,OAzEN,SAAqBO,GAMnB,OAJAL,EAAiBK,EAEjBP,EAAUe,WAAWF,EAAclB,GAE5BQ,EAAUG,EAAWC,GAAQ9C,CACtC,CAkEa2D,CAAYnB,GAErB,GAAIG,EAIF,OAFAiB,aAAarB,GACbA,EAAUe,WAAWF,EAAclB,GAC5BW,EAAWL,EAEtB,CAIA,YAHgB3Q,IAAZ0Q,IACFA,EAAUe,WAAWF,EAAclB,IAE9BlC,CACT,CAGA,OA3GAkC,EAAOb,EAASa,IAAS,EACrBhB,EAAWiB,KACbO,IAAYP,EAAQO,QAEpBJ,GADAK,EAAS,YAAaR,GACHR,EAAUN,EAASc,EAAQG,UAAY,EAAGJ,GAAQI,EACrElG,EAAW,aAAc+F,IAAYA,EAAQ/F,SAAWA,GAoG1DqH,EAAUI,OApCV,gBACkBhS,IAAZ0Q,GACFqB,aAAarB,GAEfE,EAAiB,EACjBL,EAAWI,EAAeH,EAAWE,OAAU1Q,CACjD,EA+BA4R,EAAUK,MA7BV,WACE,YAAmBjS,IAAZ0Q,EAAwBvC,EAASqD,EAAalC,IACvD,EA4BOsC,CACT,EAEgCM,EAAW/B,EACvCgC,EAAWrF,EAmEesF,EAAqB,SAAUC,EAAgBhI,EAAaG,EAAaF,GACnG,OAAQD,GACJ,IAAK,WACD,OAAO8F,EAAWkC,EAAgB7H,EAAaF,GACnD,IAAK,WACD,OAvBZ,SAAkB8F,EAAMC,EAAMC,GAC5B,IAAIO,GAAU,EACVtG,GAAW,EAEf,GAAmB,mBAAR6F,EACT,MAAM,IAAIW,UAnDQ,uBAyDpB,OAJIoB,EAAS7B,KACXO,EAAU,YAAaP,IAAYA,EAAQO,QAAUA,EACrDtG,EAAW,aAAc+F,IAAYA,EAAQ/F,SAAWA,GAEnD2H,EAAS9B,EAAMC,EAAM,CAC1B,QAAWQ,EACX,QAAWR,EACX,SAAY9F,GAEhB,CAOmB+H,CAAWD,EAAgB7H,EAAaF,GACnD,QACI,OAAO+H,EAEnB,EACIE,EAAa,SAAUC,GAAM,MAAqB,mBAAPA,CAAmB,EAC9DC,EAAQ,WAAc,MAAyB,oBAAX9F,MAAwB,EAC5D+F,EAAe,SAAUC,GAAW,OAAOA,aAAmBC,SAAWD,aAAmBE,YAAc,EAmBxG3I,EAAgC,SAAU3H,GAE5C,SAAS2H,EAAe1H,GACpB,IAAI4D,EAAQ7D,EAAOI,KAAK4B,KAAM/B,IAAU+B,KACxC6B,EAAM0M,cAAgB,WACd1M,EAAM2M,eAAiB3M,EAAM2M,cAAcf,SAE3C5L,EAAM2M,cAAcf,SACpB5L,EAAM2M,cAAgB,KAE9B,EACA3M,EAAM4M,eAAiB,WACnB,IAAIC,EAAK7M,EAAM5D,MAAO0Q,EAAYD,EAAGC,UAAWC,EAAkBF,EAAGE,gBACrE,IAAIV,IAAJ,CAGIS,GAAaA,EAAU1O,UACvB4B,EAAM8M,UAAU1O,QAAU0O,EAAU1O,SAExC,IAAImO,EAAUvM,EAAMgN,aACfT,IAIDvM,EAAMiN,mBAAqBjN,EAAMiN,oBAAsBV,IAI3DvM,EAAMiN,kBAAoBV,EAC1BvM,EAAMkN,eAAeC,QAAQZ,EAASQ,IAdtC,CAeJ,EACA/M,EAAMgN,WAAa,WACf,IAAIH,EAAK7M,EAAM5D,MAAOgR,EAAgBP,EAAGO,cAAeC,EAAcR,EAAGQ,YACzE,GAAIhB,IACA,OAAO,KAEX,GAAIe,EACA,OAAOE,SAASF,cAAcA,GAElC,GAAIC,GAAef,EAAae,GAC5B,OAAOA,EAEX,GAAIrN,EAAM8M,WAAaR,EAAatM,EAAM8M,UAAU1O,SAChD,OAAO4B,EAAM8M,UAAU1O,QAG3B,IAAImP,EAAiBvI,EAASwI,YAAYxN,GAC1C,IAAKuN,EACD,OAAO,KAEX,OADiBvN,EAAMyN,iBAEnB,IAAK,aAEL,IAAK,gBAEL,IAAK,QAEL,IAAK,aACD,OAAOF,EACX,QACI,OAAOA,EAAeG,cAElC,EACA1N,EAAM2N,oBAAsB,SAAUC,GAClC,IAAIf,EAAK7M,EAAM5D,MAAOyR,EAAKhB,EAAG7I,YAAaA,OAAqB,IAAP6J,GAAuBA,EAAIC,EAAKjB,EAAG9I,aAAcA,OAAsB,IAAP+J,GAAuBA,EAAIzJ,EAAWwI,EAAGxI,SAClK,GAAKL,GAAgBD,EAArB,CAEA,IAAIgK,EArFK,SAAU1J,EAAU2J,EAAShK,EAAaD,GAC3D,OAAO,SAAU8I,GACb,IAAIhT,EAAQgT,EAAGhT,MAAOF,EAASkT,EAAGlT,OAClCqU,GAAQ,SAAUC,GACd,OAAIA,EAAKpU,QAAUA,GAASoU,EAAKtU,SAAWA,GAIvCsU,EAAKpU,QAAUA,IAAUkK,GAAkBkK,EAAKtU,SAAWA,IAAWqK,EAFhEiK,GAMP5J,GAAY8H,EAAW9H,IACvBA,EAASxK,EAAOF,GAEb,CAAEE,MAAOA,EAAOF,OAAQA,GACnC,GACJ,CACJ,CAmE+BuU,CAAe7J,EAAUrE,EAAM0B,SAAS3E,KAAKiD,GAAQgE,EAAaD,GACrF6J,EAAQrN,SAAQ,SAAU4N,GACtB,IAAItB,EAAMsB,GAASA,EAAMC,aAAgB,CAAC,EAAGvU,EAAQgT,EAAGhT,MAAOF,EAASkT,EAAGlT,QACtDqG,EAAMqO,cAAgBhC,KAEvC0B,EAAa,CAAElU,MAAOA,EAAOF,OAAQA,IAEzCqG,EAAMqO,aAAc,CACxB,GATU,CAUd,EACArO,EAAMyN,cAAgB,WAClB,IAAIZ,EAAK7M,EAAM5D,MAAOkS,EAASzB,EAAGyB,OAAQC,EAAW1B,EAAG0B,SACxD,OAAIpC,EAAWmC,GAEJ,aAEPnC,EAAWoC,GACJ,gBAEP/R,EAAMgS,eAAeD,GACd,QAEP5N,MAAMC,QAAQ2N,GAEP,aAGJ,QACX,EACA,IAAIF,EAAcjS,EAAMiS,YAAapK,EAAc7H,EAAM6H,YAAa4I,EAAKzQ,EAAMgI,YAAaA,OAAqB,IAAPyI,EAAgB,IAAOA,EAAI3I,EAAiB9H,EAAM8H,eAQ9J,OAPAlE,EAAMxC,MAAQ,CACV3D,WAAOD,EACPD,YAAQC,GAEZoG,EAAMqO,YAAcA,EACpBrO,EAAM8M,UAAYtQ,EAAMiS,YACxBzO,EAAMiN,kBAAoB,KACtBZ,MAGJrM,EAAM2M,cAAgBX,EAAmBhM,EAAM2N,oBAAqB1J,EAAaG,EAAaF,GAC9FlE,EAAMkN,eAAiB,IAAIpH,EAAgD,QAAE9F,EAAM2M,gBAHxE3M,CAKf,CA2CA,OAxxBJ,SAAmBwF,EAAGS,GAClB,GAAiB,mBAANA,GAA0B,OAANA,EAC3B,MAAM,IAAI0E,UAAU,uBAAyB+D,OAAOzI,GAAK,iCAE7D,SAAS0I,IAAOxQ,KAAKyQ,YAAcpJ,CAAG,CADtCQ,EAAcR,EAAGS,GAEjBT,EAAEY,UAAkB,OAANH,EAAalF,OAAOuE,OAAOW,IAAM0I,EAAGvI,UAAYH,EAAEG,UAAW,IAAIuI,EACnF,CA0nBIE,CAAU/K,EAAgB3H,GA8G1B2H,EAAesC,UAAU0I,kBAAoB,WACzC3Q,KAAKyO,gBACT,EACA9I,EAAesC,UAAU2I,mBAAqB,WAC1C5Q,KAAKyO,gBACT,EACA9I,EAAesC,UAAU4I,qBAAuB,WACxC3C,MAGJlO,KAAK+O,eAAe+B,aACpB9Q,KAAKuO,gBACT,EACA5I,EAAesC,UAAUkI,OAAS,WAC9B,IAIIY,EAJArC,EAAK1O,KAAK/B,MAAOkS,EAASzB,EAAGyB,OAAQC,EAAW1B,EAAG0B,SAAUV,EAAKhB,EAAGsC,SAAUC,OAAoB,IAAPvB,EAAgB,MAAQA,EACpHC,EAAK3P,KAAKX,MACV6R,EAAa,CAAExV,MADUiU,EAAGjU,MACCF,OADemU,EAAGnU,OACFmT,UAAW3O,KAAK2O,WAGjE,OAFiB3O,KAAKsP,iBAGlB,IAAK,aACD,OAAOa,GAAUA,EAAOe,GAC5B,IAAK,gBAED,OADAH,EAAgBX,GACKc,GACzB,IAAK,QAGD,IADAH,EAAgBX,GACEpT,MAAsC,iBAAvB+T,EAAc/T,KAAmB,CAExC,IAAImU,EAzvB9C,SAAgBC,EAAGrK,GACf,IAAIsK,EAAI,CAAC,EACT,IAAK,IAAIjQ,KAAKgQ,EAAOxO,OAAOqF,UAAU3E,eAAelF,KAAKgT,EAAGhQ,IAAM2F,EAAEuK,QAAQlQ,GAAK,IAC9EiQ,EAAEjQ,GAAKgQ,EAAEhQ,IACb,GAAS,MAALgQ,GAAqD,mBAAjCxO,OAAO2O,sBACtB,KAAI3U,EAAI,EAAb,IAAgBwE,EAAIwB,OAAO2O,sBAAsBH,GAAIxU,EAAIwE,EAAE1D,OAAQd,IAC3DmK,EAAEuK,QAAQlQ,EAAExE,IAAM,GAAKgG,OAAOqF,UAAUuJ,qBAAqBpT,KAAKgT,EAAGhQ,EAAExE,MACvEyU,EAAEjQ,EAAExE,IAAMwU,EAAEhQ,EAAExE,IAF4B,CAItD,OAAOyU,CACX,CA+uB4DI,CAAOP,EAAY,CAAC,cAC5D,OAAO7S,EAAMqT,aAAaX,EAAeI,EAC7C,CAEA,OAAO9S,EAAMqT,aAAaX,EAAeG,GAC7C,IAAK,aAED,OADAH,EAAgBX,GACK5S,KAAI,SAAUmU,GAAM,QAASA,GAAMtT,EAAMqT,aAAaC,EAAIT,EAAa,IAChG,QACI,OAAOtJ,EAAiBgK,cAAcX,EAAY,MAE9D,EACOtL,CACX,CA1JqC,CA0JnCtH,EAAMwT,eAuBiB3D,IAAU7P,EAAMyT,UAAYzT,EAAM0T,gBA4C1DC,EAAA,GAAmBrM,C", "sources": ["webpack:///./src/fragments/Graph.react.js", "webpack:///./node_modules/react-resize-detector/build/withPolyfill.js"], "sourcesContent": ["import lazyLoadMathJax from '../utils/LazyLoader/mathjax';\nimport React, {Component} from 'react';\n// /build/withPolyfill for IE11 support - https://github.com/maslianok/react-resize-detector/issues/144\nimport ResizeDetector from 'react-resize-detector/build/withPolyfill';\nimport {\n    equals,\n    filter,\n    has,\n    includes,\n    isNil,\n    mergeDeepRight,\n    omit,\n    type,\n} from 'ramda';\nimport PropTypes from 'prop-types';\nimport {graphPropTypes, graphDefaultProps} from '../components/Graph.react';\n/* global Plotly:true */\n\n/**\n * `autosize: true` causes Plotly.js to conform to the parent element size.\n * This is necessary for `dcc.Graph` call to `Plotly.Plots.resize(target)` to do something.\n *\n * Users can override this value for specific use-cases by explicitly passing `autoresize: true`\n * if `responsive` is not set to True.\n */\nconst RESPONSIVE_LAYOUT = {\n    autosize: true,\n    height: undefined,\n    width: undefined,\n};\n\nconst AUTO_LAYOUT = {};\n\nconst UNRESPONSIVE_LAYOUT = {\n    autosize: false,\n};\n\n/**\n * `responsive: true` causes Plotly.js to resize the graph on `window.resize`.\n * This is necessary for `dcc.Graph` call to `Plotly.Plots.resize(target)` to do something.\n *\n * Users can override this value for specific use-cases by explicitly passing `responsive: false`\n * if `responsive` is not set to True.\n */\nconst RESPONSIVE_CONFIG = {\n    responsive: true,\n};\n\nconst AUTO_CONFIG = {};\n\nconst UNRESPONSIVE_CONFIG = {\n    responsive: false,\n};\n\nconst filterEventData = (gd, eventData, event) => {\n    let filteredEventData;\n    if (includes(event, ['click', 'hover', 'selected'])) {\n        const points = [];\n\n        if (isNil(eventData)) {\n            return null;\n        }\n\n        /*\n         * remove `data`, `layout`, `xaxis`, etc\n         * objects from the event data since they're so big\n         * and cause JSON stringify ciricular structure errors.\n         *\n         * also, pull down the `customdata` point from the data array\n         * into the event object\n         */\n        const data = gd.data;\n\n        for (let i = 0; i < eventData.points.length; i++) {\n            const fullPoint = eventData.points[i];\n            const pointData = filter(function (o) {\n                return !includes(type(o), ['Object', 'Array']);\n            }, fullPoint);\n\n            // permit a bounding box to pass through, if present\n            if (has('bbox', fullPoint)) {\n                pointData.bbox = fullPoint.bbox;\n            }\n\n            if (\n                has('curveNumber', fullPoint) &&\n                has('customdata', data[pointData.curveNumber])\n            ) {\n                if (has('pointNumber', fullPoint)) {\n                    if (typeof fullPoint.pointNumber === 'number') {\n                        pointData.customdata =\n                            data[pointData.curveNumber].customdata[\n                                fullPoint.pointNumber\n                            ];\n                    } else if (\n                        !fullPoint.pointNumber &&\n                        fullPoint.data.mode.includes('lines')\n                    ) {\n                        pointData.customdata =\n                            data[pointData.curveNumber].customdata;\n                    }\n                } else if (has('pointNumbers', fullPoint)) {\n                    pointData.customdata = fullPoint.pointNumbers.map(function (\n                        point\n                    ) {\n                        return data[pointData.curveNumber].customdata[point];\n                    });\n                }\n            }\n\n            // specific to histogram. see https://github.com/plotly/plotly.js/pull/2113/\n            if (has('pointNumbers', fullPoint)) {\n                pointData.pointNumbers = fullPoint.pointNumbers;\n            }\n\n            points[i] = pointData;\n        }\n        filteredEventData = {points};\n    } else if (event === 'relayout' || event === 'restyle') {\n        /*\n         * relayout shouldn't include any big objects\n         * it will usually just contain the ranges of the axes like\n         * \"xaxis.range[0]\": 0.7715822247381828,\n         * \"xaxis.range[1]\": 3.0095292008680063`\n         */\n        filteredEventData = eventData;\n    }\n    if (has('range', eventData)) {\n        filteredEventData.range = eventData.range;\n    }\n    if (has('lassoPoints', eventData)) {\n        filteredEventData.lassoPoints = eventData.lassoPoints;\n    }\n    return filteredEventData;\n};\n\n/**\n * Graph can be used to render any plotly.js-powered data visualization.\n *\n * You can define callbacks based on user interaction with Graphs such as\n * hovering, clicking or selecting\n */\nclass PlotlyGraph extends Component {\n    constructor(props) {\n        super(props);\n        this.gd = React.createRef();\n        this._hasPlotted = false;\n        this._prevGd = null;\n        this._queue = Promise.resolve();\n\n        this.bindEvents = this.bindEvents.bind(this);\n        this.getConfig = this.getConfig.bind(this);\n        this.getConfigOverride = this.getConfigOverride.bind(this);\n        this.getLayout = this.getLayout.bind(this);\n        this.getLayoutOverride = this.getLayoutOverride.bind(this);\n        this.graphResize = this.graphResize.bind(this);\n        this.isResponsive = this.isResponsive.bind(this);\n        this.amendTraces = this.amendTraces.bind(this);\n\n        this.state = {override: {}, originals: {}};\n    }\n\n    plot(props) {\n        let {figure, config} = props;\n        const {animate, animation_options, responsive, mathjax} = props;\n\n        const gd = this.gd.current;\n        figure = props._dashprivate_transformFigure(figure, gd);\n        config = props._dashprivate_transformConfig(config, gd);\n\n        const configClone = this.getConfig(config, responsive);\n        // add typesetMath | not exposed to the dash API\n        configClone.typesetMath = mathjax;\n\n        const figureClone = {\n            data: figure.data,\n            layout: this.getLayout(figure.layout, responsive),\n            frames: figure.frames,\n            config: configClone,\n        };\n\n        if (\n            animate &&\n            this._hasPlotted &&\n            figure.data.length === gd.data.length\n        ) {\n            // in case we've have figure frames,\n            // we need to recreate frames before animation\n            if (figure.frames) {\n                return Plotly.deleteFrames(gd)\n                    .then(() => Plotly.addFrames(gd, figure.frames))\n                    .then(() =>\n                        Plotly.animate(gd, figureClone, animation_options)\n                    );\n            }\n            return Plotly.animate(gd, figureClone, animation_options);\n        }\n\n        gd.classList.add('dash-graph--pending');\n\n        return lazyLoadMathJax(mathjax)\n            .then(() => {\n                const gd = this.gd.current;\n                return gd && Plotly.react(gd, figureClone);\n            })\n            .then(() => {\n                const gd = this.gd.current;\n\n                // double-check gd hasn't been unmounted\n                if (!gd) {\n                    return;\n                }\n\n                gd.classList.remove('dash-graph--pending');\n\n                // in case we've made a new DOM element, transfer events\n                if (this._hasPlotted && gd !== this._prevGd) {\n                    if (this._prevGd && this._prevGd.removeAllListeners) {\n                        this._prevGd.removeAllListeners();\n                        Plotly.purge(this._prevGd);\n                    }\n                    this._hasPlotted = false;\n                }\n\n                if (!this._hasPlotted) {\n                    this.bindEvents();\n                    this.graphResize(true);\n                    this._hasPlotted = true;\n                    this._prevGd = gd;\n                }\n            });\n    }\n\n    amendTraces(p, oldProps, newProps) {\n        const {prependData: oldPrepend, extendData: oldExtend} = oldProps;\n        const {prependData: newPrepend, extendData: newExtend} = newProps;\n        const _this = this;\n\n        function mergeTraces(props, dataKey, plotlyFnKey) {\n            const clearState = props.clearState;\n            const dataArray = props[dataKey];\n\n            let _p = Promise.resolve();\n\n            dataArray.forEach(data => {\n                let updateData, traceIndices, maxPoints;\n                if (Array.isArray(data) && typeof data[0] === 'object') {\n                    [updateData, traceIndices, maxPoints] = data;\n                } else {\n                    updateData = data;\n                }\n\n                if (!traceIndices) {\n                    function getFirstProp(data) {\n                        return data[Object.keys(data)[0]];\n                    }\n\n                    function generateIndices(data) {\n                        return Array.from(\n                            Array(getFirstProp(data).length).keys()\n                        );\n                    }\n                    traceIndices = generateIndices(updateData);\n                }\n\n                _p = _p.then(() => {\n                    const gd = _this.gd.current;\n                    return (\n                        gd &&\n                        Plotly[plotlyFnKey](\n                            gd,\n                            updateData,\n                            traceIndices,\n                            maxPoints\n                        )\n                    );\n                });\n            });\n\n            return _p.then(() => clearState(dataKey));\n        }\n\n        let modified = false;\n\n        if (newPrepend?.length && oldPrepend !== newPrepend) {\n            modified = true;\n            p = p.then(() =>\n                mergeTraces(newProps, 'prependData', 'prependTraces')\n            );\n        }\n\n        if (newExtend?.length && oldExtend !== newExtend) {\n            modified = true;\n            p = p.then(() =>\n                mergeTraces(newProps, 'extendData', 'extendTraces')\n            );\n        }\n\n        if (modified) {\n            p = p.then(() =>\n                newProps._dashprivate_onFigureModified(newProps.figure)\n            );\n        }\n\n        return p;\n    }\n\n    getConfig(config, responsive) {\n        return mergeDeepRight(config, this.getConfigOverride(responsive));\n    }\n\n    getLayout(layout, responsive) {\n        if (!layout) {\n            return layout;\n        }\n        const override = this.getLayoutOverride(responsive);\n        const {override: prev_override, originals: prev_originals} = this.state;\n        // Store the original data that we're about to override\n        const originals = {};\n        for (const key in override) {\n            if (layout[key] !== prev_override[key]) {\n                originals[key] = layout[key];\n            } else if (prev_originals.hasOwnProperty(key)) {\n                originals[key] = prev_originals[key];\n            }\n        }\n        this.setState({override, originals});\n        // Undo the previous override, but only for keys that the user did not change\n        for (const key in prev_originals) {\n            if (layout[key] === prev_override[key]) {\n                layout[key] = prev_originals[key];\n            }\n        }\n        // Apply the current override\n        for (const key in override) {\n            layout[key] = override[key];\n        }\n        return layout; // not really a clone\n    }\n\n    getConfigOverride(responsive) {\n        switch (responsive) {\n            case false:\n                return UNRESPONSIVE_CONFIG;\n            case true:\n                return RESPONSIVE_CONFIG;\n            default:\n                return AUTO_CONFIG;\n        }\n    }\n\n    getLayoutOverride(responsive) {\n        switch (responsive) {\n            case false:\n                return UNRESPONSIVE_LAYOUT;\n            case true:\n                return RESPONSIVE_LAYOUT;\n            default:\n                return AUTO_LAYOUT;\n        }\n    }\n\n    isResponsive(props) {\n        const {config, figure, responsive} = props;\n\n        if (type(responsive) === 'Boolean') {\n            return responsive;\n        }\n\n        return Boolean(\n            config.responsive &&\n                (!figure.layout ||\n                    ((figure.layout.autosize ||\n                        isNil(figure.layout.autosize)) &&\n                        (isNil(figure.layout.height) ||\n                            isNil(figure.layout.width))))\n        );\n    }\n\n    graphResize(force = false) {\n        if (!force && !this.isResponsive(this.props)) {\n            return;\n        }\n\n        const gd = this.gd.current;\n        if (!gd) {\n            return;\n        }\n\n        gd.classList.add('dash-graph--pending');\n\n        Plotly.Plots.resize(gd)\n            .catch(() => {})\n            .finally(() => gd.classList.remove('dash-graph--pending'));\n    }\n\n    bindEvents() {\n        const {\n            setProps,\n            clear_on_unhover,\n            relayoutData,\n            restyleData,\n            hoverData,\n            selectedData,\n        } = this.props;\n\n        const gd = this.gd.current;\n\n        gd.on('plotly_click', eventData => {\n            const clickData = filterEventData(gd, eventData, 'click');\n            if (!isNil(clickData)) {\n                setProps({clickData});\n            }\n        });\n        gd.on('plotly_clickannotation', eventData => {\n            const clickAnnotationData = omit(\n                ['event', 'fullAnnotation'],\n                eventData\n            );\n            setProps({clickAnnotationData});\n        });\n        gd.on('plotly_hover', eventData => {\n            const hover = filterEventData(gd, eventData, 'hover');\n            if (!isNil(hover) && !equals(hover, hoverData)) {\n                setProps({hoverData: hover});\n            }\n        });\n        gd.on('plotly_selected', eventData => {\n            const selected = filterEventData(gd, eventData, 'selected');\n            if (!isNil(selected) && !equals(selected, selectedData)) {\n                setProps({selectedData: selected});\n            }\n        });\n        gd.on('plotly_deselect', () => {\n            setProps({selectedData: null});\n        });\n        gd.on('plotly_relayout', eventData => {\n            const relayout = filterEventData(gd, eventData, 'relayout');\n            if (!isNil(relayout) && !equals(relayout, relayoutData)) {\n                setProps({relayoutData: relayout});\n            }\n        });\n        gd.on('plotly_restyle', eventData => {\n            const restyle = filterEventData(gd, eventData, 'restyle');\n            if (!isNil(restyle) && !equals(restyle, restyleData)) {\n                setProps({restyleData: restyle});\n            }\n        });\n        gd.on('plotly_unhover', () => {\n            if (clear_on_unhover) {\n                setProps({hoverData: null});\n            }\n        });\n    }\n\n    componentDidMount() {\n        const p = this.plot(this.props);\n        this._queue = this.amendTraces(p, {}, this.props);\n    }\n\n    componentWillUnmount() {\n        const gd = this.gd.current;\n        if (gd && gd.removeAllListeners) {\n            gd.removeAllListeners();\n            if (this._hasPlotted) {\n                Plotly.purge(gd);\n            }\n        }\n    }\n\n    shouldComponentUpdate(nextProps) {\n        return (\n            this.props.id !== nextProps.id ||\n            JSON.stringify(this.props.style) !==\n                JSON.stringify(nextProps.style) ||\n            JSON.stringify(this.props.loading_state) !==\n                JSON.stringify(nextProps.loading_state)\n        );\n    }\n\n    UNSAFE_componentWillReceiveProps(nextProps) {\n        const idChanged = this.props.id !== nextProps.id;\n        if (idChanged) {\n            /*\n             * then the dom needs to get re-rendered with a new ID.\n             * the graph will get updated in componentDidUpdate\n             */\n            return;\n        }\n\n        let p = this._queue;\n\n        if (\n            this.props.mathjax !== nextProps.mathjax ||\n            this.props.figure !== nextProps.figure ||\n            this.props._dashprivate_transformConfig !==\n                nextProps._dashprivate_transformConfig ||\n            this.props._dashprivate_transformFigure !==\n                nextProps._dashprivate_transformFigure\n        ) {\n            p = p.then(() => this.plot(nextProps));\n        }\n\n        this._queue = this.amendTraces(p, this.props, nextProps);\n    }\n\n    componentDidUpdate(prevProps) {\n        if (\n            prevProps.id !== this.props.id ||\n            prevProps.mathjax !== this.props.mathjax\n        ) {\n            this._queue = this._queue.then(() => this.plot(this.props));\n        }\n    }\n\n    render() {\n        const {className, id, style, loading_state} = this.props;\n\n        return (\n            <div\n                id={id}\n                key={id}\n                data-dash-is-loading={\n                    (loading_state && loading_state.is_loading) || undefined\n                }\n                className={className}\n                style={style}\n            >\n                <ResizeDetector\n                    handleHeight={true}\n                    handleWidth={true}\n                    refreshMode=\"debounce\"\n                    refreshOptions={{trailing: true}}\n                    refreshRate={50}\n                    onResize={this.graphResize}\n                />\n                <div ref={this.gd} style={{height: '100%', width: '100%'}} />\n            </div>\n        );\n    }\n}\n\nPlotlyGraph.propTypes = {\n    ...graphPropTypes,\n    prependData: PropTypes.arrayOf(\n        PropTypes.oneOfType([PropTypes.array, PropTypes.object])\n    ),\n    extendData: PropTypes.arrayOf(\n        PropTypes.oneOfType([PropTypes.array, PropTypes.object])\n    ),\n    clearState: PropTypes.func.isRequired,\n};\n\nPlotlyGraph.defaultProps = {\n    ...graphDefaultProps,\n    prependData: [],\n    extendData: [],\n};\n\nexport default PlotlyGraph;\n", "'use strict';Object.defineProperty(exports,'__esModule',{value:true});var $inject_window_ResizeObserver=require('resize-observer-polyfill'),React=require('react'),reactDom=require('react-dom');function _interopDefaultLegacy(e){return e&&typeof e==='object'&&'default'in e?e:{'default':e}}function _interopNamespace(e){if(e&&e.__esModule)return e;var n=Object.create(null);if(e){Object.keys(e).forEach(function(k){if(k!=='default'){var d=Object.getOwnPropertyDescriptor(e,k);Object.defineProperty(n,k,d.get?d:{enumerable:true,get:function(){return e[k]}});}})}n[\"default\"]=e;return Object.freeze(n)}var $inject_window_ResizeObserver__default=/*#__PURE__*/_interopDefaultLegacy($inject_window_ResizeObserver);var React__namespace=/*#__PURE__*/_interopNamespace(React);/*! *****************************************************************************\r\nCopyright (c) Microsoft Corporation.\r\n\r\nPermission to use, copy, modify, and/or distribute this software for any\r\npurpose with or without fee is hereby granted.\r\n\r\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\r\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\r\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\r\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\r\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\r\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\r\nPERFORMANCE OF THIS SOFTWARE.\r\n***************************************************************************** */\r\n/* global Reflect, Promise */\r\n\r\nvar extendStatics = function(d, b) {\r\n    extendStatics = Object.setPrototypeOf ||\r\n        ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\r\n        function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\r\n    return extendStatics(d, b);\r\n};\r\n\r\nfunction __extends(d, b) {\r\n    if (typeof b !== \"function\" && b !== null)\r\n        throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\r\n    extendStatics(d, b);\r\n    function __() { this.constructor = d; }\r\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\r\n}\r\n\r\nvar __assign = function() {\r\n    __assign = Object.assign || function __assign(t) {\r\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\r\n            s = arguments[i];\r\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\r\n        }\r\n        return t;\r\n    };\r\n    return __assign.apply(this, arguments);\r\n};\r\n\r\nfunction __rest(s, e) {\r\n    var t = {};\r\n    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\r\n        t[p] = s[p];\r\n    if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\r\n        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\r\n            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\r\n                t[p[i]] = s[p[i]];\r\n        }\r\n    return t;\r\n}var commonjsGlobal = typeof globalThis !== 'undefined' ? globalThis : typeof window !== 'undefined' ? window : typeof global !== 'undefined' ? global : typeof self !== 'undefined' ? self : {};/**\n * Checks if `value` is the\n * [language type](http://www.ecma-international.org/ecma-262/7.0/#sec-ecmascript-language-types)\n * of `Object`. (e.g. arrays, functions, objects, regexes, `new Number(0)`, and `new String('')`)\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is an object, else `false`.\n * @example\n *\n * _.isObject({});\n * // => true\n *\n * _.isObject([1, 2, 3]);\n * // => true\n *\n * _.isObject(_.noop);\n * // => true\n *\n * _.isObject(null);\n * // => false\n */\n\nfunction isObject$3(value) {\n  var type = typeof value;\n  return value != null && (type == 'object' || type == 'function');\n}\n\nvar isObject_1 = isObject$3;/** Detect free variable `global` from Node.js. */\n\nvar freeGlobal$1 = typeof commonjsGlobal == 'object' && commonjsGlobal && commonjsGlobal.Object === Object && commonjsGlobal;\n\nvar _freeGlobal = freeGlobal$1;var freeGlobal = _freeGlobal;\n\n/** Detect free variable `self`. */\nvar freeSelf = typeof self == 'object' && self && self.Object === Object && self;\n\n/** Used as a reference to the global object. */\nvar root$2 = freeGlobal || freeSelf || Function('return this')();\n\nvar _root = root$2;var root$1 = _root;\n\n/**\n * Gets the timestamp of the number of milliseconds that have elapsed since\n * the Unix epoch (1 January 1970 00:00:00 UTC).\n *\n * @static\n * @memberOf _\n * @since 2.4.0\n * @category Date\n * @returns {number} Returns the timestamp.\n * @example\n *\n * _.defer(function(stamp) {\n *   console.log(_.now() - stamp);\n * }, _.now());\n * // => Logs the number of milliseconds it took for the deferred invocation.\n */\nvar now$1 = function() {\n  return root$1.Date.now();\n};\n\nvar now_1 = now$1;/** Used to match a single whitespace character. */\n\nvar reWhitespace = /\\s/;\n\n/**\n * Used by `_.trim` and `_.trimEnd` to get the index of the last non-whitespace\n * character of `string`.\n *\n * @private\n * @param {string} string The string to inspect.\n * @returns {number} Returns the index of the last non-whitespace character.\n */\nfunction trimmedEndIndex$1(string) {\n  var index = string.length;\n\n  while (index-- && reWhitespace.test(string.charAt(index))) {}\n  return index;\n}\n\nvar _trimmedEndIndex = trimmedEndIndex$1;var trimmedEndIndex = _trimmedEndIndex;\n\n/** Used to match leading whitespace. */\nvar reTrimStart = /^\\s+/;\n\n/**\n * The base implementation of `_.trim`.\n *\n * @private\n * @param {string} string The string to trim.\n * @returns {string} Returns the trimmed string.\n */\nfunction baseTrim$1(string) {\n  return string\n    ? string.slice(0, trimmedEndIndex(string) + 1).replace(reTrimStart, '')\n    : string;\n}\n\nvar _baseTrim = baseTrim$1;var root = _root;\n\n/** Built-in value references. */\nvar Symbol$2 = root.Symbol;\n\nvar _Symbol = Symbol$2;var Symbol$1 = _Symbol;\n\n/** Used for built-in method references. */\nvar objectProto$1 = Object.prototype;\n\n/** Used to check objects for own properties. */\nvar hasOwnProperty = objectProto$1.hasOwnProperty;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString$1 = objectProto$1.toString;\n\n/** Built-in value references. */\nvar symToStringTag$1 = Symbol$1 ? Symbol$1.toStringTag : undefined;\n\n/**\n * A specialized version of `baseGetTag` which ignores `Symbol.toStringTag` values.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the raw `toStringTag`.\n */\nfunction getRawTag$1(value) {\n  var isOwn = hasOwnProperty.call(value, symToStringTag$1),\n      tag = value[symToStringTag$1];\n\n  try {\n    value[symToStringTag$1] = undefined;\n    var unmasked = true;\n  } catch (e) {}\n\n  var result = nativeObjectToString$1.call(value);\n  if (unmasked) {\n    if (isOwn) {\n      value[symToStringTag$1] = tag;\n    } else {\n      delete value[symToStringTag$1];\n    }\n  }\n  return result;\n}\n\nvar _getRawTag = getRawTag$1;/** Used for built-in method references. */\n\nvar objectProto = Object.prototype;\n\n/**\n * Used to resolve the\n * [`toStringTag`](http://ecma-international.org/ecma-262/7.0/#sec-object.prototype.tostring)\n * of values.\n */\nvar nativeObjectToString = objectProto.toString;\n\n/**\n * Converts `value` to a string using `Object.prototype.toString`.\n *\n * @private\n * @param {*} value The value to convert.\n * @returns {string} Returns the converted string.\n */\nfunction objectToString$1(value) {\n  return nativeObjectToString.call(value);\n}\n\nvar _objectToString = objectToString$1;var Symbol = _Symbol,\n    getRawTag = _getRawTag,\n    objectToString = _objectToString;\n\n/** `Object#toString` result references. */\nvar nullTag = '[object Null]',\n    undefinedTag = '[object Undefined]';\n\n/** Built-in value references. */\nvar symToStringTag = Symbol ? Symbol.toStringTag : undefined;\n\n/**\n * The base implementation of `getTag` without fallbacks for buggy environments.\n *\n * @private\n * @param {*} value The value to query.\n * @returns {string} Returns the `toStringTag`.\n */\nfunction baseGetTag$1(value) {\n  if (value == null) {\n    return value === undefined ? undefinedTag : nullTag;\n  }\n  return (symToStringTag && symToStringTag in Object(value))\n    ? getRawTag(value)\n    : objectToString(value);\n}\n\nvar _baseGetTag = baseGetTag$1;/**\n * Checks if `value` is object-like. A value is object-like if it's not `null`\n * and has a `typeof` result of \"object\".\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is object-like, else `false`.\n * @example\n *\n * _.isObjectLike({});\n * // => true\n *\n * _.isObjectLike([1, 2, 3]);\n * // => true\n *\n * _.isObjectLike(_.noop);\n * // => false\n *\n * _.isObjectLike(null);\n * // => false\n */\n\nfunction isObjectLike$1(value) {\n  return value != null && typeof value == 'object';\n}\n\nvar isObjectLike_1 = isObjectLike$1;var baseGetTag = _baseGetTag,\n    isObjectLike = isObjectLike_1;\n\n/** `Object#toString` result references. */\nvar symbolTag = '[object Symbol]';\n\n/**\n * Checks if `value` is classified as a `Symbol` primitive or object.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to check.\n * @returns {boolean} Returns `true` if `value` is a symbol, else `false`.\n * @example\n *\n * _.isSymbol(Symbol.iterator);\n * // => true\n *\n * _.isSymbol('abc');\n * // => false\n */\nfunction isSymbol$1(value) {\n  return typeof value == 'symbol' ||\n    (isObjectLike(value) && baseGetTag(value) == symbolTag);\n}\n\nvar isSymbol_1 = isSymbol$1;var baseTrim = _baseTrim,\n    isObject$2 = isObject_1,\n    isSymbol = isSymbol_1;\n\n/** Used as references for various `Number` constants. */\nvar NAN = 0 / 0;\n\n/** Used to detect bad signed hexadecimal string values. */\nvar reIsBadHex = /^[-+]0x[0-9a-f]+$/i;\n\n/** Used to detect binary string values. */\nvar reIsBinary = /^0b[01]+$/i;\n\n/** Used to detect octal string values. */\nvar reIsOctal = /^0o[0-7]+$/i;\n\n/** Built-in method references without a dependency on `root`. */\nvar freeParseInt = parseInt;\n\n/**\n * Converts `value` to a number.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Lang\n * @param {*} value The value to process.\n * @returns {number} Returns the number.\n * @example\n *\n * _.toNumber(3.2);\n * // => 3.2\n *\n * _.toNumber(Number.MIN_VALUE);\n * // => 5e-324\n *\n * _.toNumber(Infinity);\n * // => Infinity\n *\n * _.toNumber('3.2');\n * // => 3.2\n */\nfunction toNumber$1(value) {\n  if (typeof value == 'number') {\n    return value;\n  }\n  if (isSymbol(value)) {\n    return NAN;\n  }\n  if (isObject$2(value)) {\n    var other = typeof value.valueOf == 'function' ? value.valueOf() : value;\n    value = isObject$2(other) ? (other + '') : other;\n  }\n  if (typeof value != 'string') {\n    return value === 0 ? value : +value;\n  }\n  value = baseTrim(value);\n  var isBinary = reIsBinary.test(value);\n  return (isBinary || reIsOctal.test(value))\n    ? freeParseInt(value.slice(2), isBinary ? 2 : 8)\n    : (reIsBadHex.test(value) ? NAN : +value);\n}\n\nvar toNumber_1 = toNumber$1;var isObject$1 = isObject_1,\n    now = now_1,\n    toNumber = toNumber_1;\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT$1 = 'Expected a function';\n\n/* Built-in method references for those with the same name as other `lodash` methods. */\nvar nativeMax = Math.max,\n    nativeMin = Math.min;\n\n/**\n * Creates a debounced function that delays invoking `func` until after `wait`\n * milliseconds have elapsed since the last time the debounced function was\n * invoked. The debounced function comes with a `cancel` method to cancel\n * delayed `func` invocations and a `flush` method to immediately invoke them.\n * Provide `options` to indicate whether `func` should be invoked on the\n * leading and/or trailing edge of the `wait` timeout. The `func` is invoked\n * with the last arguments provided to the debounced function. Subsequent\n * calls to the debounced function return the result of the last `func`\n * invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the debounced function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.debounce` and `_.throttle`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to debounce.\n * @param {number} [wait=0] The number of milliseconds to delay.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=false]\n *  Specify invoking on the leading edge of the timeout.\n * @param {number} [options.maxWait]\n *  The maximum time `func` is allowed to be delayed before it's invoked.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new debounced function.\n * @example\n *\n * // Avoid costly calculations while the window size is in flux.\n * jQuery(window).on('resize', _.debounce(calculateLayout, 150));\n *\n * // Invoke `sendMail` when clicked, debouncing subsequent calls.\n * jQuery(element).on('click', _.debounce(sendMail, 300, {\n *   'leading': true,\n *   'trailing': false\n * }));\n *\n * // Ensure `batchLog` is invoked once after 1 second of debounced calls.\n * var debounced = _.debounce(batchLog, 250, { 'maxWait': 1000 });\n * var source = new EventSource('/stream');\n * jQuery(source).on('message', debounced);\n *\n * // Cancel the trailing debounced invocation.\n * jQuery(window).on('popstate', debounced.cancel);\n */\nfunction debounce$1(func, wait, options) {\n  var lastArgs,\n      lastThis,\n      maxWait,\n      result,\n      timerId,\n      lastCallTime,\n      lastInvokeTime = 0,\n      leading = false,\n      maxing = false,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT$1);\n  }\n  wait = toNumber(wait) || 0;\n  if (isObject$1(options)) {\n    leading = !!options.leading;\n    maxing = 'maxWait' in options;\n    maxWait = maxing ? nativeMax(toNumber(options.maxWait) || 0, wait) : maxWait;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n\n  function invokeFunc(time) {\n    var args = lastArgs,\n        thisArg = lastThis;\n\n    lastArgs = lastThis = undefined;\n    lastInvokeTime = time;\n    result = func.apply(thisArg, args);\n    return result;\n  }\n\n  function leadingEdge(time) {\n    // Reset any `maxWait` timer.\n    lastInvokeTime = time;\n    // Start the timer for the trailing edge.\n    timerId = setTimeout(timerExpired, wait);\n    // Invoke the leading edge.\n    return leading ? invokeFunc(time) : result;\n  }\n\n  function remainingWait(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime,\n        timeWaiting = wait - timeSinceLastCall;\n\n    return maxing\n      ? nativeMin(timeWaiting, maxWait - timeSinceLastInvoke)\n      : timeWaiting;\n  }\n\n  function shouldInvoke(time) {\n    var timeSinceLastCall = time - lastCallTime,\n        timeSinceLastInvoke = time - lastInvokeTime;\n\n    // Either this is the first call, activity has stopped and we're at the\n    // trailing edge, the system time has gone backwards and we're treating\n    // it as the trailing edge, or we've hit the `maxWait` limit.\n    return (lastCallTime === undefined || (timeSinceLastCall >= wait) ||\n      (timeSinceLastCall < 0) || (maxing && timeSinceLastInvoke >= maxWait));\n  }\n\n  function timerExpired() {\n    var time = now();\n    if (shouldInvoke(time)) {\n      return trailingEdge(time);\n    }\n    // Restart the timer.\n    timerId = setTimeout(timerExpired, remainingWait(time));\n  }\n\n  function trailingEdge(time) {\n    timerId = undefined;\n\n    // Only invoke if we have `lastArgs` which means `func` has been\n    // debounced at least once.\n    if (trailing && lastArgs) {\n      return invokeFunc(time);\n    }\n    lastArgs = lastThis = undefined;\n    return result;\n  }\n\n  function cancel() {\n    if (timerId !== undefined) {\n      clearTimeout(timerId);\n    }\n    lastInvokeTime = 0;\n    lastArgs = lastCallTime = lastThis = timerId = undefined;\n  }\n\n  function flush() {\n    return timerId === undefined ? result : trailingEdge(now());\n  }\n\n  function debounced() {\n    var time = now(),\n        isInvoking = shouldInvoke(time);\n\n    lastArgs = arguments;\n    lastThis = this;\n    lastCallTime = time;\n\n    if (isInvoking) {\n      if (timerId === undefined) {\n        return leadingEdge(lastCallTime);\n      }\n      if (maxing) {\n        // Handle invocations in a tight loop.\n        clearTimeout(timerId);\n        timerId = setTimeout(timerExpired, wait);\n        return invokeFunc(lastCallTime);\n      }\n    }\n    if (timerId === undefined) {\n      timerId = setTimeout(timerExpired, wait);\n    }\n    return result;\n  }\n  debounced.cancel = cancel;\n  debounced.flush = flush;\n  return debounced;\n}\n\nvar debounce_1 = debounce$1;var debounce = debounce_1,\n    isObject = isObject_1;\n\n/** Error message constants. */\nvar FUNC_ERROR_TEXT = 'Expected a function';\n\n/**\n * Creates a throttled function that only invokes `func` at most once per\n * every `wait` milliseconds. The throttled function comes with a `cancel`\n * method to cancel delayed `func` invocations and a `flush` method to\n * immediately invoke them. Provide `options` to indicate whether `func`\n * should be invoked on the leading and/or trailing edge of the `wait`\n * timeout. The `func` is invoked with the last arguments provided to the\n * throttled function. Subsequent calls to the throttled function return the\n * result of the last `func` invocation.\n *\n * **Note:** If `leading` and `trailing` options are `true`, `func` is\n * invoked on the trailing edge of the timeout only if the throttled function\n * is invoked more than once during the `wait` timeout.\n *\n * If `wait` is `0` and `leading` is `false`, `func` invocation is deferred\n * until to the next tick, similar to `setTimeout` with a timeout of `0`.\n *\n * See [David Corbacho's article](https://css-tricks.com/debouncing-throttling-explained-examples/)\n * for details over the differences between `_.throttle` and `_.debounce`.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Function\n * @param {Function} func The function to throttle.\n * @param {number} [wait=0] The number of milliseconds to throttle invocations to.\n * @param {Object} [options={}] The options object.\n * @param {boolean} [options.leading=true]\n *  Specify invoking on the leading edge of the timeout.\n * @param {boolean} [options.trailing=true]\n *  Specify invoking on the trailing edge of the timeout.\n * @returns {Function} Returns the new throttled function.\n * @example\n *\n * // Avoid excessively updating the position while scrolling.\n * jQuery(window).on('scroll', _.throttle(updatePosition, 100));\n *\n * // Invoke `renewToken` when the click event is fired, but not more than once every 5 minutes.\n * var throttled = _.throttle(renewToken, 300000, { 'trailing': false });\n * jQuery(element).on('click', throttled);\n *\n * // Cancel the trailing throttled invocation.\n * jQuery(window).on('popstate', throttled.cancel);\n */\nfunction throttle(func, wait, options) {\n  var leading = true,\n      trailing = true;\n\n  if (typeof func != 'function') {\n    throw new TypeError(FUNC_ERROR_TEXT);\n  }\n  if (isObject(options)) {\n    leading = 'leading' in options ? !!options.leading : leading;\n    trailing = 'trailing' in options ? !!options.trailing : trailing;\n  }\n  return debounce(func, wait, {\n    'leading': leading,\n    'maxWait': wait,\n    'trailing': trailing\n  });\n}\n\nvar throttle_1 = throttle;var patchResizeHandler = function (resizeCallback, refreshMode, refreshRate, refreshOptions) {\r\n    switch (refreshMode) {\r\n        case 'debounce':\r\n            return debounce_1(resizeCallback, refreshRate, refreshOptions);\r\n        case 'throttle':\r\n            return throttle_1(resizeCallback, refreshRate, refreshOptions);\r\n        default:\r\n            return resizeCallback;\r\n    }\r\n};\r\nvar isFunction = function (fn) { return typeof fn === 'function'; };\r\nvar isSSR = function () { return typeof window === 'undefined'; };\r\nvar isDOMElement = function (element) { return element instanceof Element || element instanceof HTMLDocument; };\r\nvar createNotifier = function (onResize, setSize, handleWidth, handleHeight) {\r\n    return function (_a) {\r\n        var width = _a.width, height = _a.height;\r\n        setSize(function (prev) {\r\n            if (prev.width === width && prev.height === height) {\r\n                // skip if dimensions haven't changed\r\n                return prev;\r\n            }\r\n            if ((prev.width === width && !handleHeight) || (prev.height === height && !handleWidth)) {\r\n                // process `handleHeight/handleWidth` props\r\n                return prev;\r\n            }\r\n            if (onResize && isFunction(onResize)) {\r\n                onResize(width, height);\r\n            }\r\n            return { width: width, height: height };\r\n        });\r\n    };\r\n};var ResizeDetector = /** @class */ (function (_super) {\r\n    __extends(ResizeDetector, _super);\r\n    function ResizeDetector(props) {\r\n        var _this = _super.call(this, props) || this;\r\n        _this.cancelHandler = function () {\r\n            if (_this.resizeHandler && _this.resizeHandler.cancel) {\r\n                // cancel debounced handler\r\n                _this.resizeHandler.cancel();\r\n                _this.resizeHandler = null;\r\n            }\r\n        };\r\n        _this.attachObserver = function () {\r\n            var _a = _this.props, targetRef = _a.targetRef, observerOptions = _a.observerOptions;\r\n            if (isSSR()) {\r\n                return;\r\n            }\r\n            if (targetRef && targetRef.current) {\r\n                _this.targetRef.current = targetRef.current;\r\n            }\r\n            var element = _this.getElement();\r\n            if (!element) {\r\n                // can't find element to observe\r\n                return;\r\n            }\r\n            if (_this.observableElement && _this.observableElement === element) {\r\n                // element is already observed\r\n                return;\r\n            }\r\n            _this.observableElement = element;\r\n            _this.resizeObserver.observe(element, observerOptions);\r\n        };\r\n        _this.getElement = function () {\r\n            var _a = _this.props, querySelector = _a.querySelector, targetDomEl = _a.targetDomEl;\r\n            if (isSSR())\r\n                return null;\r\n            // in case we pass a querySelector\r\n            if (querySelector)\r\n                return document.querySelector(querySelector);\r\n            // in case we pass a DOM element\r\n            if (targetDomEl && isDOMElement(targetDomEl))\r\n                return targetDomEl;\r\n            // in case we pass a React ref using React.createRef()\r\n            if (_this.targetRef && isDOMElement(_this.targetRef.current))\r\n                return _this.targetRef.current;\r\n            // the worse case when we don't receive any information from the parent and the library doesn't add any wrappers\r\n            // we have to use a deprecated `findDOMNode` method in order to find a DOM element to attach to\r\n            var currentElement = reactDom.findDOMNode(_this);\r\n            if (!currentElement)\r\n                return null;\r\n            var renderType = _this.getRenderType();\r\n            switch (renderType) {\r\n                case 'renderProp':\r\n                    return currentElement;\r\n                case 'childFunction':\r\n                    return currentElement;\r\n                case 'child':\r\n                    return currentElement;\r\n                case 'childArray':\r\n                    return currentElement;\r\n                default:\r\n                    return currentElement.parentElement;\r\n            }\r\n        };\r\n        _this.createResizeHandler = function (entries) {\r\n            var _a = _this.props, _b = _a.handleWidth, handleWidth = _b === void 0 ? true : _b, _c = _a.handleHeight, handleHeight = _c === void 0 ? true : _c, onResize = _a.onResize;\r\n            if (!handleWidth && !handleHeight)\r\n                return;\r\n            var notifyResize = createNotifier(onResize, _this.setState.bind(_this), handleWidth, handleHeight);\r\n            entries.forEach(function (entry) {\r\n                var _a = (entry && entry.contentRect) || {}, width = _a.width, height = _a.height;\r\n                var shouldSetSize = !_this.skipOnMount && !isSSR();\r\n                if (shouldSetSize) {\r\n                    notifyResize({ width: width, height: height });\r\n                }\r\n                _this.skipOnMount = false;\r\n            });\r\n        };\r\n        _this.getRenderType = function () {\r\n            var _a = _this.props, render = _a.render, children = _a.children;\r\n            if (isFunction(render)) {\r\n                // DEPRECATED. Use `Child Function Pattern` instead\r\n                return 'renderProp';\r\n            }\r\n            if (isFunction(children)) {\r\n                return 'childFunction';\r\n            }\r\n            if (React.isValidElement(children)) {\r\n                return 'child';\r\n            }\r\n            if (Array.isArray(children)) {\r\n                // DEPRECATED. Wrap children with a single parent\r\n                return 'childArray';\r\n            }\r\n            // DEPRECATED. Use `Child Function Pattern` instead\r\n            return 'parent';\r\n        };\r\n        var skipOnMount = props.skipOnMount, refreshMode = props.refreshMode, _a = props.refreshRate, refreshRate = _a === void 0 ? 1000 : _a, refreshOptions = props.refreshOptions;\r\n        _this.state = {\r\n            width: undefined,\r\n            height: undefined\r\n        };\r\n        _this.skipOnMount = skipOnMount;\r\n        _this.targetRef = React.createRef();\r\n        _this.observableElement = null;\r\n        if (isSSR()) {\r\n            return _this;\r\n        }\r\n        _this.resizeHandler = patchResizeHandler(_this.createResizeHandler, refreshMode, refreshRate, refreshOptions);\r\n        _this.resizeObserver = new $inject_window_ResizeObserver__default[\"default\"](_this.resizeHandler);\r\n        return _this;\r\n    }\r\n    ResizeDetector.prototype.componentDidMount = function () {\r\n        this.attachObserver();\r\n    };\r\n    ResizeDetector.prototype.componentDidUpdate = function () {\r\n        this.attachObserver();\r\n    };\r\n    ResizeDetector.prototype.componentWillUnmount = function () {\r\n        if (isSSR()) {\r\n            return;\r\n        }\r\n        this.resizeObserver.disconnect();\r\n        this.cancelHandler();\r\n    };\r\n    ResizeDetector.prototype.render = function () {\r\n        var _a = this.props, render = _a.render, children = _a.children, _b = _a.nodeType, WrapperTag = _b === void 0 ? 'div' : _b;\r\n        var _c = this.state, width = _c.width, height = _c.height;\r\n        var childProps = { width: width, height: height, targetRef: this.targetRef };\r\n        var renderType = this.getRenderType();\r\n        var typedChildren;\r\n        switch (renderType) {\r\n            case 'renderProp':\r\n                return render && render(childProps);\r\n            case 'childFunction':\r\n                typedChildren = children;\r\n                return typedChildren(childProps);\r\n            case 'child':\r\n                // @TODO bug prone logic\r\n                typedChildren = children;\r\n                if (typedChildren.type && typeof typedChildren.type === 'string') {\r\n                    // child is a native DOM elements such as div, span etc\r\n                    childProps.targetRef; var nativeProps = __rest(childProps, [\"targetRef\"]);\r\n                    return React.cloneElement(typedChildren, nativeProps);\r\n                }\r\n                // class or functional component otherwise\r\n                return React.cloneElement(typedChildren, childProps);\r\n            case 'childArray':\r\n                typedChildren = children;\r\n                return typedChildren.map(function (el) { return !!el && React.cloneElement(el, childProps); });\r\n            default:\r\n                return React__namespace.createElement(WrapperTag, null);\r\n        }\r\n    };\r\n    return ResizeDetector;\r\n}(React.PureComponent));function withResizeDetector(ComponentInner, options) {\r\n    if (options === void 0) { options = {}; }\r\n    var ResizeDetectorHOC = /** @class */ (function (_super) {\r\n        __extends(ResizeDetectorHOC, _super);\r\n        function ResizeDetectorHOC() {\r\n            var _this = _super !== null && _super.apply(this, arguments) || this;\r\n            _this.ref = React.createRef();\r\n            return _this;\r\n        }\r\n        ResizeDetectorHOC.prototype.render = function () {\r\n            var _a = this.props, forwardedRef = _a.forwardedRef, rest = __rest(_a, [\"forwardedRef\"]);\r\n            var targetRef = forwardedRef !== null && forwardedRef !== void 0 ? forwardedRef : this.ref;\r\n            return (React__namespace.createElement(ResizeDetector, __assign({}, options, { targetRef: targetRef }),\r\n                React__namespace.createElement(ComponentInner, __assign({ targetRef: targetRef }, rest))));\r\n        };\r\n        return ResizeDetectorHOC;\r\n    }(React.Component));\r\n    function forwardRefWrapper(props, ref) {\r\n        return React__namespace.createElement(ResizeDetectorHOC, __assign({}, props, { forwardedRef: ref }));\r\n    }\r\n    var name = ComponentInner.displayName || ComponentInner.name;\r\n    forwardRefWrapper.displayName = \"withResizeDetector(\".concat(name, \")\");\r\n    return React.forwardRef(forwardRefWrapper);\r\n}var useEnhancedEffect = isSSR() ? React.useEffect : React.useLayoutEffect;\r\nfunction useResizeDetector(props) {\r\n    if (props === void 0) { props = {}; }\r\n    var _a = props.skipOnMount, skipOnMount = _a === void 0 ? false : _a, refreshMode = props.refreshMode, _b = props.refreshRate, refreshRate = _b === void 0 ? 1000 : _b, refreshOptions = props.refreshOptions, _c = props.handleWidth, handleWidth = _c === void 0 ? true : _c, _d = props.handleHeight, handleHeight = _d === void 0 ? true : _d, targetRef = props.targetRef, observerOptions = props.observerOptions, onResize = props.onResize;\r\n    var skipResize = React.useRef(skipOnMount);\r\n    var localRef = React.useRef(null);\r\n    var ref = (targetRef !== null && targetRef !== void 0 ? targetRef : localRef);\r\n    var resizeHandler = React.useRef();\r\n    var _e = React.useState({\r\n        width: undefined,\r\n        height: undefined\r\n    }), size = _e[0], setSize = _e[1];\r\n    useEnhancedEffect(function () {\r\n        if (isSSR()) {\r\n            return;\r\n        }\r\n        var notifyResize = createNotifier(onResize, setSize, handleWidth, handleHeight);\r\n        var resizeCallback = function (entries) {\r\n            if (!handleWidth && !handleHeight)\r\n                return;\r\n            entries.forEach(function (entry) {\r\n                var _a = (entry && entry.contentRect) || {}, width = _a.width, height = _a.height;\r\n                var shouldSetSize = !skipResize.current && !isSSR();\r\n                if (shouldSetSize) {\r\n                    notifyResize({ width: width, height: height });\r\n                }\r\n                skipResize.current = false;\r\n            });\r\n        };\r\n        resizeHandler.current = patchResizeHandler(resizeCallback, refreshMode, refreshRate, refreshOptions);\r\n        var resizeObserver = new $inject_window_ResizeObserver__default[\"default\"](resizeHandler.current);\r\n        if (ref.current) {\r\n            // Something wrong with typings here...\r\n            resizeObserver.observe(ref.current, observerOptions);\r\n        }\r\n        return function () {\r\n            resizeObserver.disconnect();\r\n            var patchedResizeHandler = resizeHandler.current;\r\n            if (patchedResizeHandler && patchedResizeHandler.cancel) {\r\n                patchedResizeHandler.cancel();\r\n            }\r\n        };\r\n    }, [refreshMode, refreshRate, refreshOptions, handleWidth, handleHeight, onResize, observerOptions, ref.current]);\r\n    return __assign({ ref: ref }, size);\r\n}exports[\"default\"]=ResizeDetector;exports.useResizeDetector=useResizeDetector;exports.withResizeDetector=withResizeDetector;//# sourceMappingURL=withPolyfill.js.map\n"], "names": ["RESPONSIVE_LAYOUT", "autosize", "height", "undefined", "width", "AUTO_LAYOUT", "UNRESPONSIVE_LAYOUT", "RESPONSIVE_CONFIG", "responsive", "AUTO_CONFIG", "UNRESPONSIVE_CONFIG", "filterEventData", "gd", "eventData", "event", "filteredEventData", "includes", "points", "isNil", "data", "_loop", "fullPoint", "i", "pointData", "filter", "o", "type", "has", "bbox", "curveNumber", "pointNumber", "customdata", "mode", "pointNumbers", "map", "point", "length", "range", "lassoPoints", "PlotlyGraph", "_Component", "_inherits", "_super", "props", "_this2", "_classCallCheck", "call", "React", "_hasPlotted", "_prevGd", "_queue", "Promise", "resolve", "bindEvents", "bind", "_assertThisInitialized", "getConfig", "getConfigOverride", "getLayout", "getLayoutOverride", "graphResize", "isResponsive", "amendTraces", "state", "override", "originals", "key", "value", "_this3", "figure", "config", "animate", "animation_options", "mathjax", "this", "current", "_dashprivate_transformFigure", "_dashprivate_transformConfig", "config<PERSON><PERSON>", "typesetMath", "figureClone", "layout", "frames", "<PERSON><PERSON><PERSON>", "deleteFrames", "then", "addFrames", "classList", "add", "lazyLoadMathJax", "react", "remove", "removeAllListeners", "purge", "p", "oldProps", "newProps", "oldPrepend", "prependData", "oldExtend", "extendData", "newPrepend", "newExtend", "_this", "mergeTraces", "dataKey", "plotly<PERSON>n<PERSON><PERSON>", "clearState", "dataArray", "_p", "for<PERSON>ach", "updateData", "traceIndices", "maxPoints", "Array", "isArray", "_data", "from", "Object", "keys", "getFirstProp", "generateIndices", "modified", "_dashprivate_onFigureModified", "mergeDeepRight", "_this$state", "prev_override", "prev_originals", "hasOwnProperty", "setState", "Boolean", "arguments", "Plots", "resize", "catch", "finally", "_this$props", "setProps", "clear_on_unhover", "relayoutData", "restyleData", "hoverData", "selectedData", "on", "clickData", "clickAnnotationData", "omit", "hover", "equals", "selected", "relayout", "restyle", "plot", "nextProps", "id", "JSON", "stringify", "style", "loading_state", "_this4", "prevProps", "_this5", "_this$props2", "className", "is_loading", "ResizeDetector", "handleHeight", "handleWidth", "refreshMode", "refreshOptions", "trailing", "refreshRate", "onResize", "ref", "Component", "propTypes", "_objectSpread", "graphPropTypes", "PropTypes", "isRequired", "defaultProps", "graphDefaultProps", "$inject_window_ResizeObserver", "reactDom", "_interopDefaultLegacy", "e", "_interopNamespace", "__esModule", "n", "create", "k", "d", "getOwnPropertyDescriptor", "defineProperty", "get", "enumerable", "freeze", "$inject_window_ResizeObserver__default", "React__namespace", "extendStatics", "b", "setPrototypeOf", "__proto__", "prototype", "commonjsGlobal", "globalThis", "window", "g", "self", "isObject_1", "freeGlobal", "freeSelf", "root$2", "Function", "root$1", "reWhitespace", "reTrimStart", "_Symbol", "Symbol", "Symbol$1", "objectProto$1", "nativeObjectToString$1", "toString", "symToStringTag$1", "toStringTag", "nativeObjectToString", "getRawTag", "isOwn", "tag", "unmasked", "result", "symToStringTag", "baseGetTag", "objectToString", "baseTrim", "string", "slice", "index", "test", "char<PERSON>t", "trimmedEndIndex", "replace", "isObject$2", "reIsBadHex", "reIsBinary", "reIsOctal", "freeParseInt", "parseInt", "isObject$1", "now", "Date", "toNumber", "isObjectLike", "isSymbol", "other", "valueOf", "isBinary", "nativeMax", "Math", "max", "nativeMin", "min", "debounce_1", "func", "wait", "options", "lastArgs", "lastThis", "max<PERSON><PERSON>", "timerId", "lastCallTime", "lastInvokeTime", "leading", "maxing", "TypeError", "invokeFunc", "time", "args", "thisArg", "apply", "shouldInvoke", "timeSinceLastCall", "timerExpired", "trailingEdge", "setTimeout", "timeWaiting", "remainingWait", "debounced", "isInvoking", "leading<PERSON>dge", "clearTimeout", "cancel", "flush", "debounce", "isObject", "patchResizeHandler", "resizeCallback", "throttle_1", "isFunction", "fn", "isSSR", "isDOMElement", "element", "Element", "HTMLDocument", "cancelHandler", "resize<PERSON><PERSON>ler", "attachObserver", "_a", "targetRef", "observerOptions", "getElement", "observableElement", "resizeObserver", "observe", "querySelector", "targetDomEl", "document", "currentElement", "findDOMNode", "getRenderType", "parentElement", "createResizeHandler", "entries", "_b", "_c", "notifyResize", "setSize", "prev", "createNotifier", "entry", "contentRect", "skip<PERSON>n<PERSON>ount", "render", "children", "isValidElement", "createRef", "String", "__", "constructor", "__extends", "componentDidMount", "componentDidUpdate", "componentWillUnmount", "disconnect", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType", "WrapperTag", "childProps", "nativeProps", "s", "t", "indexOf", "getOwnPropertySymbols", "propertyIsEnumerable", "__rest", "cloneElement", "el", "createElement", "PureComponent", "useEffect", "useLayoutEffect", "exports"], "sourceRoot": ""}