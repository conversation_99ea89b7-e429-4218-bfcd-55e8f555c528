{"name": "dash-core-components", "version": "2.12.1", "description": "Core component suite for Dash", "repository": {"type": "git", "url": "git://github.com/plotly/dash.git"}, "bugs": {"url": "https://github.com/plotly/dash/issues"}, "homepage": "https://github.com/plotly/dash", "main": "dash_core_components/dash_core_components.js", "scripts": {"private::format.black": "black dash_core_components_base/ tests/ setup.py", "private::format.eslint": "eslint src --fix", "private::format.prettier": "prettier --config .prettierrc --write src/**/*.js", "private::lint.black": "node -e \"if ((process.env.PYVERSION || 'python39') !== 'python36'){process.exit(1)} \" || black --check dash_core_components_base/ tests/ setup.py", "private::lint.eslint": "eslint src", "private::lint.flake8": "flake8 --exclude=dash_core_components,node_modules,venv", "private::lint.prettier": "prettier --config .prettierrc src/**/*.js --list-different", "prepublishOnly": "rm -rf lib && babel src --out-dir lib --copy-files --config-file ./.lib.babelrc && rm -rf lib/jl/ lib/*.jl", "test": "run-s -c lint test:intg test:pyimport", "test:intg": "pytest --nopercyfinalize --headless tests/integration ", "test:pyimport": "python -m unittest tests/test_dash_import.py", "build:js": "webpack --mode production", "build:backends": "dash-generate-components ./src/components dash_core_components -p package-info.json  && cp dash_core_components_base/** dash_core_components/ && dash-generate-components ./src/components dash_core_components -p package-info.json -k RangeSlider,Slider,Dropdown,RadioItems,Checklist,DatePickerSingle,DatePickerRange,Input,Link --r-prefix 'dcc' --r-suggests 'dash,dashHtmlComponents,jsonlite,plotly' --jl-prefix 'dcc' && black dash_core_components", "build": "run-s prepublishOnly build:js build:backends", "postbuild": "es-check es2015 dash_core_components/*.js", "build:watch": "watch 'npm run build' src", "format": "run-s private::format.*", "lint": "run-s private::lint.*"}, "author": "<PERSON> <<EMAIL>>", "maintainer": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"@fortawesome/fontawesome-svg-core": "1.2.36", "@fortawesome/free-regular-svg-icons": "^5.15.4", "@fortawesome/free-solid-svg-icons": "^5.15.4", "@fortawesome/react-fontawesome": "^0.1.17", "base64-js": "^1.5.1", "color": "^4.2.3", "d3-format": "^1.4.5", "fast-isnumeric": "^1.1.4", "file-saver": "^2.0.5", "highlight.js": "^11.8.0", "mathjax": "^3.2.2", "moment": "^2.29.4", "node-polyfill-webpack-plugin": "^2.0.1", "prop-types": "^15.8.1", "ramda": "^0.29.0", "rc-slider": "^9.7.5", "react-addons-shallow-compare": "^15.6.3", "react-dates": "^21.8.0", "react-docgen": "^5.4.3", "react-dropzone": "^4.1.2", "react-fast-compare": "^3.2.2", "react-markdown": "^4.3.1", "react-resize-detector": "^6.7.6", "react-select-fast-filter-options": "^0.2.3", "react-virtualized-select": "^3.1.3", "remark-math": "^3.0.1", "uniqid": "^5.4.0"}, "devDependencies": {"@babel/cli": "^7.23.0", "@babel/core": "^7.23.0", "@babel/plugin-syntax-dynamic-import": "^7.8.3", "@babel/preset-env": "^7.22.20", "@babel/preset-react": "^7.22.15", "@plotly/dash-component-plugins": "^1.2.3", "@plotly/webpack-dash-dynamic-import": "^1.3.0", "babel-loader": "^9.1.3", "component-playground": "^3.2.1", "copyfiles": "^2.4.1", "css-loader": "^6.8.1", "es-check": "^7.1.1", "eslint": "^8.41.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-react": "^7.32.2", "identity-obj-proxy": "^3.0.0", "npm-run-all": "^4.1.5", "prettier": "^2.8.8", "react": "^16.14.0", "react-dom": "^16.14.0", "react-jsx-parser": "1.21.0", "style-loader": "^3.3.3", "styled-jsx": "^3.4.4", "webpack": "^5.88.2", "webpack-cli": "^5.1.4"}, "files": ["/dash_core_components/*{.js,.map}", "/lib/"], "peerDependencies": {"react": ">=16", "react-dom": ">=16"}, "browserslist": ["last 8 years and not dead"]}