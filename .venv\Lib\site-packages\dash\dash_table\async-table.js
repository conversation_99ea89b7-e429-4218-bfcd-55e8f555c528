/*! For license information please see async-table.js.LICENSE.txt */
(self.webpackChunkdash_table=self.webpackChunkdash_table||[]).push([[108],{70:function(e,t,n){"use strict";function r(e,t){return e===t||o(Object.values(e),Object.values(t))}function o(e,t){if(!e)return!1;var n=e.length;if(n!==t.length)return!1;for(var r=0;r<n;++r)if(e[r]!==t[r])return!1;return!0}n.d(t,{A:function(){return o},X:function(){return r}})},5117:function(e,t,n){"use strict";n.d(t,{Pi:function(){return i},qe:function(){return o},yw:function(){return a}});var r=n(70);function o(e){var t,n=null;return function(){for(var o=arguments.length,i=new Array(o),a=0;a<o;a++)i[a]=arguments[a];return(0,r.A)(n,i)?t:(n=i)&&(t=e.apply(void 0,i))}}function i(e){return function(){return o(e)}}function a(e){var t,n=null,o=!0;return function(){for(var i=arguments.length,a=new Array(i),l=0;l<i;l++)a[l]=arguments[l];var s=(0,r.A)(n,a)?{cached:!0,first:o,result:t}:{cached:!1,first:o,result:(n=a)&&(t=e.apply(void 0,a))};return o=!1,s}}},4167:function(e,t,n){"use strict";function r(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,a(r.key),r)}}function o(e,t,n){return t&&r(e.prototype,t),n&&r(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function i(e,t,n){return(t=a(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function a(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var l,s,c,u,p,d,f,h;n.d(t,{Ap:function(){return A},CR:function(){return u},J2:function(){return h},KI:function(){return v},Pj:function(){return s},QD:function(){return l},T2:function(){return c},UT:function(){return b},h8:function(){return d},oN:function(){return p},p9:function(){return f},sg:function(){return y}}),function(e){e.Any="any",e.Numeric="numeric",e.Text="text",e.Datetime="datetime"}(l||(l={})),function(e){e.All="all",e.Visible="visible"}(s||(s={})),function(e){e.Csv="csv",e.Xlsx="xlsx",e.None="none"}(c||(c={})),function(e){e.Ids="ids",e.Names="names",e.None="none",e.Display="display"}(u||(u={})),function(e){e.Insensitive="insensitive",e.Sensitive="sensitive"}(p||(p={})),function(e){e.Single="single",e.Multi="multi"}(d||(d={})),function(e){e.Custom="custom",e.Native="native",e.None="none"}(f||(f={})),function(e){e.And="and",e.Or="or"}(h||(h={}));var v,b,A,y=o((function e(t){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),i(this,"clearable",void 0),i(this,"deletable",void 0),i(this,"editable",!1),i(this,"filter_options",void 0),i(this,"hideable",void 0),i(this,"renamable",void 0),i(this,"selectable",void 0),i(this,"sort_as_null",[]),i(this,"id",void 0),i(this,"name",[]),Object.keys(t).includes("name")&&(this.name=t.name),Object.keys(t).includes("id")&&(this.id=t.id)}));!function(e){e.Coerce="coerce",e.None="none",e.Validate="validate"}(v||(v={})),function(e){e.Default="default",e.Accept="accept",e.Reject="reject"}(b||(b={})),function(e){e.Dropdown="dropdown",e.Input="input",e.Markdown="markdown"}(A||(A={}))},335:function(e,t,n){"use strict";var r,o;n.d(t,{a:function(){return o},v:function(){return r}}),function(e){e.Text="text",e.Markdown="markdown"}(r||(r={})),function(e){e.Both="both",e.Data="data",e.Header="header"}(o||(o={}))},8821:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return qd}});var r=n(9196),o=n.n(r),i=n(6331),a=n(5117),l=n(3936);function s(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function c(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,d(r.key),r)}}function u(e,t,n){return t&&c(e.prototype,t),n&&c(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function p(e,t,n){return(t=d(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function d(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var f=function(){function e(t){s(this,e),p(this,"name",void 0),p(this,"__stylesheet",void 0),this.name=t}return u(e,[{key:"rules",get:function(){var e=this.sheet;return Array.from(e.rules||e.cssRules)}},{key:"addRule",value:function(e,t){this.sheet.addRule?this.sheet.addRule(e,t):this.sheet.insertRule("".concat(e," { ").concat(t," }"),0)}},{key:"deleteRule",value:function(e){this.sheet.deleteRule(e)}},{key:"findRule",value:function(e){var t=this.rules,n=t.findIndex((function(t){return t.selectorText===e}));return-1===n?null:{rule:t[n],index:n}}},{key:"sheet",get:function(){var e;return(this.__stylesheet=this.__stylesheet||(e=document.createElement("style"),e.type="text/css",e.id=this.name,document.getElementsByTagName("head")[0].appendChild(e),e)).sheet}}]),e}(),h=function(){function e(t){s(this,e),p(this,"prefix",void 0),p(this,"stylesheet",void 0),this.prefix=t,this.stylesheet=new f("".concat(t,"-dynamic-inline.css"))}return u(e,[{key:"deleteRule",value:function(e){e="".concat(this.prefix," ").concat(e);var t=this.stylesheet.findRule(e);t&&this.stylesheet.deleteRule(t.index)}},{key:"setRule",value:function(e,t){e="".concat(this.prefix," ").concat(e);var n=this.stylesheet.findRule(e);if(n){if(n.rule.cssText===t||n.rule.cssText==="".concat(e," { ").concat(t," }"))return;this.stylesheet.deleteRule(n.index)}this.stylesheet.addRule(e,t),l.ZP.trace("stylesheet",e,t)}}]),e}(),v={MOUSE_LEFT:1,MOUSE_RIGHT:3,MOUSE_MIDDLE:2,BACKSPACE:8,COMMA:188,INSERT:45,DELETE:46,END:35,ENTER:13,ESCAPE:27,CONTROL:17,COMMAND_LEFT:91,COMMAND_RIGHT:93,COMMAND_FIREFOX:224,ALT:18,HOME:36,PAGE_DOWN:34,PAGE_UP:33,PERIOD:190,SPACE:32,SHIFT:16,CAPS_LOCK:20,TAB:9,ARROW_RIGHT:39,ARROW_LEFT:37,ARROW_UP:38,ARROW_DOWN:40,F1:112,F2:113,F3:114,F4:115,F5:116,F6:117,F7:118,F8:119,F9:120,F10:121,F11:122,F12:123,A:65,X:88,C:67,V:86},b=(v.ARROW_DOWN,v.ARROW_UP,v.ARROW_LEFT,v.ARROW_RIGHT,v.HOME,v.END,v.DELETE,v.BACKSPACE,v.F1,v.F2,v.F3,v.F4,v.F5,v.F6,v.F7,v.F8,v.F9,v.F10,v.F11,v.F12,v.TAB,v.PAGE_DOWN,v.PAGE_UP,v.ENTER,v.ESCAPE,v.SHIFT,v.CAPS_LOCK,v.ALT,[v.ARROW_DOWN,v.ARROW_UP,v.ARROW_LEFT,v.ARROW_RIGHT]),A=[].concat(b,[v.TAB,v.ENTER]);function y(e){return-1!==A.indexOf(e)}function g(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var m=function(e){return Array.isArray(e.name)?e.name.length:1},w=function(e){return Math.max.apply(Math,function(e){if(Array.isArray(e))return g(e)}(t=e.map(m))||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(t)||function(e,t){if(e){if("string"==typeof e)return g(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?g(e,t):void 0}}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}());var t};function E(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function C(e,t,n,r,o){var i=arguments.length>5&&void 0!==arguments[5]&&arguments[5];if(!e.name||Array.isArray(e.name)&&e.name.length<n||!r)return{groupIndexFirst:o,groupIndexLast:o};if(i)for(var a=o;a>=0;--a){var l=t[a];if(!(l.name&&Array.isArray(l.name)&&l.name.length>n&&l.name[n]===e.name[n]))break;o=a}for(var s=o,c=o;c<t.length;++c){var u=t[c];if(!(u.name&&Array.isArray(u.name)&&u.name.length>n&&u.name[n]===e.name[n]))break;s=c}return{groupIndexFirst:o,groupIndexLast:s}}function k(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]&&arguments[4],a=C(e,t,n,r,t.indexOf(e),o),l=a.groupIndexFirst,s=a.groupIndexLast;return i.tPi(l,s+1,i.jge("id",t))}function x(e,t,n,r,o,i){return{data:O(e,t,n,r,o,i).data}}function O(e,t,n,r,o,a){var l=k(e,n,r,o);return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?E(Object(n),!0).forEach((function(t){var r,o,i;r=e,o=t,i=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):E(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({columns:i.hXT((function(e){return-1===l.indexOf(e.id)}),t),data:i.UID(i.CEd(l),a)},B)}function S(e,t,n,r){var o=C(e,t,n,r,t.indexOf(e)),a=o.groupIndexFirst,l=o.groupIndexLast;return i.UID((function(e){return e.id}),t.slice(a,l+1))}var B={active_cell:void 0,start_cell:void 0,end_cell:void 0,selected_cells:[]};var _=n(4167),j=n(8102);function P(){P=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(e,t,n,r){var i=t&&t.prototype instanceof A?t:A,a=Object.create(i.prototype),l=new j(r||[]);return o(a,"_invoke",{value:O(e,n,l)}),a}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var d="suspendedStart",f="suspendedYield",h="executing",v="completed",b={};function A(){}function y(){}function g(){}var m={};c(m,a,(function(){return this}));var w=Object.getPrototypeOf,E=w&&w(w(D([])));E&&E!==n&&r.call(E,a)&&(m=E);var C=g.prototype=A.prototype=Object.create(m);function k(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function n(o,i,a,l){var s=p(e[o],e,i);if("throw"!==s.type){var c=s.arg,u=c.value;return u&&"object"==typeof u&&r.call(u,"__await")?t.resolve(u.__await).then((function(e){n("next",e,a,l)}),(function(e){n("throw",e,a,l)})):t.resolve(u).then((function(e){c.value=e,a(c)}),(function(e){return n("throw",e,a,l)}))}l(s.arg)}var i;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return i=i?i.then(o,o):o()}})}function O(t,n,r){var o=d;return function(i,a){if(o===h)throw new Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:e,done:!0}}for(r.method=i,r.arg=a;;){var l=r.delegate;if(l){var s=S(l,r);if(s){if(s===b)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===d)throw o=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var c=p(t,n,r);if("normal"===c.type){if(o=r.done?v:f,c.arg===b)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(o=v,r.method="throw",r.arg=c.arg)}}}function S(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,S(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),b;var i=p(o,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,b;var a=i.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,b):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,b)}function B(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function _(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(B,this),this.reset(!0)}function D(t){if(t||""===t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(typeof t+" is not iterable")}return y.prototype=g,o(C,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:y,configurable:!0}),y.displayName=c(g,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,c(e,s,"GeneratorFunction")),e.prototype=Object.create(C),e},t.awrap=function(e){return{__await:e}},k(x.prototype),c(x.prototype,l,(function(){return this})),t.AsyncIterator=x,t.async=function(e,n,r,o,i){void 0===i&&(i=Promise);var a=new x(u(e,n,r,o),i);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},k(C),c(C,s,"Generator"),c(C,a,(function(){return this})),c(C,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=D,j.prototype={constructor:j,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(_),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return l.type="throw",l.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],l=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,b):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),b},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),_(n),b}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;_(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:D(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),b}},t}function D(e,t,n,r,o,i,a){try{var l=e[i](a),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,o)}function T(e){return function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function a(e){D(i,r,o,a,l,"next",e)}function l(e){D(i,r,o,a,l,"throw",e)}a(void 0)}))}}function I(e){var t=[],n=i.h0F(i.Ed_),r=i.h0F(i.Ed_);return n((function(e,n){var o={};r((function(e,r){o[e]?r===o[e].e.c+1?o[e].e={r:n,c:r}:(t.push(o[e]),o[e]={s:{r:n,c:r},e:{r:n,c:r}}):o[e]={s:{r:n,c:r},e:{r:n,c:r}}}),e);var a=Object.values(o);t=i.zoF(t,a)}),e),i.hXT((function(e){return e.s.c!==e.e.c||e.s.r!==e.e.r}),t)}function F(e,t,n,r,o){return M.apply(this,arguments)}function M(){return(M=T(P().mark((function e(t,n,r,o,a){var l,s,c,u;return P().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,j.Z.xlsx;case 2:return l=e.sent,s=l.utils.aoa_to_sheet([]),n=i.UID(i.eiS(r))(n),o===_.CR.Display||o===_.CR.Names||o===_.CR.None?(l.utils.sheet_add_json(s,t,{skipHeader:!0}),c=t.length>0?{header:r,skipHeader:!0,origin:t.length}:{skipHeader:!0},l.utils.sheet_add_json(s,n,c),o===_.CR.Display&&a&&(s["!merges"]=I(t))):o===_.CR.Ids&&l.utils.sheet_add_json(s,n,{header:r}),u=l.utils.book_new(),l.utils.book_append_sheet(u,s,"SheetJS"),e.abrupt("return",u);case 9:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function R(e,t){return z.apply(this,arguments)}function z(){return(z=T(P().mark((function e(t,n){var r;return P().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,j.Z.xlsx;case 2:r=e.sent,"xlsx"===n?r.writeFile(t,"Data.xlsx",{bookType:"xlsx",type:"buffer"}):"csv"===n&&r.writeFile(t,"Data.csv",{bookType:"csv",type:"buffer"});case 4:case"end":return e.stop()}}),e)})))).apply(this,arguments)}function N(e,t){var n=function(e,t){return e.map((function(e){return e instanceof Array&&e.length<t?e.concat(Array(t-e.length).fill("")):0===t||1===t?[e]:e instanceof String||"string"==typeof e?Array(t).fill(e):e}))}(e,t);return i.p4s(n)}function L(){L=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(e,t,n,r){var i=t&&t.prototype instanceof A?t:A,a=Object.create(i.prototype),l=new j(r||[]);return o(a,"_invoke",{value:O(e,n,l)}),a}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var d="suspendedStart",f="suspendedYield",h="executing",v="completed",b={};function A(){}function y(){}function g(){}var m={};c(m,a,(function(){return this}));var w=Object.getPrototypeOf,E=w&&w(w(P([])));E&&E!==n&&r.call(E,a)&&(m=E);var C=g.prototype=A.prototype=Object.create(m);function k(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function n(o,i,a,l){var s=p(e[o],e,i);if("throw"!==s.type){var c=s.arg,u=c.value;return u&&"object"==typeof u&&r.call(u,"__await")?t.resolve(u.__await).then((function(e){n("next",e,a,l)}),(function(e){n("throw",e,a,l)})):t.resolve(u).then((function(e){c.value=e,a(c)}),(function(e){return n("throw",e,a,l)}))}l(s.arg)}var i;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return i=i?i.then(o,o):o()}})}function O(t,n,r){var o=d;return function(i,a){if(o===h)throw new Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:e,done:!0}}for(r.method=i,r.arg=a;;){var l=r.delegate;if(l){var s=S(l,r);if(s){if(s===b)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===d)throw o=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var c=p(t,n,r);if("normal"===c.type){if(o=r.done?v:f,c.arg===b)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(o=v,r.method="throw",r.arg=c.arg)}}}function S(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,S(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),b;var i=p(o,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,b;var a=i.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,b):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,b)}function B(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function _(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(B,this),this.reset(!0)}function P(t){if(t||""===t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(typeof t+" is not iterable")}return y.prototype=g,o(C,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:y,configurable:!0}),y.displayName=c(g,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,c(e,s,"GeneratorFunction")),e.prototype=Object.create(C),e},t.awrap=function(e){return{__await:e}},k(x.prototype),c(x.prototype,l,(function(){return this})),t.AsyncIterator=x,t.async=function(e,n,r,o,i){void 0===i&&(i=Promise);var a=new x(u(e,n,r,o),i);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},k(C),c(C,s,"Generator"),c(C,a,(function(){return this})),c(C,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=P,j.prototype={constructor:j,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(_),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return l.type="throw",l.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],l=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,b):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),b},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),_(n),b}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;_(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:P(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),b}},t}function q(e,t,n,r,o,i,a){try{var l=e[i](a),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,o)}var U=o().memo((function(e){var t=e.columns,n=e.export_columns,r=e.export_format,i=e.virtual_data,a=e.export_headers,l=e.visibleColumns,s=e.merge_duplicate_headers,c=r===_.T2.Csv||r===_.T2.Xlsx,u=n===_.Pj.Visible?l:t,p=function(){var e,n=(e=L().mark((function e(){var n,o,l,c,p;return L().wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=u.map((function(e){return e.id})),o=u.map((function(e){return e.name})),l=w(t),c=a!==_.CR.None?N(o,l):[],e.next=6,F(c,i.data,n,a,s);case 6:return p=e.sent,e.next=9,R(p,r);case 9:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,o){var i=e.apply(t,n);function a(e){q(i,r,o,a,l,"next",e)}function l(e){q(i,r,o,a,l,"throw",e)}a(void 0)}))});return function(){return n.apply(this,arguments)}}();return o().createElement("div",null,c?o().createElement("button",{className:"export",onClick:p},"Export"):null)}));function W(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function V(e){var t=i.jge("row",e),n=i.jge("column",e);return{minRow:i.u4g(i.VV$,1/0,t),minCol:i.u4g(i.VV$,1/0,n),maxRow:i.u4g(i.Fp7,0,t),maxCol:i.u4g(i.Fp7,0,n)}}function H(e,t){var n=V(t),r=n.minRow,o=n.minCol,i=n.maxRow,a=n.maxCol,l=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],s=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return W(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?W(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(e,2),s=l[0],c=l[1],u=[s,c];return s>i&&(u[0]=r,u[1]=c+1>a?o:c+1),s<r&&(u[0]=i,u[1]=c-1<o?a:c-1),c>a&&(u[1]=o,u[0]=s+1>i?r:s+1),c<o&&(u[1]=a,u[0]=s-1<o?i:s-1),u}function Y(e,t,n,r){var o={row:e,column:t,column_id:n[t].id},i=r.data[e].id;return void 0!==i&&(o.row_id=i),o}function K(e,t,n){var r=e.minRow,o=e.maxRow,a=e.minCol,l=e.maxCol;return(0,i.UID)((function(e){return Y(e[0],e[1],t,n)}),(0,i.icZ)((0,i.w6H)(r,o+1),(0,i.w6H)(a,l+1)))}var Z=function(e){var t=document.createElement("div");t.style.position="absolute",t.style.visibility="hidden",t.style.width="100px",t.style.height="100px",t.style.overflow="scroll";var n=document.createElement("div");return n.style.width="100px",n.style.height="100px",t.appendChild(n),e.appendChild(t),new Promise((function(r){setTimeout((function(){var o=n.clientWidth-t.clientWidth;e.removeChild(t),r(o)}),0)}))};function Q(e,t,n){for(var r=e.length,o=new Array(r),i=0;i<r;++i)o[i]=n(e[i],t[i],i);return o}function $(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function G(e,t){for(var n,r=[],o=function(){var n;t=t.replace(/^\s+/,"");var o=r.slice(-1)[0],a=o?o.lexeme:null,l=e.filter((function(e){return e.if&&(Array.isArray(e.if)?a?e.if&&-1!==e.if.indexOf(a.type):e.if&&-1!==e.if.indexOf(void 0):e.if(r,o))})),s=i.sEJ((function(e){return e.regexp.test(t)}),l);if(!s)return{v:{lexemes:r,valid:!1,error:t}};var c=null!==(n=t.match(s.regexp))&&void 0!==n?n:[],u=c[s.regexpMatch||0],p=c[s.regexpFlags||-1];r.push({lexeme:s,flags:p,value:u}),t=t.substring(u.length)};t.length;)if(n=o())return n.v;var a=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],s=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return $(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?$(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}([void 0,void 0].concat(r).slice(-2),2),l=a[0],s=a[1],c=!s||("function"==typeof s.lexeme.terminal?s.lexeme.terminal(r,l):s.lexeme.terminal);return{lexemes:r,valid:c}}var J=n(4490),X=n.n(J);function ee(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(void 0,"symbol"==typeof(o=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(r.key))?o:String(o)),r)}var o}var te=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n;return t=e,n=[{key:"set",value:function(e,t){e.clipboardData.setData("text/plain",t),e.preventDefault()}},{key:"get",value:function(e){return e.clipboardData?e.clipboardData.getData("text/plain"):void 0}}],null&&ee(t.prototype,null),n&&ee(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),ne=function(e,t){return{success:!0,value:e}};function re(e,t){if((n=(e=t?e.toExponential(t-1):e.toExponential()).indexOf("e"))<0)return null;var n,r=e.slice(0,n);return[r.length>1?r[0]+r.slice(2):r,+e.slice(n+1)]}var oe,ie=/^(?:(.)?([<>=^]))?([+\-( ])?([$#])?(0)?(\d+)?(,)?(\.\d+)?(~)?([a-z%])?$/i;function ae(e){if(!(t=ie.exec(e)))throw new Error("invalid format: "+e);var t;return new le({fill:t[1],align:t[2],sign:t[3],symbol:t[4],zero:t[5],width:t[6],comma:t[7],precision:t[8]&&t[8].slice(1),trim:t[9],type:t[10]})}function le(e){this.fill=void 0===e.fill?" ":e.fill+"",this.align=void 0===e.align?">":e.align+"",this.sign=void 0===e.sign?"-":e.sign+"",this.symbol=void 0===e.symbol?"":e.symbol+"",this.zero=!!e.zero,this.width=void 0===e.width?void 0:+e.width,this.comma=!!e.comma,this.precision=void 0===e.precision?void 0:+e.precision,this.trim=!!e.trim,this.type=void 0===e.type?"":e.type+""}function se(e,t){var n=re(e,t);if(!n)return e+"";var r=n[0],o=n[1];return o<0?"0."+new Array(-o).join("0")+r:r.length>o+1?r.slice(0,o+1)+"."+r.slice(o+1):r+new Array(o-r.length+2).join("0")}ae.prototype=le.prototype,le.prototype.toString=function(){return this.fill+this.align+this.sign+this.symbol+(this.zero?"0":"")+(void 0===this.width?"":Math.max(1,0|this.width))+(this.comma?",":"")+(void 0===this.precision?"":"."+Math.max(0,0|this.precision))+(this.trim?"~":"")+this.type};var ce={"%":function(e,t){return(100*e).toFixed(t)},b:function(e){return Math.round(e).toString(2)},c:function(e){return e+""},d:function(e){return Math.abs(e=Math.round(e))>=1e21?e.toLocaleString("en").replace(/,/g,""):e.toString(10)},e:function(e,t){return e.toExponential(t)},f:function(e,t){return e.toFixed(t)},g:function(e,t){return e.toPrecision(t)},o:function(e){return Math.round(e).toString(8)},p:function(e,t){return se(100*e,t)},r:se,s:function(e,t){var n=re(e,t);if(!n)return e+"";var r=n[0],o=n[1],i=o-(oe=3*Math.max(-8,Math.min(8,Math.floor(o/3))))+1,a=r.length;return i===a?r:i>a?r+new Array(i-a+1).join("0"):i>0?r.slice(0,i)+"."+r.slice(i):"0."+new Array(1-i).join("0")+re(e,Math.max(0,t+i-1))[0]},X:function(e){return Math.round(e).toString(16).toUpperCase()},x:function(e){return Math.round(e).toString(16)}};function ue(e){return e}var pe=Array.prototype.map,de=["y","z","a","f","p","n","µ","m","","k","M","G","T","P","E","Z","Y"];var fe=n(5924),he=n.n(fe),ve=function(e,t){var n=Boolean(t&&t.validation&&t.validation.allow_null),r=be(e);return{success:r&&n,value:r?null:e}},be=function(e){return null==e||"number"==typeof e&&(isNaN(e)||!isFinite(e))},Ae=["group","symbol"];function ye(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var ge=function(e){var t=e.group,n=e.symbol,r=function(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}(e,Ae);return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ye(Object(n),!0).forEach((function(t){var r,o,i;r=e,o=t,i=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ye(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({currency:n,thousands:t},i.CEd(["separate_4digits","symbol"],r))};function me(e,t){return he()(e)?{success:!0,value:+e}:ve(e,t)}function we(e,t){return"number"!=typeof e||be(e)?ve(e,t):{success:!0,value:e}}function Ee(e,t){return be(e)?ve(e,t):"string"==typeof e?{success:!0,value:e}:{success:!0,value:JSON.stringify(e)}}function Ce(e,t){return"string"==typeof e?{success:!0,value:e}:ve(e,t)}var ke=/^\s*(-?\d{4}|\d{2})(-(\d{1,2})(-(\d{1,2})([ Tt]([01]?\d|2[0-3])(:([0-5]\d)(:([0-5]\d(\.\d+)?))?(Z|z|[+\-]\d{2}:?\d{2})?)?)?)?)?\s*$/m,xe=(new Date).getFullYear()-70;function Oe(e,t){if("string"!=typeof e)return null;var n=e.match(ke);if(!n)return null;var r=n[1],o=2===r.length;if(o&&(!t||!t.allow_YY))return null;var i=o?(Number(r)+2e3-xe)%100+xe:Number(r),a=i<0,l=n[3],s=Number(l||"1")-1,c=n[5],u=Number(c||1),p=n[7],d=Number(p||0),f=n[9],h=Number(f||0),v=n[11],b=new Date(Date.UTC(2e3,s,u,d,h));if(b.setUTCFullYear(i),b.getUTCMonth()!==s||b.getUTCDate()!==u)return null;var A=v?29:f?16:p?13:c?10:l?7:4;return(a?"-":"")+(b.toISOString().substr(a?3:0,17).replace("T"," ")+(v||"")).substr(0,A)}function Se(e,t){var n=Oe(e,t&&t.validation);return null!==n?{success:!0,value:n}:ve(e,t)}function Be(e,t){return"string"==typeof e&&null!==Oe(e,t&&t.validation)?{success:!0,value:e.trim()}:ve(e,t)}function _e(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function je(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?_e(Object(n),!0).forEach((function(t){var r,o,i;r=e,o=t,i=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_e(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Pe=function(e,t){var n=function(e,t){var n=t&&t.on_change&&t.on_change.action||_.KI.Coerce;switch(n){case _.KI.Coerce:return je({action:n},function(e){switch(e.type){case _.QD.Numeric:return me;case _.QD.Text:return Ee;case _.QD.Datetime:return Se;case _.QD.Any:default:return ne}}(t)(e,t));case _.KI.None:return{success:!0,value:e,action:n};case _.KI.Validate:return je({action:n},function(e){switch(e.type){case _.QD.Numeric:return we;case _.QD.Text:return Ce;case _.QD.Datetime:return Be;case _.QD.Any:default:return ne}}(t)(e,t))}}(e,t);return n.success?n:function(e,t){var n=t&&t.on_change&&t.on_change.failure||_.UT.Reject;if(e.failure=n,n===_.UT.Default){var r=t&&t.validation&&t.validation.default,o=i.kKJ(r)?null:r;e.success=!0,e.value=o}else n===_.UT.Accept&&(e.success=!0);return e}(n,t)};function De(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],s=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||Ie(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Te(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=Ie(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0,o=function(){};return{s:o,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:o}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var i,a=!0,l=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return a=e.done,e},e:function(e){l=!0,i=e},f:function(){try{a||null==n.return||n.return()}finally{if(l)throw i}}}}function Ie(e,t){if(e){if("string"==typeof e)return Fe(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Fe(e,t):void 0}}function Fe(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Me(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,ze(r.key),r)}}function Re(e,t,n){return(t=ze(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ze(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var Ne=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n;return t=e,n=[{key:"toClipboard",value:function(t,n,r,o,a,s){var c=i.jj$(i.jge("row",n).sort((function(e,t){return e-t}))),u=i.jj$(i.jge("column",n).sort((function(e,t){return e-t}))),p=i.tPi(i.YMb(c),i.Z$Q(c)+1,a).map((function(e){return i.NQ5(u,i.NQ5(i.jge("id",o),e))})),d=X().prototype.stringify(p);if(e.lastLocalCopy=p,s){var f=N(i.jge("name",o),w(r)),h=i.UID((function(e){return i.UID((function(t){return e[t]}),u)}),f).concat(p);d=X().prototype.stringify(h),e.lastLocalCopy=h,e.localCopyWithoutHeaders=p}l.ZP.trace("TableClipboard -- set clipboard data: ",d),te.set(t,d)}},{key:"clearClipboard",value:function(){e.lastLocalCopy=[],e.localCopyWithoutHeaders=[]}},{key:"fromClipboard",value:function(t,n,r,o,a,s){var c=!(arguments.length>6&&void 0!==arguments[6])||arguments[6],u=!(arguments.length>7&&void 0!==arguments[7])||arguments[7],p=arguments.length>8?arguments[8]:void 0,d=te.get(t);if(l.ZP.trace("TableClipboard -- get clipboard data: ",d),d){var f=X().prototype.stringify(e.lastLocalCopy),h=p?e.localCopyWithoutHeaders:e.lastLocalCopy;return function(e,t,n,r,o,a){var s=!(arguments.length>6&&void 0!==arguments[6])||arguments[6],c=!(arguments.length>7&&void 0!==arguments[7])||arguments[7];c||l.ZP.debug("Clipboard -- Sorting or filtering active, do not create new rows"),s||l.ZP.debug("Clipboard -- Do not create new columns");var u=i.d9v(a),p=r.slice(0),d=o.slice(0);if(s&&e[0].length+t.column>=o.length){for(var f=[],h=function(e){f.push({id:"Column ".concat(e+1),name:"Column ".concat(e+1),type:_.QD.Any,sort_as_null:[]}),u.forEach((function(t){return t["Column ".concat(e)]=""}))},v=o.length;v<e[0].length+t.column;v++)h(v);p=i.cZv(i.cq5(i.Z$Q(o),r)+1,f,p),d=i.zoF(d,f)}var b=n[t.row];if(c&&e.length+b>=a.length){var A={};o.forEach((function(e){return A[e.id]=""})),u=i.zoF(u,i.rx1(A,e.length+b-a.length))}var y,g=n.slice(-1)[0]||0,m=n.length,w=Te(e.entries());try{for(w.s();!(y=w.n()).done;){var E,C=De(y.value,2),k=C[0],x=Te(C[1].entries());try{for(x.s();!(E=x.n()).done;){var O=De(E.value,2),S=O[0],B=O[1],j=t.row+k,P=m>j?n[j]:c?g+(j-m+1):void 0;if(void 0!==P){var D=d[t.column+S];if(D&&D.editable){var T=Pe(B,D);T.success&&(u=i.t8m(i.QMA([P,D.id]),T.value,u))}}}}catch(e){x.e(e)}finally{x.f()}}}catch(e){w.e(e)}finally{w.f()}return{data:u,columns:p}}(f===d?h:e.parse(d),n,r,o,a,s,c,u)}}},{key:"parse",value:function(t){var n,r,o,i,a,l,s=0,c=[[]],u=t.split("\n");for(u.length>1&&""===u[u.length-1]&&u.pop(),c=[],n=0,r=u.length;n<r;n+=1){var p=u[n].split("\t");for(o=0,i=p.length;o<i;o+=1)c[s]||(c[s]=[]),a&&0===o?(l=c[s].length-1,c[s][l]=c[s][l]+"\n"+p[0].replace(/""/g,'"'),a&&1&e.countQuotes(p[0])&&(a=!1,c[s][l]=c[s][l].substring(0,c[s][l].length-1))):o===i-1&&0===p[o].indexOf('"')&&1&e.countQuotes(p[o])?(c[s].push(p[o].substring(1).replace(/""/g,'"')),a=!0):(c[s].push(p[o]),a=!1);a||(s+=1)}return c}},{key:"countQuotes",value:function(e){return e.split('"').length-1}}],null&&Me(t.prototype,null),n&&Me(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();Re(Ne,"lastLocalCopy",[[]]),Re(Ne,"localCopyWithoutHeaders",[[]]);var Le=Ne,qe=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;if(t=t||function(){for(t=e;t&&"td"!==t.nodeName.toLowerCase();)t=t.parentElement;return t}(),e&&t){for(var n=e;"relative"!==getComputedStyle(n).position&&"sticky"!==getComputedStyle(n).position&&n.parentElement;)n=n.parentElement;for(var r=e;"relative"!==getComputedStyle(r).position&&r.parentElement;)r=r.parentElement;var o=n.getBoundingClientRect(),i=r.getBoundingClientRect(),a=t.getBoundingClientRect(),l=a.left-o.left+n.scrollLeft,s=a.top-o.top+n.scrollTop+a.height;e.style.width="".concat(a.width,"px"),e.style.top="".concat(s,"px"),e.style.left="".concat(l,"px"),e.style.position="absolute",a.top+a.height/2>i.bottom||a.top-a.height/2<i.top||a.left<i.left||a.left+.25*a.width>i.left+i.width?e.style.display="none":e.style.display="block"}},Ue=function(e,t,n){return"last"===n?e===t:"first"===n?0===e:"boolean"==typeof n?n:!!n&&n[e]},We=(0,a.Pi)((function(e,t,n){var r=function(e,t){return i.UID((function(n){return e.map((function(e){return i.kKJ(e.name)&&n===t-1?e.id:function(e,t){return Array.isArray(e.name)?e.name[t]:e.name}(e,n)}))}),i.w6H(0,t))}(t,w(e)),o=function(e,t,n){return i.UID((function(t){if(n){var r=[0],o=0;return t.forEach((function(e,n){e!==t[o]&&(r.push(n),o=n)})),r}return i.w6H(0,e.length)}),t)}(t,r,n);return i.$Re(r,o)}));function Ve(e){for(var t=e.length,n=new Array(t),r=0;r<t;++r)n[r]=e[r].slice(0);return n}function He(e,t,n){for(var r=e.length,o=t.length,i=0;i<r;++i)for(var a=0;a<o;++a)n(e[i],t[a],i,a)}function Ye(e,t,n){for(var r=e.length,o=t.length,i=new Array(r),a=0;a<r;++a){for(var l=new Array(o),s=0;s<o;++s)l[s]=n(e[a],t[s],a,s);i[a]=l}return i}function Ke(e,t,n){for(var r=e.length,o=new Array(r),i=0;i<r;++i){for(var a=e[i].length,l=new Array(a),s=0;s<a;++s)l[s]=n(e[i][s],t?t[i][s]:void 0,i,s);o[i]=l}return o}function Ze(e,t,n,r){for(var o=e.length,i=new Array(o),a=0;a<o;++a){for(var l=e[a].length,s=new Array(l),c=0;c<l;++c)s[c]=r(e[a][c],t?t[a][c]:void 0,n?n[a][c]:void 0,a,c);i[a]=s}return i}function Qe(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var o=n.slice(0,-1);return i.u4g((function(e,t){return e.get(t)||e.set(t,new Map).get(t)}),e,o)}var $e=function(){return function(e){var t=new Map;return{get:function(){for(var n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];var i=r.slice(-1)[0],l=Qe.apply(void 0,[t].concat(r));return l.get(i)||l.set(i,(0,a.qe)(e)).get(i)}}}};function Ge(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Je(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Xe(r.key),r)}}function Xe(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}function et(e,t){return et=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},et(e,t)}function tt(e){return tt=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},tt(e)}var nt,rt=/^children$/,ot=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&et(e,t)}(s,e);var t,n,r,a,l=(r=s,a=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=tt(r);if(a){var n=tt(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,e)});function s(e){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),l.call(this,e)}return t=s,n=[{key:"propsWithDefaults",get:function(){return this.props}},{key:"render",value:function(){var e=this.propsWithDefaults,t=e.attributes,n=e.className,r=e.onClick,i=e.onDoubleClick,a=e.onMouseEnter,l=e.onMouseLeave,s=e.onMouseMove,c=e.style;return o().createElement("td",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ge(Object(n),!0).forEach((function(t){var r,o,i;r=e,o=t,i=n[t],(o=Xe(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ge(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({ref:"td",children:this.props.children,tabIndex:-1,className:n,onClick:r,onDoubleClick:i,onMouseEnter:a,onMouseLeave:l,onMouseMove:s,onMouseOver:s,style:c},t))}},{key:"shouldComponentUpdate",value:function(e){var t=this.props,n=this.getChildProps(t),r=this.getChildProps(e);return i.YjB((function(n){return!rt.test(n)&&t[n]!==e[n]}),i.p8H(t))||i.YjB((function(e){return n[e]!==r[e]}),i.p8H(n))}},{key:"getChildProps",value:function(e){return e&&e.children&&e.children[0]&&e.children[0].props}}],n&&Je(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),s}(r.Component),it=function(e,t,n){return!!e&&e.row===t&&e.column===n},at=function(e,t,n,r){var o=e(),a=o.cell_selectable,l=o.selected_cells,s=o.active_cell,c=o.setProps,u=o.viewport,p=o.virtualized,d=o.visibleColumns,f=n+p.offset.columns,h=Y(t,f,d,u);if((!s||t!==s.row||f!==s.column)&&(d[f].presentation!==_.Ap.Markdown&&r.preventDefault(),a)){var v=window.getSelection();v&&v.removeAllRanges();var b=function(e,t,n){return i.YjB((function(e){return e.row===t&&e.column===n}),e)}(l,t,f);if(!b||r.shiftKey){var A={is_focused:!1,end_cell:h};r.shiftKey&&s?A.selected_cells=K({minRow:(0,i.VV$)(t,s.row),maxRow:(0,i.Fp7)(t,s.row),minCol:(0,i.VV$)(f,s.column),maxCol:(0,i.Fp7)(f,s.column)},d,u):(A.active_cell=h,A.start_cell=h,A.selected_cells=[h]),c(A)}else c({is_focused:!1,active_cell:h})}},lt=function(e,t,n,r){var o=e(),i=o.is_focused,a=o.setProps,l=o.viewport,s=o.virtualized,c=o.visibleColumns;if(c[n].editable){var u=Y(t,n+s.offset.columns,c,l);i||(r.preventDefault(),a({selected_cells:[u],active_cell:u,start_cell:u,end_cell:u,is_focused:!0}))}},st=function(e,t,n,r){var o=e(),a=o.data,l=o.setProps,s=o.virtualized,c=o.visibleColumns[n],u=s.indices[t-s.offset.rows];if(c.editable){var p=Pe(r,c);p.success&&l({data:(0,i.t8m)((0,i.QMA)([u,c.id]),p.value,a)})}},ct=function(e,t,n){var r=e(),o=r.setState,i=r.virtualized;o({currentTooltip:{header:!1,id:r.visibleColumns[n].id,row:i.indices[t-i.offset.rows]}})},ut=function(e,t,n){var r=e();(0,r.setState)({currentTooltip:{header:!0,id:r.visibleColumns[n].id,row:t}})},pt=function(e,t,n){(0,e().setState)({currentTooltip:void 0})},dt=function(e,t,n){var r=e(),o=r.currentTooltip,i=r.setState,a=r.virtualized,l=r.visibleColumns[n],s=a.indices[t-a.offset.rows];o&&o.id===l.id&&o.row===s&&!o.header||i({currentTooltip:{header:!1,id:l.id,row:s}})},ft=function(e,t,n){var r=e(),o=r.currentTooltip,i=r.setState,a=r.visibleColumns[n];o&&o.id===a.id&&o.row===t&&o.header||i({currentTooltip:{header:!0,id:a.id,row:t}})},ht=function(e,t,n,r){var o=e(),i=o.active_cell,a=o.is_focused,l=it(i,t,n);if(!a&&l){r.preventDefault();var s=r.target;s.setSelectionRange(0,s.value?s.value.length:0)}},vt=function(e,t,n,r){r.preventDefault()};function bt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,gt(r.key),r)}}function At(e,t,n){return t&&bt(e.prototype,t),n&&bt(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function yt(e,t,n){return(t=gt(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function gt(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}!function(e){e.Change="change",e.Click="click",e.DoubleClick="doubleclick",e.Enter="enter",e.EnterHeader="enterheader",e.Leave="leave",e.Move="move",e.MoveHeader="moveheader",e.MouseUp="mouseup",e.Paste="paste"}(nt||(nt={}));var mt=function(e){return new wt(e).get},wt=At((function e(t){var n,r,o=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),yt(this,"propsFn",void 0),yt(this,"cache",(n=function(e,t,n){switch(e){case nt.Change:return st.bind(void 0,o.propsFn,t,n);case nt.Click:return at.bind(void 0,o.propsFn,t,n);case nt.DoubleClick:return lt.bind(void 0,o.propsFn,t,n);case nt.Enter:return ct.bind(void 0,o.propsFn,t,n);case nt.EnterHeader:return ut.bind(void 0,o.propsFn,t,n);case nt.Leave:return pt.bind(void 0,o.propsFn,t,n);case nt.Move:return dt.bind(void 0,o.propsFn,t,n);case nt.MoveHeader:return ft.bind(void 0,o.propsFn,t,n);case nt.MouseUp:return ht.bind(void 0,o.propsFn,t,n);case nt.Paste:return vt.bind(void 0,o.propsFn,t,n);default:throw new Error("unexpected handler ".concat(e))}},r=new Map,{get:function(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];var i=t.slice(-1)[0],a=Qe.apply(void 0,[r].concat(t));return a.has(i)?a.get(i):a.set(i,n.apply(void 0,t)).get(i)}})),yt(this,"get",(function(e,t,n){return o.cache.get(e,t,n)})),this.propsFn=t}));function Et(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,kt(r.key),r)}}function Ct(e,t,n){return(t=kt(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function kt(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var xt=function(){function e(t){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:mt(t);!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Ct(this,"handlers",void 0),Ct(this,"partialGet",(0,a.qe)((function(e,t,r){return t.map((function(t,o){return e.map((function(e,t){return n.getWrapper(!1,!1,o+r.rows,t,e)}),e)}))}))),Ct(this,"get",(0,a.qe)((function(e,t,n,r){return e=Ve(e),(r.length?r:n?[n]:[]).forEach((function(r){var i=r.row,a=r.column;if(i-=t.rows,a-=t.columns,!(i<0||a<0||e.length<=i||e[i].length<=a)){var l=e[i][a],s=it(n,i+t.rows,a+t.columns);e[i][a]=o().cloneElement(l,{className:l.props.className+" cell--selected"+(s?" focused":"")})}})),e}))),Ct(this,"wrapper",$e()((function(e,t,n,r,i,a,l,s,c,u){return o().createElement(ot,{active:e,attributes:{"data-dash-column":r,"data-dash-row":i},className:t,key:"column-".concat(n),onClick:c,onDoubleClick:u,onMouseEnter:a,onMouseLeave:l,onMouseMove:s})}))),this.handlers=r}var t,n;return t=e,n=[{key:"getWrapper",value:function(e,t,n,r,o){var i=o.presentation===_.Ap.Dropdown,a="dash-cell"+" column-".concat(r)+(e?" focused":"")+(t?" cell--selected":"")+(i?" dropdown":"");return this.wrapper.get(n,r)(e,a,r,o.id,n,this.handlers(nt.Enter,n,r),this.handlers(nt.Leave,n,r),this.handlers(nt.Move,n,r),this.handlers(nt.Click,n,r),this.handlers(nt.DoubleClick,n,r))}}],n&&Et(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Ot(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Pt(r.key),r)}}function St(e,t){return St=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},St(e,t)}function Bt(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _t(e){return _t=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},_t(e)}function jt(e,t,n){return(t=Pt(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Pt(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var Dt=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&St(e,t)}(l,e);var t,n,r,i,a=(r=l,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=_t(r);if(i){var n=_t(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Bt(e)}(this,e)});function l(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),jt(Bt(t=a.call(this,e)),"propagateChange",(function(){t.state.value!==t.props.value&&(0,t.props.onChange)(t.state.value)})),jt(Bt(t),"handleChange",(function(e){t.setState({value:e.target.value})})),jt(Bt(t),"handleKeyDown",(function(e){var n=t.props.focused;n&&e.keyCode!==v.TAB&&e.keyCode!==v.ENTER||(n||y(e.keyCode))&&t.propagateChange()})),t.state={value:e.value},t}return t=l,(n=[{key:"render",value:function(){var e=this.props,t=e.className,n=e.onMouseUp,r=e.onPaste,i=e.value,a=null===this.state.value?void 0:this.state.value;return o().createElement("div",{className:"dash-input-cell-value-container dash-cell-value-container"},o().createElement("div",{className:"input-cell-value-shadow cell-value-shadow"},i),o().createElement("input",{ref:"textInput",type:"text",className:t,onBlur:this.propagateChange,onChange:this.handleChange,onKeyDown:this.handleKeyDown,onMouseUp:n,onPaste:r,value:a}))}},{key:"UNSAFE_componentWillReceiveProps",value:function(e){var t=e.value;this.state.value!==t&&this.setState({value:t})}},{key:"componentDidUpdate",value:function(){this.setFocus()}},{key:"componentDidMount",value:function(){this.setFocus()}},{key:"setFocus",value:function(){var e=this.props,t=e.active,n=e.applyFocus;if(t){var r=this.refs.textInput;n&&r&&document.activeElement!==r&&(r.focus(),r.setSelectionRange(0,r.value?r.value.length:0))}}}])&&Ot(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),l}(r.PureComponent);function Tt(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(void 0,"symbol"==typeof(o=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(r.key))?o:String(o)),r)}var o}function It(e,t){return It=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},It(e,t)}function Ft(e){return Ft=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ft(e)}var Mt=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&It(e,t)}(l,e);var t,n,r,i,a=(r=l,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Ft(r);if(i){var n=Ft(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,e)});function l(){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),a.apply(this,arguments)}return t=l,(n=[{key:"render",value:function(){var e=this.props,t=e.className,n=e.value;return o().createElement("div",{ref:"el",className:t,tabIndex:-1},"boolean"==typeof n?n.toString():n)}},{key:"componentDidUpdate",value:function(){this.setFocus()}},{key:"componentDidMount",value:function(){this.setFocus()}},{key:"setFocus",value:function(){var e=this.props,t=e.active,n=e.applyFocus;if(t){var r,o=this.refs.el;n&&o&&document.activeElement!==o&&(null===(r=window.getSelection())||void 0===r||r.selectAllChildren(o),o.focus())}}}])&&Tt(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),l}(r.PureComponent),Rt=n(5639),zt=n(4184),Nt=n.n(zt),Lt=n(9064),qt=n.n(Lt),Ut=n(1850),Wt=n.n(Ut),Vt=function(e){var t=e.onMouseDown;return o().createElement("span",{className:"Select-arrow",onMouseDown:t})};Vt.propTypes={onMouseDown:qt().func};var Ht=[{base:"A",letters:/[\u0041\u24B6\uFF21\u00C0\u00C1\u00C2\u1EA6\u1EA4\u1EAA\u1EA8\u00C3\u0100\u0102\u1EB0\u1EAE\u1EB4\u1EB2\u0226\u01E0\u00C4\u01DE\u1EA2\u00C5\u01FA\u01CD\u0200\u0202\u1EA0\u1EAC\u1EB6\u1E00\u0104\u023A\u2C6F]/g},{base:"AA",letters:/[\uA732]/g},{base:"AE",letters:/[\u00C6\u01FC\u01E2]/g},{base:"AO",letters:/[\uA734]/g},{base:"AU",letters:/[\uA736]/g},{base:"AV",letters:/[\uA738\uA73A]/g},{base:"AY",letters:/[\uA73C]/g},{base:"B",letters:/[\u0042\u24B7\uFF22\u1E02\u1E04\u1E06\u0243\u0182\u0181]/g},{base:"C",letters:/[\u0043\u24B8\uFF23\u0106\u0108\u010A\u010C\u00C7\u1E08\u0187\u023B\uA73E]/g},{base:"D",letters:/[\u0044\u24B9\uFF24\u1E0A\u010E\u1E0C\u1E10\u1E12\u1E0E\u0110\u018B\u018A\u0189\uA779]/g},{base:"DZ",letters:/[\u01F1\u01C4]/g},{base:"Dz",letters:/[\u01F2\u01C5]/g},{base:"E",letters:/[\u0045\u24BA\uFF25\u00C8\u00C9\u00CA\u1EC0\u1EBE\u1EC4\u1EC2\u1EBC\u0112\u1E14\u1E16\u0114\u0116\u00CB\u1EBA\u011A\u0204\u0206\u1EB8\u1EC6\u0228\u1E1C\u0118\u1E18\u1E1A\u0190\u018E]/g},{base:"F",letters:/[\u0046\u24BB\uFF26\u1E1E\u0191\uA77B]/g},{base:"G",letters:/[\u0047\u24BC\uFF27\u01F4\u011C\u1E20\u011E\u0120\u01E6\u0122\u01E4\u0193\uA7A0\uA77D\uA77E]/g},{base:"H",letters:/[\u0048\u24BD\uFF28\u0124\u1E22\u1E26\u021E\u1E24\u1E28\u1E2A\u0126\u2C67\u2C75\uA78D]/g},{base:"I",letters:/[\u0049\u24BE\uFF29\u00CC\u00CD\u00CE\u0128\u012A\u012C\u0130\u00CF\u1E2E\u1EC8\u01CF\u0208\u020A\u1ECA\u012E\u1E2C\u0197]/g},{base:"J",letters:/[\u004A\u24BF\uFF2A\u0134\u0248]/g},{base:"K",letters:/[\u004B\u24C0\uFF2B\u1E30\u01E8\u1E32\u0136\u1E34\u0198\u2C69\uA740\uA742\uA744\uA7A2]/g},{base:"L",letters:/[\u004C\u24C1\uFF2C\u013F\u0139\u013D\u1E36\u1E38\u013B\u1E3C\u1E3A\u0141\u023D\u2C62\u2C60\uA748\uA746\uA780]/g},{base:"LJ",letters:/[\u01C7]/g},{base:"Lj",letters:/[\u01C8]/g},{base:"M",letters:/[\u004D\u24C2\uFF2D\u1E3E\u1E40\u1E42\u2C6E\u019C]/g},{base:"N",letters:/[\u004E\u24C3\uFF2E\u01F8\u0143\u00D1\u1E44\u0147\u1E46\u0145\u1E4A\u1E48\u0220\u019D\uA790\uA7A4]/g},{base:"NJ",letters:/[\u01CA]/g},{base:"Nj",letters:/[\u01CB]/g},{base:"O",letters:/[\u004F\u24C4\uFF2F\u00D2\u00D3\u00D4\u1ED2\u1ED0\u1ED6\u1ED4\u00D5\u1E4C\u022C\u1E4E\u014C\u1E50\u1E52\u014E\u022E\u0230\u00D6\u022A\u1ECE\u0150\u01D1\u020C\u020E\u01A0\u1EDC\u1EDA\u1EE0\u1EDE\u1EE2\u1ECC\u1ED8\u01EA\u01EC\u00D8\u01FE\u0186\u019F\uA74A\uA74C]/g},{base:"OI",letters:/[\u01A2]/g},{base:"OO",letters:/[\uA74E]/g},{base:"OU",letters:/[\u0222]/g},{base:"P",letters:/[\u0050\u24C5\uFF30\u1E54\u1E56\u01A4\u2C63\uA750\uA752\uA754]/g},{base:"Q",letters:/[\u0051\u24C6\uFF31\uA756\uA758\u024A]/g},{base:"R",letters:/[\u0052\u24C7\uFF32\u0154\u1E58\u0158\u0210\u0212\u1E5A\u1E5C\u0156\u1E5E\u024C\u2C64\uA75A\uA7A6\uA782]/g},{base:"S",letters:/[\u0053\u24C8\uFF33\u1E9E\u015A\u1E64\u015C\u1E60\u0160\u1E66\u1E62\u1E68\u0218\u015E\u2C7E\uA7A8\uA784]/g},{base:"T",letters:/[\u0054\u24C9\uFF34\u1E6A\u0164\u1E6C\u021A\u0162\u1E70\u1E6E\u0166\u01AC\u01AE\u023E\uA786]/g},{base:"TZ",letters:/[\uA728]/g},{base:"U",letters:/[\u0055\u24CA\uFF35\u00D9\u00DA\u00DB\u0168\u1E78\u016A\u1E7A\u016C\u00DC\u01DB\u01D7\u01D5\u01D9\u1EE6\u016E\u0170\u01D3\u0214\u0216\u01AF\u1EEA\u1EE8\u1EEE\u1EEC\u1EF0\u1EE4\u1E72\u0172\u1E76\u1E74\u0244]/g},{base:"V",letters:/[\u0056\u24CB\uFF36\u1E7C\u1E7E\u01B2\uA75E\u0245]/g},{base:"VY",letters:/[\uA760]/g},{base:"W",letters:/[\u0057\u24CC\uFF37\u1E80\u1E82\u0174\u1E86\u1E84\u1E88\u2C72]/g},{base:"X",letters:/[\u0058\u24CD\uFF38\u1E8A\u1E8C]/g},{base:"Y",letters:/[\u0059\u24CE\uFF39\u1EF2\u00DD\u0176\u1EF8\u0232\u1E8E\u0178\u1EF6\u1EF4\u01B3\u024E\u1EFE]/g},{base:"Z",letters:/[\u005A\u24CF\uFF3A\u0179\u1E90\u017B\u017D\u1E92\u1E94\u01B5\u0224\u2C7F\u2C6B\uA762]/g},{base:"a",letters:/[\u0061\u24D0\uFF41\u1E9A\u00E0\u00E1\u00E2\u1EA7\u1EA5\u1EAB\u1EA9\u00E3\u0101\u0103\u1EB1\u1EAF\u1EB5\u1EB3\u0227\u01E1\u00E4\u01DF\u1EA3\u00E5\u01FB\u01CE\u0201\u0203\u1EA1\u1EAD\u1EB7\u1E01\u0105\u2C65\u0250]/g},{base:"aa",letters:/[\uA733]/g},{base:"ae",letters:/[\u00E6\u01FD\u01E3]/g},{base:"ao",letters:/[\uA735]/g},{base:"au",letters:/[\uA737]/g},{base:"av",letters:/[\uA739\uA73B]/g},{base:"ay",letters:/[\uA73D]/g},{base:"b",letters:/[\u0062\u24D1\uFF42\u1E03\u1E05\u1E07\u0180\u0183\u0253]/g},{base:"c",letters:/[\u0063\u24D2\uFF43\u0107\u0109\u010B\u010D\u00E7\u1E09\u0188\u023C\uA73F\u2184]/g},{base:"d",letters:/[\u0064\u24D3\uFF44\u1E0B\u010F\u1E0D\u1E11\u1E13\u1E0F\u0111\u018C\u0256\u0257\uA77A]/g},{base:"dz",letters:/[\u01F3\u01C6]/g},{base:"e",letters:/[\u0065\u24D4\uFF45\u00E8\u00E9\u00EA\u1EC1\u1EBF\u1EC5\u1EC3\u1EBD\u0113\u1E15\u1E17\u0115\u0117\u00EB\u1EBB\u011B\u0205\u0207\u1EB9\u1EC7\u0229\u1E1D\u0119\u1E19\u1E1B\u0247\u025B\u01DD]/g},{base:"f",letters:/[\u0066\u24D5\uFF46\u1E1F\u0192\uA77C]/g},{base:"g",letters:/[\u0067\u24D6\uFF47\u01F5\u011D\u1E21\u011F\u0121\u01E7\u0123\u01E5\u0260\uA7A1\u1D79\uA77F]/g},{base:"h",letters:/[\u0068\u24D7\uFF48\u0125\u1E23\u1E27\u021F\u1E25\u1E29\u1E2B\u1E96\u0127\u2C68\u2C76\u0265]/g},{base:"hv",letters:/[\u0195]/g},{base:"i",letters:/[\u0069\u24D8\uFF49\u00EC\u00ED\u00EE\u0129\u012B\u012D\u00EF\u1E2F\u1EC9\u01D0\u0209\u020B\u1ECB\u012F\u1E2D\u0268\u0131]/g},{base:"j",letters:/[\u006A\u24D9\uFF4A\u0135\u01F0\u0249]/g},{base:"k",letters:/[\u006B\u24DA\uFF4B\u1E31\u01E9\u1E33\u0137\u1E35\u0199\u2C6A\uA741\uA743\uA745\uA7A3]/g},{base:"l",letters:/[\u006C\u24DB\uFF4C\u0140\u013A\u013E\u1E37\u1E39\u013C\u1E3D\u1E3B\u017F\u0142\u019A\u026B\u2C61\uA749\uA781\uA747]/g},{base:"lj",letters:/[\u01C9]/g},{base:"m",letters:/[\u006D\u24DC\uFF4D\u1E3F\u1E41\u1E43\u0271\u026F]/g},{base:"n",letters:/[\u006E\u24DD\uFF4E\u01F9\u0144\u00F1\u1E45\u0148\u1E47\u0146\u1E4B\u1E49\u019E\u0272\u0149\uA791\uA7A5]/g},{base:"nj",letters:/[\u01CC]/g},{base:"o",letters:/[\u006F\u24DE\uFF4F\u00F2\u00F3\u00F4\u1ED3\u1ED1\u1ED7\u1ED5\u00F5\u1E4D\u022D\u1E4F\u014D\u1E51\u1E53\u014F\u022F\u0231\u00F6\u022B\u1ECF\u0151\u01D2\u020D\u020F\u01A1\u1EDD\u1EDB\u1EE1\u1EDF\u1EE3\u1ECD\u1ED9\u01EB\u01ED\u00F8\u01FF\u0254\uA74B\uA74D\u0275]/g},{base:"oi",letters:/[\u01A3]/g},{base:"ou",letters:/[\u0223]/g},{base:"oo",letters:/[\uA74F]/g},{base:"p",letters:/[\u0070\u24DF\uFF50\u1E55\u1E57\u01A5\u1D7D\uA751\uA753\uA755]/g},{base:"q",letters:/[\u0071\u24E0\uFF51\u024B\uA757\uA759]/g},{base:"r",letters:/[\u0072\u24E1\uFF52\u0155\u1E59\u0159\u0211\u0213\u1E5B\u1E5D\u0157\u1E5F\u024D\u027D\uA75B\uA7A7\uA783]/g},{base:"s",letters:/[\u0073\u24E2\uFF53\u00DF\u015B\u1E65\u015D\u1E61\u0161\u1E67\u1E63\u1E69\u0219\u015F\u023F\uA7A9\uA785\u1E9B]/g},{base:"t",letters:/[\u0074\u24E3\uFF54\u1E6B\u1E97\u0165\u1E6D\u021B\u0163\u1E71\u1E6F\u0167\u01AD\u0288\u2C66\uA787]/g},{base:"tz",letters:/[\uA729]/g},{base:"u",letters:/[\u0075\u24E4\uFF55\u00F9\u00FA\u00FB\u0169\u1E79\u016B\u1E7B\u016D\u00FC\u01DC\u01D8\u01D6\u01DA\u1EE7\u016F\u0171\u01D4\u0215\u0217\u01B0\u1EEB\u1EE9\u1EEF\u1EED\u1EF1\u1EE5\u1E73\u0173\u1E77\u1E75\u0289]/g},{base:"v",letters:/[\u0076\u24E5\uFF56\u1E7D\u1E7F\u028B\uA75F\u028C]/g},{base:"vy",letters:/[\uA761]/g},{base:"w",letters:/[\u0077\u24E6\uFF57\u1E81\u1E83\u0175\u1E87\u1E85\u1E98\u1E89\u2C73]/g},{base:"x",letters:/[\u0078\u24E7\uFF58\u1E8B\u1E8D]/g},{base:"y",letters:/[\u0079\u24E8\uFF59\u1EF3\u00FD\u0177\u1EF9\u0233\u1E8F\u00FF\u1EF7\u1E99\u1EF5\u01B4\u024F\u1EFF]/g},{base:"z",letters:/[\u007A\u24E9\uFF5A\u017A\u1E91\u017C\u017E\u1E93\u1E95\u01B6\u0225\u0240\u2C6C\uA763]/g}],Yt=function(e){for(var t=0;t<Ht.length;t++)e=e.replace(Ht[t].letters,Ht[t].base);return e},Kt=function(e){return null!=e&&""!==e},Zt=function(e,t,n,r){return r.ignoreAccents&&(t=Yt(t)),r.ignoreCase&&(t=t.toLowerCase()),r.trimFilter&&(t=t.replace(/^\s+|\s+$/g,"")),n&&(n=n.map((function(e){return e[r.valueKey]}))),e.filter((function(e){if(n&&n.indexOf(e[r.valueKey])>-1)return!1;if(r.filterOption)return r.filterOption.call(void 0,e,t);if(!t)return!0;var o=e[r.valueKey],i=e[r.labelKey],a=Kt(o),l=Kt(i);if(!a&&!l)return!1;var s=a?String(o):null,c=l?String(i):null;return r.ignoreAccents&&(s&&"label"!==r.matchProp&&(s=Yt(s)),c&&"value"!==r.matchProp&&(c=Yt(c))),r.ignoreCase&&(s&&"label"!==r.matchProp&&(s=s.toLowerCase()),c&&"value"!==r.matchProp&&(c=c.toLowerCase())),"start"===r.matchPos?s&&"label"!==r.matchProp&&s.substr(0,t.length)===t||c&&"value"!==r.matchProp&&c.substr(0,t.length)===t:s&&"label"!==r.matchProp&&s.indexOf(t)>=0||c&&"value"!==r.matchProp&&c.indexOf(t)>=0}))},Qt=function(e){var t=e.focusedOption,n=e.focusOption,r=e.inputValue,i=e.instancePrefix,a=e.onFocus,l=e.onOptionRef,s=e.onSelect,c=e.optionClassName,u=e.optionComponent,p=e.optionRenderer,d=e.options,f=e.removeValue,h=e.selectValue,v=e.valueArray,b=e.valueKey,A=u;return d.map((function(e,u){var d=v&&v.some((function(t){return t[b]===e[b]})),y=e===t,g=Nt()(c,{"Select-option":!0,"is-selected":d,"is-focused":y,"is-disabled":e.disabled});return o().createElement(A,{className:g,focusOption:n,inputValue:r,instancePrefix:i,isDisabled:e.disabled,isFocused:y,isSelected:d,key:"option-"+u+"-"+e[b],onFocus:a,onSelect:s,option:e,optionIndex:u,ref:function(e){l(e,y)},removeValue:f,selectValue:h},p(e,u,r))}))};Qt.propTypes={focusOption:qt().func,focusedOption:qt().object,inputValue:qt().string,instancePrefix:qt().string,onFocus:qt().func,onOptionRef:qt().func,onSelect:qt().func,optionClassName:qt().string,optionComponent:qt().func,optionRenderer:qt().func,options:qt().array,removeValue:qt().func,selectValue:qt().func,valueArray:qt().array,valueKey:qt().string};var $t=function(e){e.preventDefault(),e.stopPropagation(),"A"===e.target.tagName&&"href"in e.target&&(e.target.target?window.open(e.target.href,e.target.target):window.location.href=e.target.href)},Gt="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Jt=(function(){function e(e){this.value=e}function t(t){var n,r;function o(n,r){try{var a=t[n](r),l=a.value;l instanceof e?Promise.resolve(l.value).then((function(e){o("next",e)}),(function(e){o("throw",e)})):i(a.done?"return":"normal",a.value)}catch(e){i("throw",e)}}function i(e,t){switch(e){case"return":n.resolve({value:t,done:!0});break;case"throw":n.reject(t);break;default:n.resolve({value:t,done:!1})}(n=n.next)?o(n.key,n.arg):r=null}this._invoke=function(e,t){return new Promise((function(i,a){var l={key:e,arg:t,resolve:i,reject:a,next:null};r?r=r.next=l:(n=r=l,o(e,t))}))},"function"!=typeof t.return&&(this.return=void 0)}"function"==typeof Symbol&&Symbol.asyncIterator&&(t.prototype[Symbol.asyncIterator]=function(){return this}),t.prototype.next=function(e){return this._invoke("next",e)},t.prototype.throw=function(e){return this._invoke("throw",e)},t.prototype.return=function(e){return this._invoke("return",e)}}(),function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}),Xt=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),en=function(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e},tn=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},nn=function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)},rn=function(e,t){var n={};for(var r in e)t.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n},on=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t},an=function(e){function t(e){Jt(this,t);var n=on(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.handleMouseDown=n.handleMouseDown.bind(n),n.handleMouseEnter=n.handleMouseEnter.bind(n),n.handleMouseMove=n.handleMouseMove.bind(n),n.handleTouchStart=n.handleTouchStart.bind(n),n.handleTouchEnd=n.handleTouchEnd.bind(n),n.handleTouchMove=n.handleTouchMove.bind(n),n.onFocus=n.onFocus.bind(n),n}return nn(t,e),Xt(t,[{key:"handleMouseDown",value:function(e){e.preventDefault(),e.stopPropagation(),this.props.onSelect(this.props.option,e)}},{key:"handleMouseEnter",value:function(e){this.onFocus(e)}},{key:"handleMouseMove",value:function(e){this.onFocus(e)}},{key:"handleTouchEnd",value:function(e){this.dragging||this.handleMouseDown(e)}},{key:"handleTouchMove",value:function(){this.dragging=!0}},{key:"handleTouchStart",value:function(){this.dragging=!1}},{key:"onFocus",value:function(e){this.props.isFocused||this.props.onFocus(this.props.option,e)}},{key:"render",value:function(){var e=this.props,t=e.option,n=e.instancePrefix,r=e.optionIndex,i=Nt()(this.props.className,t.className);return t.disabled?o().createElement("div",{className:i,onMouseDown:$t,onClick:$t},this.props.children):o().createElement("div",{className:i,style:t.style,role:"option","aria-label":t.label,onMouseDown:this.handleMouseDown,onMouseEnter:this.handleMouseEnter,onMouseMove:this.handleMouseMove,onTouchStart:this.handleTouchStart,onTouchMove:this.handleTouchMove,onTouchEnd:this.handleTouchEnd,id:n+"-option-"+r,title:t.title},this.props.children)}}]),t}(o().Component);an.propTypes={children:qt().node,className:qt().string,instancePrefix:qt().string.isRequired,isDisabled:qt().bool,isFocused:qt().bool,isSelected:qt().bool,onFocus:qt().func,onSelect:qt().func,onUnfocus:qt().func,option:qt().object.isRequired,optionIndex:qt().number};var ln=function(e){function t(e){Jt(this,t);var n=on(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.handleMouseDown=n.handleMouseDown.bind(n),n.onRemove=n.onRemove.bind(n),n.handleTouchEndRemove=n.handleTouchEndRemove.bind(n),n.handleTouchMove=n.handleTouchMove.bind(n),n.handleTouchStart=n.handleTouchStart.bind(n),n}return nn(t,e),Xt(t,[{key:"handleMouseDown",value:function(e){if("mousedown"!==e.type||0===e.button)return this.props.onClick?(e.stopPropagation(),void this.props.onClick(this.props.value,e)):void(this.props.value.href&&e.stopPropagation())}},{key:"onRemove",value:function(e){e.preventDefault(),e.stopPropagation(),this.props.onRemove(this.props.value)}},{key:"handleTouchEndRemove",value:function(e){this.dragging||this.onRemove(e)}},{key:"handleTouchMove",value:function(){this.dragging=!0}},{key:"handleTouchStart",value:function(){this.dragging=!1}},{key:"renderRemoveIcon",value:function(){if(!this.props.disabled&&this.props.onRemove)return o().createElement("span",{className:"Select-value-icon","aria-hidden":"true",onMouseDown:this.onRemove,onTouchEnd:this.handleTouchEndRemove,onTouchStart:this.handleTouchStart,onTouchMove:this.handleTouchMove},"×")}},{key:"renderLabel",value:function(){var e="Select-value-label";return this.props.onClick||this.props.value.href?o().createElement("a",{className:e,href:this.props.value.href,target:this.props.value.target,onMouseDown:this.handleMouseDown,onTouchEnd:this.handleMouseDown},this.props.children):o().createElement("span",{className:e,role:"option","aria-selected":"true",id:this.props.id},this.props.children)}},{key:"render",value:function(){return o().createElement("div",{className:Nt()("Select-value",this.props.value.disabled?"Select-value-disabled":"",this.props.value.className),style:this.props.value.style,title:this.props.value.title},this.renderRemoveIcon(),this.renderLabel())}}]),t}(o().Component);ln.propTypes={children:qt().node,disabled:qt().bool,id:qt().string,onClick:qt().func,onRemove:qt().func,value:qt().object.isRequired};var sn=function(e){return"string"==typeof e?e:null!==e&&JSON.stringify(e)||""},cn=qt().oneOfType([qt().string,qt().node]),un=qt().oneOfType([qt().string,qt().number]),pn=1,dn=function(e,t){var n=void 0===e?"undefined":Gt(e);if("string"!==n&&"number"!==n&&"boolean"!==n)return e;var r=t.options,o=t.valueKey;if(r)for(var i=0;i<r.length;i++)if(String(r[i][o])===String(e))return r[i]},fn=function(e,t){return!e||(t?0===e.length:0===Object.keys(e).length)},hn=function(e){function t(e){Jt(this,t);var n=on(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return["clearValue","focusOption","getOptionLabel","handleInputBlur","handleInputChange","handleInputFocus","handleInputValueChange","handleKeyDown","handleMenuScroll","handleMouseDown","handleMouseDownOnArrow","handleMouseDownOnMenu","handleTouchEnd","handleTouchEndClearValue","handleTouchMove","handleTouchOutside","handleTouchStart","handleValueClick","onOptionRef","removeValue","selectValue"].forEach((function(e){return n[e]=n[e].bind(n)})),n.state={inputValue:"",isFocused:!1,isOpen:!1,isPseudoFocused:!1,required:!1},n}return nn(t,e),Xt(t,[{key:"componentWillMount",value:function(){this._instancePrefix="react-select-"+(this.props.instanceId||++pn)+"-";var e=this.getValueArray(this.props.value);this.props.required&&this.setState({required:fn(e[0],this.props.multi)})}},{key:"componentDidMount",value:function(){void 0!==this.props.autofocus&&"undefined"!=typeof console&&console.warn("Warning: The autofocus prop has changed to autoFocus, support will be removed after react-select@1.0"),(this.props.autoFocus||this.props.autofocus)&&this.focus()}},{key:"componentWillReceiveProps",value:function(e){var t=this.getValueArray(e.value,e);e.required?this.setState({required:fn(t[0],e.multi)}):this.props.required&&this.setState({required:!1}),this.state.inputValue&&this.props.value!==e.value&&e.onSelectResetsInput&&this.setState({inputValue:this.handleInputValueChange("")})}},{key:"componentDidUpdate",value:function(e,t){if(this.menu&&this.focused&&this.state.isOpen&&!this.hasScrolledToOption){var n=(0,Ut.findDOMNode)(this.focused),r=(0,Ut.findDOMNode)(this.menu),o=r.scrollTop,i=o+r.offsetHeight,a=n.offsetTop,l=a+n.offsetHeight;(o>a||i<l)&&(r.scrollTop=n.offsetTop),this.hasScrolledToOption=!0}else this.state.isOpen||(this.hasScrolledToOption=!1);if(this._scrollToFocusedOptionOnUpdate&&this.focused&&this.menu){this._scrollToFocusedOptionOnUpdate=!1;var s=(0,Ut.findDOMNode)(this.focused),c=(0,Ut.findDOMNode)(this.menu),u=s.getBoundingClientRect(),p=c.getBoundingClientRect();u.bottom>p.bottom?c.scrollTop=s.offsetTop+s.clientHeight-c.offsetHeight:u.top<p.top&&(c.scrollTop=s.offsetTop)}if(this.props.scrollMenuIntoView&&this.menuContainer){var d=this.menuContainer.getBoundingClientRect();window.innerHeight<d.bottom+this.props.menuBuffer&&window.scrollBy(0,d.bottom+this.props.menuBuffer-window.innerHeight)}if(e.disabled!==this.props.disabled&&(this.setState({isFocused:!1}),this.closeMenu()),t.isOpen!==this.state.isOpen){this.toggleTouchOutsideEvent(this.state.isOpen);var f=this.state.isOpen?this.props.onOpen:this.props.onClose;f&&f()}}},{key:"componentWillUnmount",value:function(){this.toggleTouchOutsideEvent(!1)}},{key:"toggleTouchOutsideEvent",value:function(e){var t=e?document.addEventListener?"addEventListener":"attachEvent":document.removeEventListener?"removeEventListener":"detachEvent",n=document.addEventListener?"":"on";document[t](n+"touchstart",this.handleTouchOutside),document[t](n+"mousedown",this.handleTouchOutside)}},{key:"handleTouchOutside",value:function(e){this.wrapper&&!this.wrapper.contains(e.target)&&this.closeMenu()}},{key:"focus",value:function(){this.input&&this.input.focus()}},{key:"blurInput",value:function(){this.input&&this.input.blur()}},{key:"handleTouchMove",value:function(){this.dragging=!0}},{key:"handleTouchStart",value:function(){this.dragging=!1}},{key:"handleTouchEnd",value:function(e){this.dragging||this.handleMouseDown(e)}},{key:"handleTouchEndClearValue",value:function(e){this.dragging||this.clearValue(e)}},{key:"handleMouseDown",value:function(e){if(!(this.props.disabled||"mousedown"===e.type&&0!==e.button))if("INPUT"!==e.target.tagName){if(e.preventDefault(),!this.props.searchable)return this.focus(),this.setState({isOpen:!this.state.isOpen,focusedOption:null});if(this.state.isFocused){this.focus();var t=this.input,n=!0;"function"==typeof t.getInput&&(t=t.getInput()),t.value="",this._focusAfterClear&&(n=!1,this._focusAfterClear=!1),this.setState({isOpen:n,isPseudoFocused:!1,focusedOption:null})}else this._openAfterFocus=this.props.openOnClick,this.focus(),this.setState({focusedOption:null})}else this.state.isFocused?this.state.isOpen||this.setState({isOpen:!0,isPseudoFocused:!1,focusedOption:null}):(this._openAfterFocus=this.props.openOnClick,this.focus())}},{key:"handleMouseDownOnArrow",value:function(e){this.props.disabled||"mousedown"===e.type&&0!==e.button||(this.state.isOpen?(e.stopPropagation(),e.preventDefault(),this.closeMenu()):this.setState({isOpen:!0}))}},{key:"handleMouseDownOnMenu",value:function(e){this.props.disabled||"mousedown"===e.type&&0!==e.button||(e.stopPropagation(),e.preventDefault(),this._openAfterFocus=!0,this.focus())}},{key:"closeMenu",value:function(){this.props.onCloseResetsInput?this.setState({inputValue:this.handleInputValueChange(""),isOpen:!1,isPseudoFocused:this.state.isFocused&&!this.props.multi}):this.setState({isOpen:!1,isPseudoFocused:this.state.isFocused&&!this.props.multi}),this.hasScrolledToOption=!1}},{key:"handleInputFocus",value:function(e){if(!this.props.disabled){var t=this.state.isOpen||this._openAfterFocus||this.props.openOnFocus;t=!this._focusAfterClear&&t,this.props.onFocus&&this.props.onFocus(e),this.setState({isFocused:!0,isOpen:!!t}),this._focusAfterClear=!1,this._openAfterFocus=!1}}},{key:"handleInputBlur",value:function(e){if(!this.menu||this.menu!==document.activeElement&&!this.menu.contains(document.activeElement)){this.props.onBlur&&this.props.onBlur(e);var t={isFocused:!1,isOpen:!1,isPseudoFocused:!1};this.props.onBlurResetsInput&&(t.inputValue=this.handleInputValueChange("")),this.setState(t)}else this.focus()}},{key:"handleInputChange",value:function(e){var t=e.target.value;this.state.inputValue!==e.target.value&&(t=this.handleInputValueChange(t)),this.setState({inputValue:t,isOpen:!0,isPseudoFocused:!1})}},{key:"setInputValue",value:function(e){if(this.props.onInputChange){var t=this.props.onInputChange(e);null!=t&&"object"!==(void 0===t?"undefined":Gt(t))&&(e=""+t)}this.setState({inputValue:e})}},{key:"handleInputValueChange",value:function(e){if(this.props.onInputChange){var t=this.props.onInputChange(e);null!=t&&"object"!==(void 0===t?"undefined":Gt(t))&&(e=""+t)}return e}},{key:"handleKeyDown",value:function(e){if(!(this.props.disabled||"function"==typeof this.props.onInputKeyDown&&(this.props.onInputKeyDown(e),e.defaultPrevented)))switch(e.keyCode){case 8:!this.state.inputValue&&this.props.backspaceRemoves&&(e.preventDefault(),this.popValue());break;case 9:if(e.shiftKey||!this.state.isOpen||!this.props.tabSelectsValue)break;e.preventDefault(),this.selectFocusedOption();break;case 13:e.preventDefault(),e.stopPropagation(),this.state.isOpen?this.selectFocusedOption():this.focusNextOption();break;case 27:e.preventDefault(),this.state.isOpen?(this.closeMenu(),e.stopPropagation()):this.props.clearable&&this.props.escapeClearsValue&&(this.clearValue(e),e.stopPropagation());break;case 32:if(this.props.searchable)break;if(e.preventDefault(),!this.state.isOpen){this.focusNextOption();break}e.stopPropagation(),this.selectFocusedOption();break;case 38:e.preventDefault(),this.focusPreviousOption();break;case 40:e.preventDefault(),this.focusNextOption();break;case 33:e.preventDefault(),this.focusPageUpOption();break;case 34:e.preventDefault(),this.focusPageDownOption();break;case 35:if(e.shiftKey)break;e.preventDefault(),this.focusEndOption();break;case 36:if(e.shiftKey)break;e.preventDefault(),this.focusStartOption();break;case 46:!this.state.inputValue&&this.props.deleteRemoves&&(e.preventDefault(),this.popValue())}}},{key:"handleValueClick",value:function(e,t){this.props.onValueClick&&this.props.onValueClick(e,t)}},{key:"handleMenuScroll",value:function(e){if(this.props.onMenuScrollToBottom){var t=e.target;t.scrollHeight>t.offsetHeight&&t.scrollHeight-t.offsetHeight-t.scrollTop<=0&&this.props.onMenuScrollToBottom()}}},{key:"getOptionLabel",value:function(e){return e[this.props.labelKey]}},{key:"getValueArray",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,n="object"===(void 0===t?"undefined":Gt(t))?t:this.props;if(n.multi){if("string"==typeof e&&(e=e.split(n.delimiter)),!Array.isArray(e)){if(null==e)return[];e=[e]}return e.map((function(e){return dn(e,n)})).filter((function(e){return e}))}var r=dn(e,n);return r?[r]:[]}},{key:"setValue",value:function(e){var t=this;if(this.props.autoBlur&&this.blurInput(),this.props.required){var n=fn(e,this.props.multi);this.setState({required:n})}this.props.simpleValue&&e&&(e=this.props.multi?e.map((function(e){return e[t.props.valueKey]})).join(this.props.delimiter):e[this.props.valueKey]),this.props.onChange&&this.props.onChange(e)}},{key:"selectValue",value:function(e){var t=this;this.props.closeOnSelect&&(this.hasScrolledToOption=!1);var n=this.props.onSelectResetsInput?"":this.state.inputValue;this.props.multi?this.setState({focusedIndex:null,inputValue:this.handleInputValueChange(n),isOpen:!this.props.closeOnSelect},(function(){t.getValueArray(t.props.value).some((function(n){return n[t.props.valueKey]===e[t.props.valueKey]}))?t.removeValue(e):t.addValue(e)})):this.setState({inputValue:this.handleInputValueChange(n),isOpen:!this.props.closeOnSelect,isPseudoFocused:this.state.isFocused},(function(){t.setValue(e)}))}},{key:"addValue",value:function(e){var t=this.getValueArray(this.props.value),n=this._visibleOptions.filter((function(e){return!e.disabled})),r=n.indexOf(e);this.setValue(t.concat(e)),this.props.closeOnSelect&&(n.length-1===r?this.focusOption(n[r-1]):n.length>r&&this.focusOption(n[r+1]))}},{key:"popValue",value:function(){var e=this.getValueArray(this.props.value);e.length&&!1!==e[e.length-1].clearableValue&&this.setValue(this.props.multi?e.slice(0,e.length-1):null)}},{key:"removeValue",value:function(e){var t=this,n=this.getValueArray(this.props.value);this.setValue(n.filter((function(n){return n[t.props.valueKey]!==e[t.props.valueKey]}))),this.focus()}},{key:"clearValue",value:function(e){e&&"mousedown"===e.type&&0!==e.button||(e.preventDefault(),this.setValue(this.getResetValue()),this.setState({inputValue:this.handleInputValueChange(""),isOpen:!1},this.focus),this._focusAfterClear=!0)}},{key:"getResetValue",value:function(){return void 0!==this.props.resetValue?this.props.resetValue:this.props.multi?[]:null}},{key:"focusOption",value:function(e){this.setState({focusedOption:e})}},{key:"focusNextOption",value:function(){this.focusAdjacentOption("next")}},{key:"focusPreviousOption",value:function(){this.focusAdjacentOption("previous")}},{key:"focusPageUpOption",value:function(){this.focusAdjacentOption("page_up")}},{key:"focusPageDownOption",value:function(){this.focusAdjacentOption("page_down")}},{key:"focusStartOption",value:function(){this.focusAdjacentOption("start")}},{key:"focusEndOption",value:function(){this.focusAdjacentOption("end")}},{key:"focusAdjacentOption",value:function(e){var t=this._visibleOptions.map((function(e,t){return{option:e,index:t}})).filter((function(e){return!e.option.disabled}));if(this._scrollToFocusedOptionOnUpdate=!0,!this.state.isOpen){var n={focusedOption:this._focusedOption||(t.length?t["next"===e?0:t.length-1].option:null),isOpen:!0};return this.props.onSelectResetsInput&&(n.inputValue=""),void this.setState(n)}if(t.length){for(var r=-1,o=0;o<t.length;o++)if(this._focusedOption===t[o].option){r=o;break}if("next"===e&&-1!==r)r=(r+1)%t.length;else if("previous"===e)r>0?r-=1:r=t.length-1;else if("start"===e)r=0;else if("end"===e)r=t.length-1;else if("page_up"===e){var i=r-this.props.pageSize;r=i<0?0:i}else if("page_down"===e){var a=r+this.props.pageSize;r=a>t.length-1?t.length-1:a}-1===r&&(r=0),this.setState({focusedIndex:t[r].index,focusedOption:t[r].option})}}},{key:"getFocusedOption",value:function(){return this._focusedOption}},{key:"selectFocusedOption",value:function(){if(this._focusedOption)return this.selectValue(this._focusedOption)}},{key:"renderLoading",value:function(){if(this.props.isLoading)return o().createElement("span",{className:"Select-loading-zone","aria-hidden":"true"},o().createElement("span",{className:"Select-loading"}))}},{key:"renderValue",value:function(e,t){var n=this,r=this.props.valueRenderer||this.getOptionLabel,i=this.props.valueComponent;if(!e.length){var a=function(e,t,n){var r=e.inputValue,o=e.isPseudoFocused,i=e.isFocused,a=t.onSelectResetsInput;return!r||!a&&!n&&!o&&!i}(this.state,this.props,t);return a?o().createElement("div",{className:"Select-placeholder"},this.props.placeholder):null}var l,s,c,u,p,d,f=this.props.onValueClick?this.handleValueClick:null;return this.props.multi?e.map((function(t,a){return o().createElement(i,{disabled:n.props.disabled||!1===t.clearableValue,id:n._instancePrefix+"-value-"+a,instancePrefix:n._instancePrefix,key:"value-"+a+"-"+t[n.props.valueKey],onClick:f,onRemove:n.removeValue,placeholder:n.props.placeholder,value:t,values:e},r(t,a),o().createElement("span",{className:"Select-aria-only"}," "))})):(l=this.state,s=this.props,c=l.inputValue,u=l.isPseudoFocused,p=l.isFocused,d=s.onSelectResetsInput,c&&(d||!p&&u||p&&!u)?void 0:(t&&(f=null),o().createElement(i,{disabled:this.props.disabled,id:this._instancePrefix+"-value-item",instancePrefix:this._instancePrefix,onClick:f,placeholder:this.props.placeholder,value:e[0]},r(e[0]))))}},{key:"renderInput",value:function(e,t){var n,r=this,i=Nt()("Select-input",this.props.inputProps.className),a=this.state.isOpen,l=Nt()((en(n={},this._instancePrefix+"-list",a),en(n,this._instancePrefix+"-backspace-remove-message",this.props.multi&&!this.props.disabled&&this.state.isFocused&&!this.state.inputValue),n)),s=this.state.inputValue;!s||this.props.onSelectResetsInput||this.state.isFocused||(s="");var c=tn({},this.props.inputProps,{"aria-activedescendant":a?this._instancePrefix+"-option-"+t:this._instancePrefix+"-value","aria-describedby":this.props["aria-describedby"],"aria-expanded":""+a,"aria-haspopup":""+a,"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],"aria-owns":l,onBlur:this.handleInputBlur,onChange:this.handleInputChange,onFocus:this.handleInputFocus,ref:function(e){return r.input=e},role:"combobox",required:this.state.required,tabIndex:this.props.tabIndex,value:s});if(this.props.inputRenderer)return this.props.inputRenderer(c);if(this.props.disabled||!this.props.searchable){var u=rn(this.props.inputProps,[]),p=Nt()(en({},this._instancePrefix+"-list",a));return o().createElement("div",tn({},u,{"aria-expanded":a,"aria-owns":p,"aria-activedescendant":a?this._instancePrefix+"-option-"+t:this._instancePrefix+"-value","aria-disabled":""+this.props.disabled,"aria-label":this.props["aria-label"],"aria-labelledby":this.props["aria-labelledby"],className:i,onBlur:this.handleInputBlur,onFocus:this.handleInputFocus,ref:function(e){return r.input=e},role:"combobox",style:{border:0,width:1,display:"inline-block"},tabIndex:this.props.tabIndex||0}))}return this.props.autosize?o().createElement(Rt.Z,tn({id:this.props.id},c,{className:i,minWidth:"5"})):o().createElement("div",{className:i,key:"input-wrap",style:{display:"inline-block"}},o().createElement("input",tn({id:this.props.id},c)))}},{key:"renderClear",value:function(){var e=this.getValueArray(this.props.value);if(this.props.clearable&&e.length&&!this.props.disabled&&!this.props.isLoading){var t=this.props.multi?this.props.clearAllText:this.props.clearValueText,n=this.props.clearRenderer();return o().createElement("span",{"aria-label":t,className:"Select-clear-zone",onMouseDown:this.clearValue,onTouchEnd:this.handleTouchEndClearValue,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,title:t},n)}}},{key:"renderArrow",value:function(){if(this.props.arrowRenderer){var e=this.handleMouseDownOnArrow,t=this.state.isOpen,n=this.props.arrowRenderer({onMouseDown:e,isOpen:t});return n?o().createElement("span",{className:"Select-arrow-zone",onMouseDown:e},n):null}}},{key:"filterOptions",value:function(e){var t=this.state.inputValue,n=this.props.options||[];return this.props.filterOptions?("function"==typeof this.props.filterOptions?this.props.filterOptions:Zt)(n,t,e,{filterOption:this.props.filterOption,ignoreAccents:this.props.ignoreAccents,ignoreCase:this.props.ignoreCase,labelKey:this.props.labelKey,matchPos:this.props.matchPos,matchProp:this.props.matchProp,trimFilter:this.props.trimFilter,valueKey:this.props.valueKey}):n}},{key:"onOptionRef",value:function(e,t){t&&(this.focused=e)}},{key:"renderMenu",value:function(e,t,n){return e&&e.length?this.props.menuRenderer({focusedOption:n,focusOption:this.focusOption,inputValue:this.state.inputValue,instancePrefix:this._instancePrefix,labelKey:this.props.labelKey,onFocus:this.focusOption,onOptionRef:this.onOptionRef,onSelect:this.selectValue,optionClassName:this.props.optionClassName,optionComponent:this.props.optionComponent,optionRenderer:this.props.optionRenderer||this.getOptionLabel,options:e,removeValue:this.removeValue,selectValue:this.selectValue,valueArray:t,valueKey:this.props.valueKey}):this.props.noResultsText?o().createElement("div",{className:"Select-noresults"},this.props.noResultsText):null}},{key:"renderHiddenField",value:function(e){var t=this;if(this.props.name){if(this.props.joinValues){var n=e.map((function(e){return sn(e[t.props.valueKey])})).join(this.props.delimiter);return o().createElement("input",{disabled:this.props.disabled,name:this.props.name,ref:function(e){return t.value=e},type:"hidden",value:n})}return e.map((function(e,n){return o().createElement("input",{disabled:t.props.disabled,key:"hidden."+n,name:t.props.name,ref:"value"+n,type:"hidden",value:sn(e[t.props.valueKey])})}))}}},{key:"getFocusableOptionIndex",value:function(e){var t=this._visibleOptions;if(!t.length)return null;var n=this.props.valueKey,r=this.state.focusedOption||e;if(r&&!r.disabled){var o=-1;if(t.some((function(e,t){var i=e[n]===r[n];return i&&(o=t),i})),-1!==o)return o}for(var i=0;i<t.length;i++)if(!t[i].disabled)return i;return null}},{key:"renderOuter",value:function(e,t,n){var r=this,i=this.renderMenu(e,t,n);return i?o().createElement("div",{ref:function(e){return r.menuContainer=e},className:"Select-menu-outer",style:this.props.menuContainerStyle},o().createElement("div",{className:"Select-menu",id:this._instancePrefix+"-list",onMouseDown:this.handleMouseDownOnMenu,onScroll:this.handleMenuScroll,ref:function(e){return r.menu=e},role:"listbox",style:this.props.menuStyle,tabIndex:-1},i)):null}},{key:"render",value:function(){var e=this,t=this.getValueArray(this.props.value),n=this._visibleOptions=this.filterOptions(this.props.multi&&this.props.removeSelected?t:null),r=this.state.isOpen;this.props.multi&&!n.length&&t.length&&!this.state.inputValue&&(r=!1);var i,a=this.getFocusableOptionIndex(t[0]);i=this._focusedOption=null!==a?n[a]:null;var l=Nt()("Select",this.props.className,{"has-value":t.length,"is-clearable":this.props.clearable,"is-disabled":this.props.disabled,"is-focused":this.state.isFocused,"is-loading":this.props.isLoading,"is-open":r,"is-pseudo-focused":this.state.isPseudoFocused,"is-searchable":this.props.searchable,"Select--multi":this.props.multi,"Select--rtl":this.props.rtl,"Select--single":!this.props.multi}),s=null;return this.props.multi&&!this.props.disabled&&t.length&&!this.state.inputValue&&this.state.isFocused&&this.props.backspaceRemoves&&(s=o().createElement("span",{id:this._instancePrefix+"-backspace-remove-message",className:"Select-aria-only","aria-live":"assertive"},this.props.backspaceToRemoveMessage.replace("{label}",t[t.length-1][this.props.labelKey]))),o().createElement("div",{ref:function(t){return e.wrapper=t},className:l,style:this.props.wrapperStyle},this.renderHiddenField(t),o().createElement("div",{ref:function(t){return e.control=t},className:"Select-control",onKeyDown:this.handleKeyDown,onMouseDown:this.handleMouseDown,onTouchEnd:this.handleTouchEnd,onTouchMove:this.handleTouchMove,onTouchStart:this.handleTouchStart,style:this.props.style},o().createElement("div",{className:"Select-multi-value-wrapper",id:this._instancePrefix+"-value"},this.renderValue(t,r),this.renderInput(t,a)),s,this.renderLoading(),this.renderClear(),this.renderArrow()),r?this.renderOuter(n,t,i):null)}}]),t}(o().Component);hn.propTypes={"aria-describedby":qt().string,"aria-label":qt().string,"aria-labelledby":qt().string,arrowRenderer:qt().func,autoBlur:qt().bool,autoFocus:qt().bool,autofocus:qt().bool,autosize:qt().bool,backspaceRemoves:qt().bool,backspaceToRemoveMessage:qt().string,className:qt().string,clearAllText:cn,clearRenderer:qt().func,clearValueText:cn,clearable:qt().bool,closeOnSelect:qt().bool,deleteRemoves:qt().bool,delimiter:qt().string,disabled:qt().bool,escapeClearsValue:qt().bool,filterOption:qt().func,filterOptions:qt().any,id:qt().string,ignoreAccents:qt().bool,ignoreCase:qt().bool,inputProps:qt().object,inputRenderer:qt().func,instanceId:qt().string,isLoading:qt().bool,joinValues:qt().bool,labelKey:qt().string,matchPos:qt().string,matchProp:qt().string,menuBuffer:qt().number,menuContainerStyle:qt().object,menuRenderer:qt().func,menuStyle:qt().object,multi:qt().bool,name:qt().string,noResultsText:cn,onBlur:qt().func,onBlurResetsInput:qt().bool,onChange:qt().func,onClose:qt().func,onCloseResetsInput:qt().bool,onFocus:qt().func,onInputChange:qt().func,onInputKeyDown:qt().func,onMenuScrollToBottom:qt().func,onOpen:qt().func,onSelectResetsInput:qt().bool,onValueClick:qt().func,openOnClick:qt().bool,openOnFocus:qt().bool,optionClassName:qt().string,optionComponent:qt().func,optionRenderer:qt().func,options:qt().array,pageSize:qt().number,placeholder:cn,removeSelected:qt().bool,required:qt().bool,resetValue:qt().any,rtl:qt().bool,scrollMenuIntoView:qt().bool,searchable:qt().bool,simpleValue:qt().bool,style:qt().object,tabIndex:un,tabSelectsValue:qt().bool,trimFilter:qt().bool,value:qt().any,valueComponent:qt().func,valueKey:qt().string,valueRenderer:qt().func,wrapperStyle:qt().object},hn.defaultProps={arrowRenderer:Vt,autosize:!0,backspaceRemoves:!0,backspaceToRemoveMessage:"Press backspace to remove {label}",clearable:!0,clearAllText:"Clear all",clearRenderer:function(){return o().createElement("span",{className:"Select-clear",dangerouslySetInnerHTML:{__html:"&times;"}})},clearValueText:"Clear value",closeOnSelect:!0,deleteRemoves:!0,delimiter:",",disabled:!1,escapeClearsValue:!0,filterOptions:Zt,ignoreAccents:!0,ignoreCase:!0,inputProps:{},isLoading:!1,joinValues:!1,labelKey:"label",matchPos:"any",matchProp:"any",menuBuffer:0,menuRenderer:Qt,multi:!1,noResultsText:"No results found",onBlurResetsInput:!0,onCloseResetsInput:!0,onSelectResetsInput:!0,openOnClick:!0,optionComponent:an,pageSize:5,placeholder:"Select...",removeSelected:!0,required:!1,rtl:!1,scrollMenuIntoView:!0,searchable:!0,simpleValue:!1,tabSelectsValue:!0,trimFilter:!0,valueComponent:ln,valueKey:"value"};var vn={autoload:qt().bool.isRequired,cache:qt().any,children:qt().func.isRequired,ignoreAccents:qt().bool,ignoreCase:qt().bool,loadOptions:qt().func.isRequired,loadingPlaceholder:qt().oneOfType([qt().string,qt().node]),multi:qt().bool,noResultsText:qt().oneOfType([qt().string,qt().node]),onChange:qt().func,onInputChange:qt().func,options:qt().array.isRequired,placeholder:qt().oneOfType([qt().string,qt().node]),searchPromptText:qt().oneOfType([qt().string,qt().node]),value:qt().any},bn={},An={autoload:!0,cache:bn,children:function(e){return o().createElement(hn,e)},ignoreAccents:!0,ignoreCase:!0,loadingPlaceholder:"Loading...",options:[],searchPromptText:"Type to search"},yn=function(e){function t(e,n){Jt(this,t);var r=on(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,n));return r._cache=e.cache===bn?{}:e.cache,r.state={inputValue:"",isLoading:!1,options:e.options},r.onInputChange=r.onInputChange.bind(r),r}return nn(t,e),Xt(t,[{key:"componentDidMount",value:function(){this.props.autoload&&this.loadOptions("")}},{key:"componentWillReceiveProps",value:function(e){e.options!==this.props.options&&this.setState({options:e.options})}},{key:"componentWillUnmount",value:function(){this._callback=null}},{key:"loadOptions",value:function(e){var t=this,n=this.props.loadOptions,r=this._cache;if(r&&Object.prototype.hasOwnProperty.call(r,e))return this._callback=null,void this.setState({isLoading:!1,options:r[e]});var o=function n(o,i){var a=i&&i.options||[];r&&(r[e]=a),n===t._callback&&(t._callback=null,t.setState({isLoading:!1,options:a}))};this._callback=o;var i=n(e,o);i&&i.then((function(e){return o(0,e)}),(function(e){return o()})),this._callback&&!this.state.isLoading&&this.setState({isLoading:!0})}},{key:"onInputChange",value:function(e){var t=this.props,n=t.ignoreAccents,r=t.ignoreCase,o=t.onInputChange,i=e;if(o){var a=o(i);null!=a&&"object"!==(void 0===a?"undefined":Gt(a))&&(i=""+a)}var l=i;return n&&(l=Yt(l)),r&&(l=l.toLowerCase()),this.setState({inputValue:i}),this.loadOptions(l),i}},{key:"noResultsText",value:function(){var e=this.props,t=e.loadingPlaceholder,n=e.noResultsText,r=e.searchPromptText,o=this.state,i=o.inputValue;return o.isLoading?t:i&&n?n:r}},{key:"focus",value:function(){this.select.focus()}},{key:"render",value:function(){var e=this,t=this.props,n=t.children,r=t.loadingPlaceholder,o=t.placeholder,i=this.state,a=i.isLoading,l=i.options,s={noResultsText:this.noResultsText(),placeholder:a?r:o,options:a&&r?[]:l,ref:function(t){return e.select=t}};return n(tn({},this.props,s,{isLoading:a,onInputChange:this.onInputChange}))}}]),t}(r.Component);yn.propTypes=vn,yn.defaultProps=An;var gn=function(e){function t(e,n){Jt(this,t);var r=on(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e,n));return r.filterOptions=r.filterOptions.bind(r),r.menuRenderer=r.menuRenderer.bind(r),r.onInputKeyDown=r.onInputKeyDown.bind(r),r.onInputChange=r.onInputChange.bind(r),r.onOptionSelect=r.onOptionSelect.bind(r),r}return nn(t,e),Xt(t,[{key:"createNewOption",value:function(){var e=this.props,t=e.isValidNewOption,n=e.newOptionCreator,r=e.onNewOptionClick,o=e.options,i=void 0===o?[]:o;if(t({label:this.inputValue})){var a=n({label:this.inputValue,labelKey:this.labelKey,valueKey:this.valueKey});this.isOptionUnique({option:a,options:i})&&(r?r(a):(i.unshift(a),this.select.selectValue(a)))}}},{key:"filterOptions",value:function(){var e=this.props,t=e.filterOptions,n=e.isValidNewOption,r=e.promptTextCreator,o=e.showNewOptionAtTop,i=(arguments.length<=2?void 0:arguments[2])||[],a=t.apply(void 0,arguments)||[];if(n({label:this.inputValue})){var l=this.props.newOptionCreator,s=l({label:this.inputValue,labelKey:this.labelKey,valueKey:this.valueKey});if(this.isOptionUnique({option:s,options:i.concat(a)})){var c=r(this.inputValue);this._createPlaceholderOption=l({label:c,labelKey:this.labelKey,valueKey:this.valueKey}),o?a.unshift(this._createPlaceholderOption):a.push(this._createPlaceholderOption)}}return a}},{key:"isOptionUnique",value:function(e){var t=e.option,n=e.options,r=this.props.isOptionUnique;return n=n||this.props.options,r({labelKey:this.labelKey,option:t,options:n,valueKey:this.valueKey})}},{key:"menuRenderer",value:function(e){return(0,this.props.menuRenderer)(tn({},e,{onSelect:this.onOptionSelect,selectValue:this.onOptionSelect}))}},{key:"onInputChange",value:function(e){var t=this.props.onInputChange;return this.inputValue=e,t&&(this.inputValue=t(e)),this.inputValue}},{key:"onInputKeyDown",value:function(e){var t=this.props,n=t.shouldKeyDownEventCreateNewOption,r=t.onInputKeyDown,o=this.select.getFocusedOption();o&&o===this._createPlaceholderOption&&n(e)?(this.createNewOption(),e.preventDefault()):r&&r(e)}},{key:"onOptionSelect",value:function(e){e===this._createPlaceholderOption?this.createNewOption():this.select.selectValue(e)}},{key:"focus",value:function(){this.select.focus()}},{key:"render",value:function(){var e=this,t=this.props,n=t.ref,r=rn(t,["ref"]),o=this.props.children;return o||(o=mn),o(tn({},r,{allowCreate:!0,filterOptions:this.filterOptions,menuRenderer:this.menuRenderer,onInputChange:this.onInputChange,onInputKeyDown:this.onInputKeyDown,ref:function(t){e.select=t,t&&(e.labelKey=t.props.labelKey,e.valueKey=t.props.valueKey),n&&n(t)}}))}}]),t}(o().Component),mn=function(e){return o().createElement(hn,e)},wn=function(e){var t=e.option,n=e.options,r=e.labelKey,o=e.valueKey;return!n||!n.length||0===n.filter((function(e){return e[r]===t[r]||e[o]===t[o]})).length},En=function(e){return!!e.label},Cn=function(e){var t=e.label,n=e.labelKey,r={};return r[e.valueKey]=t,r[n]=t,r.className="Select-create-option-placeholder",r},kn=function(e){return'Create option "'+e+'"'},xn=function(e){switch(e.keyCode){case 9:case 13:case 188:return!0;default:return!1}};gn.isOptionUnique=wn,gn.isValidNewOption=En,gn.newOptionCreator=Cn,gn.promptTextCreator=kn,gn.shouldKeyDownEventCreateNewOption=xn,gn.defaultProps={filterOptions:Zt,isOptionUnique:wn,isValidNewOption:En,menuRenderer:Qt,newOptionCreator:Cn,promptTextCreator:kn,shouldKeyDownEventCreateNewOption:xn,showNewOptionAtTop:!0},gn.propTypes={children:qt().func,filterOptions:qt().any,isOptionUnique:qt().func,isValidNewOption:qt().func,menuRenderer:qt().any,newOptionCreator:qt().func,onInputChange:qt().func,onInputKeyDown:qt().func,onNewOptionClick:qt().func,options:qt().array,promptTextCreator:qt().func,ref:qt().func,shouldKeyDownEventCreateNewOption:qt().func,showNewOptionAtTop:qt().bool};var On=function(e){function t(){return Jt(this,t),on(this,(t.__proto__||Object.getPrototypeOf(t)).apply(this,arguments))}return nn(t,e),Xt(t,[{key:"focus",value:function(){this.select.focus()}},{key:"render",value:function(){var e=this;return o().createElement(yn,this.props,(function(t){var n=t.ref,r=rn(t,["ref"]),i=n;return o().createElement(gn,r,(function(t){var n=t.ref,r=rn(t,["ref"]),o=n;return e.props.children(tn({},r,{ref:function(t){o(t),i(t),e.select=t}}))}))}))}}]),t}(o().Component);On.propTypes={children:qt().func.isRequired},On.defaultProps={children:function(e){return o().createElement(hn,e)}},hn.Async=yn,hn.AsyncCreatable=On,hn.Creatable=gn,hn.Value=ln,hn.Option=an;var Sn=hn;function Bn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(void 0,"symbol"==typeof(o=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(r.key))?o:String(o)),r)}var o}var _n=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e)}var t,n;return t=e,n=[{key:"getFirstParentOfType",value:function(e,t){t=t.toUpperCase();for(var n=e;n;){if(n.tagName.toUpperCase()===t)return n;if(null===n.parentElement)return;n=n.parentElement}}},{key:"getParentById",value:function(e,t){for(var n=e;n;){if(n.id===t)return n;if(null===n.parentElement)return;n=n.parentElement}}}],null&&Bn(t.prototype,null),n&&Bn(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();function jn(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,In(r.key),r)}}function Pn(e,t){return Pn=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Pn(e,t)}function Dn(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Tn(e){return Tn=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Tn(e)}function In(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var Fn,Mn=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Pn(e,t)}(l,e);var t,n,r,i,a=(r=l,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Tn(r);if(i){var n=Tn(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Dn(e)}(this,e)});function l(){var e,t,n,r;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l);for(var o=arguments.length,i=new Array(o),s=0;s<o;s++)i[s]=arguments[s];return t=Dn(e=a.call.apply(a,[this].concat(i))),r=function(){var t=e.refs.dropdown;qe(t.wrapper.querySelector(".Select-menu-outer"))},(n=In(n="handleOpenDropdown"))in t?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r,e}return t=l,(n=[{key:"render",value:function(){var e=this.props,t=e.clearable,n=e.dropdown,r=e.onChange,i=e.value,a=e.disabled;return o().createElement("div",{className:"dash-dropdown-cell-value-container dash-cell-value-container",onClick:this.handleClick},o().createElement("div",{className:"dropdown-cell-value-shadow cell-value-shadow"},(n&&n.find((function(e){return e.value===i}))||{label:void 0}).label),o().createElement(Sn,{ref:"dropdown",clearable:t,onChange:function(e){r(e?e.value:e)},scrollMenuIntoView:!1,onOpen:this.handleOpenDropdown,options:n,placeholder:"",value:i,disabled:a}))}},{key:"componentDidUpdate",value:function(){this.setFocus()}},{key:"componentDidMount",value:function(){this.setFocus()}},{key:"handleClick",value:function(e){e.stopPropagation()}},{key:"setFocus",value:function(){var e=this.props,t=e.active,n=e.applyFocus;if(t){var r=this.refs.dropdown;if(n&&r&&document.activeElement!==r){var o=_n.getFirstParentOfType(r.wrapper,"td");o&&-1===o.className.indexOf("phantom-cell")&&o.focus()}}}}])&&jn(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),l}(r.PureComponent),Rn=function(e){return e},zn=function(e){var t;return e.type===_.QD.Numeric&&(t=function(e){if(!e)return function(e){return e};var t=function(e){var t,n,r=void 0===e.grouping||void 0===e.thousands?ue:(t=pe.call(e.grouping,Number),n=e.thousands+"",function(e,r){for(var o=e.length,i=[],a=0,l=t[0],s=0;o>0&&l>0&&(s+l+1>r&&(l=Math.max(1,r-s)),i.push(e.substring(o-=l,o+l)),!((s+=l+1)>r));)l=t[a=(a+1)%t.length];return i.reverse().join(n)}),o=void 0===e.currency?"":e.currency[0]+"",i=void 0===e.currency?"":e.currency[1]+"",a=void 0===e.decimal?".":e.decimal+"",l=void 0===e.numerals?ue:function(e){return function(t){return t.replace(/[0-9]/g,(function(t){return e[+t]}))}}(pe.call(e.numerals,String)),s=void 0===e.percent?"%":e.percent+"",c=void 0===e.minus?"−":e.minus+"",u=void 0===e.nan?"NaN":e.nan+"";function p(e){var t=(e=ae(e)).fill,n=e.align,p=e.sign,d=e.symbol,f=e.zero,h=e.width,v=e.comma,b=e.precision,A=e.trim,y=e.type;"n"===y?(v=!0,y="g"):ce[y]||(void 0===b&&(b=12),A=!0,y="g"),(f||"0"===t&&"="===n)&&(f=!0,t="0",n="=");var g="$"===d?o:"#"===d&&/[boxX]/.test(y)?"0"+y.toLowerCase():"",m="$"===d?i:/[%p]/.test(y)?s:"",w=ce[y],E=/[defgprs%]/.test(y);function C(e){var o,i,s,d=g,C=m;if("c"===y)C=w(e)+C,e="";else{var k=(e=+e)<0||1/e<0;if(e=isNaN(e)?u:w(Math.abs(e),b),A&&(e=function(e){e:for(var t,n=e.length,r=1,o=-1;r<n;++r)switch(e[r]){case".":o=t=r;break;case"0":0===o&&(o=r),t=r;break;default:if(!+e[r])break e;o>0&&(o=0)}return o>0?e.slice(0,o)+e.slice(t+1):e}(e)),k&&0==+e&&"+"!==p&&(k=!1),d=(k?"("===p?p:c:"-"===p||"("===p?"":p)+d,C=("s"===y?de[8+oe/3]:"")+C+(k&&"("===p?")":""),E)for(o=-1,i=e.length;++o<i;)if(48>(s=e.charCodeAt(o))||s>57){C=(46===s?a+e.slice(o+1):e.slice(o))+C,e=e.slice(0,o);break}}v&&!f&&(e=r(e,1/0));var x=d.length+e.length+C.length,O=x<h?new Array(h-x+1).join(t):"";switch(v&&f&&(e=r(O+e,O.length?h-C.length:1/0),O=""),n){case"<":e=d+e+C+O;break;case"=":e=d+O+e+C;break;case"^":e=O.slice(0,x=O.length>>1)+d+e+C+O.slice(x);break;default:e=O+d+e+C}return l(e)}return b=void 0===b?6:/[gprs]/.test(y)?Math.max(1,Math.min(21,b)):Math.max(0,Math.min(20,b)),C.toString=function(){return e+""},C}return{format:p,formatPrefix:function(e,t){var n,r=p(((e=ae(e)).type="f",e)),o=3*Math.max(-8,Math.min(8,Math.floor((n=t,((n=re(Math.abs(n)))?n[1]:NaN)/3)))),i=Math.pow(10,-o),a=de[8+o/3];return function(e){return r(i*e)+a}}}}(ge(e.locale)),n=e.prefix?t.formatPrefix(e.specifier,e.prefix):t.format(e.specifier),r=e.locale.separate_4digits?e.specifier:e.specifier.replace(/,/,""),o=e.prefix?t.formatPrefix(r,e.prefix):t.format(r);return function(t){return"number"!=typeof(t=be(t)?e.nully:t)?t:Math.abs(t)<1e4?o(t):n(t)}}(e.format)),t||Rn};function Nn(e){return(Fn=Fn||document.createElement("textarea")).innerHTML="&"+e+";",Fn.value}var Ln=Object.prototype.hasOwnProperty;function qn(e){return[].slice.call(arguments,1).forEach((function(t){if(t){if("object"!=typeof t)throw new TypeError(t+"must be object");Object.keys(t).forEach((function(n){e[n]=t[n]}))}})),e}var Un=/\\([\\!"#$%&'()*+,.\/:;<=>?@[\]^_`{|}~-])/g;function Wn(e){return e.indexOf("\\")<0?e:e.replace(Un,"$1")}function Vn(e){return!(e>=55296&&e<=57343||e>=64976&&e<=65007||65535==(65535&e)||65534==(65535&e)||e>=0&&e<=8||11===e||e>=14&&e<=31||e>=127&&e<=159||e>1114111)}function Hn(e){if(e>65535){var t=55296+((e-=65536)>>10),n=56320+(1023&e);return String.fromCharCode(t,n)}return String.fromCharCode(e)}var Yn=/&([a-z#][a-z0-9]{1,31});/gi,Kn=/^#((?:x[a-f0-9]{1,8}|[0-9]{1,8}))/i;function Zn(e,t){var n=0,r=Nn(t);return t!==r?r:35===t.charCodeAt(0)&&Kn.test(t)&&Vn(n="x"===t[1].toLowerCase()?parseInt(t.slice(2),16):parseInt(t.slice(1),10))?Hn(n):e}function Qn(e){return e.indexOf("&")<0?e:e.replace(Yn,Zn)}var $n=/[&<>"]/,Gn=/[&<>"]/g,Jn={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;"};function Xn(e){return Jn[e]}function er(e){return $n.test(e)?e.replace(Gn,Xn):e}var tr={};function nr(e,t){return++t>=e.length-2?t:"paragraph_open"===e[t].type&&e[t].tight&&"inline"===e[t+1].type&&0===e[t+1].content.length&&"paragraph_close"===e[t+2].type&&e[t+2].tight?nr(e,t+2):t}tr.blockquote_open=function(){return"<blockquote>\n"},tr.blockquote_close=function(e,t){return"</blockquote>"+rr(e,t)},tr.code=function(e,t){return e[t].block?"<pre><code>"+er(e[t].content)+"</code></pre>"+rr(e,t):"<code>"+er(e[t].content)+"</code>"},tr.fence=function(e,t,n,r,o){var i,a,l,s,c=e[t],u="",p=n.langPrefix;if(c.params){if(a=(i=c.params.split(/\s+/g)).join(" "),l=o.rules.fence_custom,s=i[0],l&&Ln.call(l,s))return o.rules.fence_custom[i[0]](e,t,n,r,o);u=' class="'+p+er(Qn(Wn(a)))+'"'}return"<pre><code"+u+">"+(n.highlight&&n.highlight.apply(n.highlight,[c.content].concat(i))||er(c.content))+"</code></pre>"+rr(e,t)},tr.fence_custom={},tr.heading_open=function(e,t){return"<h"+e[t].hLevel+">"},tr.heading_close=function(e,t){return"</h"+e[t].hLevel+">\n"},tr.hr=function(e,t,n){return(n.xhtmlOut?"<hr />":"<hr>")+rr(e,t)},tr.bullet_list_open=function(){return"<ul>\n"},tr.bullet_list_close=function(e,t){return"</ul>"+rr(e,t)},tr.list_item_open=function(){return"<li>"},tr.list_item_close=function(){return"</li>\n"},tr.ordered_list_open=function(e,t){var n=e[t];return"<ol"+(n.order>1?' start="'+n.order+'"':"")+">\n"},tr.ordered_list_close=function(e,t){return"</ol>"+rr(e,t)},tr.paragraph_open=function(e,t){return e[t].tight?"":"<p>"},tr.paragraph_close=function(e,t){var n=!(e[t].tight&&t&&"inline"===e[t-1].type&&!e[t-1].content);return(e[t].tight?"":"</p>")+(n?rr(e,t):"")},tr.link_open=function(e,t,n){var r=e[t].title?' title="'+er(Qn(e[t].title))+'"':"",o=n.linkTarget?' target="'+n.linkTarget+'"':"";return'<a href="'+er(e[t].href)+'"'+r+o+">"},tr.link_close=function(){return"</a>"},tr.image=function(e,t,n){var r=' src="'+er(e[t].src)+'"',o=e[t].title?' title="'+er(Qn(e[t].title))+'"':"";return"<img"+r+' alt="'+(e[t].alt?er(Qn(Wn(e[t].alt))):"")+'"'+o+(n.xhtmlOut?" /":"")+">"},tr.table_open=function(){return"<table>\n"},tr.table_close=function(){return"</table>\n"},tr.thead_open=function(){return"<thead>\n"},tr.thead_close=function(){return"</thead>\n"},tr.tbody_open=function(){return"<tbody>\n"},tr.tbody_close=function(){return"</tbody>\n"},tr.tr_open=function(){return"<tr>"},tr.tr_close=function(){return"</tr>\n"},tr.th_open=function(e,t){var n=e[t];return"<th"+(n.align?' style="text-align:'+n.align+'"':"")+">"},tr.th_close=function(){return"</th>"},tr.td_open=function(e,t){var n=e[t];return"<td"+(n.align?' style="text-align:'+n.align+'"':"")+">"},tr.td_close=function(){return"</td>"},tr.strong_open=function(){return"<strong>"},tr.strong_close=function(){return"</strong>"},tr.em_open=function(){return"<em>"},tr.em_close=function(){return"</em>"},tr.del_open=function(){return"<del>"},tr.del_close=function(){return"</del>"},tr.ins_open=function(){return"<ins>"},tr.ins_close=function(){return"</ins>"},tr.mark_open=function(){return"<mark>"},tr.mark_close=function(){return"</mark>"},tr.sub=function(e,t){return"<sub>"+er(e[t].content)+"</sub>"},tr.sup=function(e,t){return"<sup>"+er(e[t].content)+"</sup>"},tr.hardbreak=function(e,t,n){return n.xhtmlOut?"<br />\n":"<br>\n"},tr.softbreak=function(e,t,n){return n.breaks?n.xhtmlOut?"<br />\n":"<br>\n":"\n"},tr.text=function(e,t){return er(e[t].content)},tr.htmlblock=function(e,t){return e[t].content},tr.htmltag=function(e,t){return e[t].content},tr.abbr_open=function(e,t){return'<abbr title="'+er(Qn(e[t].title))+'">'},tr.abbr_close=function(){return"</abbr>"},tr.footnote_ref=function(e,t){var n=Number(e[t].id+1).toString(),r="fnref"+n;return e[t].subId>0&&(r+=":"+e[t].subId),'<sup class="footnote-ref"><a href="#fn'+n+'" id="'+r+'">['+n+"]</a></sup>"},tr.footnote_block_open=function(e,t,n){return(n.xhtmlOut?'<hr class="footnotes-sep" />\n':'<hr class="footnotes-sep">\n')+'<section class="footnotes">\n<ol class="footnotes-list">\n'},tr.footnote_block_close=function(){return"</ol>\n</section>\n"},tr.footnote_open=function(e,t){return'<li id="fn'+Number(e[t].id+1).toString()+'"  class="footnote-item">'},tr.footnote_close=function(){return"</li>\n"},tr.footnote_anchor=function(e,t){var n="fnref"+Number(e[t].id+1).toString();return e[t].subId>0&&(n+=":"+e[t].subId),' <a href="#'+n+'" class="footnote-backref">↩</a>'},tr.dl_open=function(){return"<dl>\n"},tr.dt_open=function(){return"<dt>"},tr.dd_open=function(){return"<dd>"},tr.dl_close=function(){return"</dl>\n"},tr.dt_close=function(){return"</dt>\n"},tr.dd_close=function(){return"</dd>\n"};var rr=tr.getBreak=function(e,t){return(t=nr(e,t))<e.length&&"list_item_close"===e[t].type?"":"\n"};function or(){this.rules=qn({},tr),this.getBreak=tr.getBreak}function ir(){this.__rules__=[],this.__cache__=null}function ar(e,t,n,r,o){this.src=e,this.env=r,this.options=n,this.parser=t,this.tokens=o,this.pos=0,this.posMax=this.src.length,this.level=0,this.pending="",this.pendingLevel=0,this.cache=[],this.isInLabel=!1,this.linkLevel=0,this.linkContent="",this.labelUnmatchedScopes=0}function lr(e,t){var n,r,o,i=-1,a=e.posMax,l=e.pos,s=e.isInLabel;if(e.isInLabel)return-1;if(e.labelUnmatchedScopes)return e.labelUnmatchedScopes--,-1;for(e.pos=t+1,e.isInLabel=!0,n=1;e.pos<a;){if(91===(o=e.src.charCodeAt(e.pos)))n++;else if(93===o&&0==--n){r=!0;break}e.parser.skipToken(e)}return r?(i=e.pos,e.labelUnmatchedScopes=0):e.labelUnmatchedScopes=n-1,e.pos=l,e.isInLabel=s,i}function sr(e,t,n,r){var o,i,a,l,s,c;if(42!==e.charCodeAt(0))return-1;if(91!==e.charCodeAt(1))return-1;if(-1===e.indexOf("]:"))return-1;if((i=lr(o=new ar(e,t,n,r,[]),1))<0||58!==e.charCodeAt(i+1))return-1;for(l=o.posMax,a=i+2;a<l&&10!==o.src.charCodeAt(a);a++);return s=e.slice(2,i),0===(c=e.slice(i+2,a).trim()).length?-1:(r.abbreviations||(r.abbreviations={}),void 0===r.abbreviations[":"+s]&&(r.abbreviations[":"+s]=c),a)}function cr(e){var t=Qn(e);try{t=decodeURI(t)}catch(e){}return encodeURI(t)}function ur(e,t){var n,r,o,i=t,a=e.posMax;if(60===e.src.charCodeAt(t)){for(t++;t<a;){if(10===(n=e.src.charCodeAt(t)))return!1;if(62===n)return o=cr(Wn(e.src.slice(i+1,t))),!!e.parser.validateLink(o)&&(e.pos=t+1,e.linkContent=o,!0);92===n&&t+1<a?t+=2:t++}return!1}for(r=0;t<a&&32!==(n=e.src.charCodeAt(t))&&!(n<32||127===n);)if(92===n&&t+1<a)t+=2;else{if(40===n&&++r>1)break;if(41===n&&--r<0)break;t++}return i!==t&&(o=Wn(e.src.slice(i,t)),!!e.parser.validateLink(o)&&(e.linkContent=o,e.pos=t,!0))}function pr(e,t){var n,r=t,o=e.posMax,i=e.src.charCodeAt(t);if(34!==i&&39!==i&&40!==i)return!1;for(t++,40===i&&(i=41);t<o;){if((n=e.src.charCodeAt(t))===i)return e.pos=t+1,e.linkContent=Wn(e.src.slice(r+1,t)),!0;92===n&&t+1<o?t+=2:t++}return!1}function dr(e){return e.trim().replace(/\s+/g," ").toUpperCase()}function fr(e,t,n,r){var o,i,a,l,s,c,u,p,d;if(91!==e.charCodeAt(0))return-1;if(-1===e.indexOf("]:"))return-1;if((i=lr(o=new ar(e,t,n,r,[]),0))<0||58!==e.charCodeAt(i+1))return-1;for(l=o.posMax,a=i+2;a<l&&(32===(s=o.src.charCodeAt(a))||10===s);a++);if(!ur(o,a))return-1;for(u=o.linkContent,c=a=o.pos,a+=1;a<l&&(32===(s=o.src.charCodeAt(a))||10===s);a++);for(a<l&&c!==a&&pr(o,a)?(p=o.linkContent,a=o.pos):(p="",a=c);a<l&&32===o.src.charCodeAt(a);)a++;return a<l&&10!==o.src.charCodeAt(a)?-1:(d=dr(e.slice(1,i)),void 0===r.references[d]&&(r.references[d]={title:p,href:u}),a)}or.prototype.renderInline=function(e,t,n){for(var r=this.rules,o=e.length,i=0,a="";o--;)a+=r[e[i].type](e,i++,t,n,this);return a},or.prototype.render=function(e,t,n){for(var r=this.rules,o=e.length,i=-1,a="";++i<o;)"inline"===e[i].type?a+=this.renderInline(e[i].children,t,n):a+=r[e[i].type](e,i,t,n,this);return a},ir.prototype.__find__=function(e){for(var t=this.__rules__.length,n=-1;t--;)if(this.__rules__[++n].name===e)return n;return-1},ir.prototype.__compile__=function(){var e=this,t=[""];e.__rules__.forEach((function(e){e.enabled&&e.alt.forEach((function(e){t.indexOf(e)<0&&t.push(e)}))})),e.__cache__={},t.forEach((function(t){e.__cache__[t]=[],e.__rules__.forEach((function(n){n.enabled&&(t&&n.alt.indexOf(t)<0||e.__cache__[t].push(n.fn))}))}))},ir.prototype.at=function(e,t,n){var r=this.__find__(e),o=n||{};if(-1===r)throw new Error("Parser rule not found: "+e);this.__rules__[r].fn=t,this.__rules__[r].alt=o.alt||[],this.__cache__=null},ir.prototype.before=function(e,t,n,r){var o=this.__find__(e),i=r||{};if(-1===o)throw new Error("Parser rule not found: "+e);this.__rules__.splice(o,0,{name:t,enabled:!0,fn:n,alt:i.alt||[]}),this.__cache__=null},ir.prototype.after=function(e,t,n,r){var o=this.__find__(e),i=r||{};if(-1===o)throw new Error("Parser rule not found: "+e);this.__rules__.splice(o+1,0,{name:t,enabled:!0,fn:n,alt:i.alt||[]}),this.__cache__=null},ir.prototype.push=function(e,t,n){var r=n||{};this.__rules__.push({name:e,enabled:!0,fn:t,alt:r.alt||[]}),this.__cache__=null},ir.prototype.enable=function(e,t){e=Array.isArray(e)?e:[e],t&&this.__rules__.forEach((function(e){e.enabled=!1})),e.forEach((function(e){var t=this.__find__(e);if(t<0)throw new Error("Rules manager: invalid rule name "+e);this.__rules__[t].enabled=!0}),this),this.__cache__=null},ir.prototype.disable=function(e){(e=Array.isArray(e)?e:[e]).forEach((function(e){var t=this.__find__(e);if(t<0)throw new Error("Rules manager: invalid rule name "+e);this.__rules__[t].enabled=!1}),this),this.__cache__=null},ir.prototype.getRules=function(e){return null===this.__cache__&&this.__compile__(),this.__cache__[e]||[]},ar.prototype.pushPending=function(){this.tokens.push({type:"text",content:this.pending,level:this.pendingLevel}),this.pending=""},ar.prototype.push=function(e){this.pending&&this.pushPending(),this.tokens.push(e),this.pendingLevel=this.level},ar.prototype.cacheSet=function(e,t){for(var n=this.cache.length;n<=e;n++)this.cache.push(0);this.cache[e]=t},ar.prototype.cacheGet=function(e){return e<this.cache.length?this.cache[e]:0};var hr=" \n()[]'\".,!?-";function vr(e){return e.replace(/([-()\[\]{}+?*.$\^|,:#<!\\])/g,"\\$1")}var br=/\+-|\.\.|\?\?\?\?|!!!!|,,|--/,Ar=/\((c|tm|r|p)\)/gi,yr={c:"©",r:"®",p:"§",tm:"™"},gr=/['"]/,mr=/['"]/g,wr=/[-\s()\[\]]/;function Er(e,t){return!(t<0||t>=e.length||wr.test(e[t]))}function Cr(e,t,n){return e.substr(0,t)+n+e.substr(t+1)}var kr=[["block",function(e){e.inlineMode?e.tokens.push({type:"inline",content:e.src.replace(/\n/g," ").trim(),level:0,lines:[0,1],children:[]}):e.block.parse(e.src,e.options,e.env,e.tokens)}],["abbr",function(e){var t,n,r,o,i=e.tokens;if(!e.inlineMode)for(t=1,n=i.length-1;t<n;t++)if("paragraph_open"===i[t-1].type&&"inline"===i[t].type&&"paragraph_close"===i[t+1].type){for(r=i[t].content;r.length&&!((o=sr(r,e.inline,e.options,e.env))<0);)r=r.slice(o).trim();i[t].content=r,r.length||(i[t-1].tight=!0,i[t+1].tight=!0)}}],["references",function(e){var t,n,r,o,i=e.tokens;if(e.env.references=e.env.references||{},!e.inlineMode)for(t=1,n=i.length-1;t<n;t++)if("inline"===i[t].type&&"paragraph_open"===i[t-1].type&&"paragraph_close"===i[t+1].type){for(r=i[t].content;r.length&&!((o=fr(r,e.inline,e.options,e.env))<0);)r=r.slice(o).trim();i[t].content=r,r.length||(i[t-1].tight=!0,i[t+1].tight=!0)}}],["inline",function(e){var t,n,r,o=e.tokens;for(n=0,r=o.length;n<r;n++)"inline"===(t=o[n]).type&&e.inline.parse(t.content,e.options,e.env,t.children)}],["footnote_tail",function(e){var t,n,r,o,i,a,l,s,c,u=0,p=!1,d={};if(e.env.footnotes&&(e.tokens=e.tokens.filter((function(e){return"footnote_reference_open"===e.type?(p=!0,s=[],c=e.label,!1):"footnote_reference_close"===e.type?(p=!1,d[":"+c]=s,!1):(p&&s.push(e),!p)})),e.env.footnotes.list)){for(a=e.env.footnotes.list,e.tokens.push({type:"footnote_block_open",level:u++}),t=0,n=a.length;t<n;t++){for(e.tokens.push({type:"footnote_open",id:t,level:u++}),a[t].tokens?((l=[]).push({type:"paragraph_open",tight:!1,level:u++}),l.push({type:"inline",content:"",level:u,children:a[t].tokens}),l.push({type:"paragraph_close",tight:!1,level:--u})):a[t].label&&(l=d[":"+a[t].label]),e.tokens=e.tokens.concat(l),i="paragraph_close"===e.tokens[e.tokens.length-1].type?e.tokens.pop():null,o=a[t].count>0?a[t].count:1,r=0;r<o;r++)e.tokens.push({type:"footnote_anchor",id:t,subId:r,level:u});i&&e.tokens.push(i),e.tokens.push({type:"footnote_close",level:--u})}e.tokens.push({type:"footnote_block_close",level:--u})}}],["abbr2",function(e){var t,n,r,o,i,a,l,s,c,u,p,d,f=e.tokens;if(e.env.abbreviations)for(e.env.abbrRegExp||(d="(^|["+hr.split("").map(vr).join("")+"])("+Object.keys(e.env.abbreviations).map((function(e){return e.substr(1)})).sort((function(e,t){return t.length-e.length})).map(vr).join("|")+")($|["+hr.split("").map(vr).join("")+"])",e.env.abbrRegExp=new RegExp(d,"g")),u=e.env.abbrRegExp,n=0,r=f.length;n<r;n++)if("inline"===f[n].type)for(t=(o=f[n].children).length-1;t>=0;t--)if("text"===(i=o[t]).type){for(s=0,a=i.content,u.lastIndex=0,c=i.level,l=[];p=u.exec(a);)u.lastIndex>s&&l.push({type:"text",content:a.slice(s,p.index+p[1].length),level:c}),l.push({type:"abbr_open",title:e.env.abbreviations[":"+p[2]],level:c++}),l.push({type:"text",content:p[2],level:c}),l.push({type:"abbr_close",level:--c}),s=u.lastIndex-p[3].length;l.length&&(s<a.length&&l.push({type:"text",content:a.slice(s),level:c}),f[n].children=o=[].concat(o.slice(0,t),l,o.slice(t+1)))}}],["replacements",function(e){var t,n,r,o,i,a;if(e.options.typographer)for(i=e.tokens.length-1;i>=0;i--)if("inline"===e.tokens[i].type)for(t=(o=e.tokens[i].children).length-1;t>=0;t--)"text"===(n=o[t]).type&&(r=(a=r=n.content).indexOf("(")<0?a:a.replace(Ar,(function(e,t){return yr[t.toLowerCase()]})),br.test(r)&&(r=r.replace(/\+-/g,"±").replace(/\.{2,}/g,"…").replace(/([?!])…/g,"$1..").replace(/([?!]){4,}/g,"$1$1$1").replace(/,{2,}/g,",").replace(/(^|[^-])---([^-]|$)/gm,"$1—$2").replace(/(^|\s)--(\s|$)/gm,"$1–$2").replace(/(^|[^-\s])--([^-\s]|$)/gm,"$1–$2")),n.content=r)}],["smartquotes",function(e){var t,n,r,o,i,a,l,s,c,u,p,d,f,h,v,b,A;if(e.options.typographer)for(A=[],v=e.tokens.length-1;v>=0;v--)if("inline"===e.tokens[v].type)for(b=e.tokens[v].children,A.length=0,t=0;t<b.length;t++)if("text"===(n=b[t]).type&&!gr.test(n.text)){for(l=b[t].level,f=A.length-1;f>=0&&!(A[f].level<=l);f--);A.length=f+1,i=0,a=(r=n.content).length;e:for(;i<a&&(mr.lastIndex=i,o=mr.exec(r));)if(s=!Er(r,o.index-1),i=o.index+1,h="'"===o[0],(c=!Er(r,i))||s){if(p=!c,d=!s)for(f=A.length-1;f>=0&&(u=A[f],!(A[f].level<l));f--)if(u.single===h&&A[f].level===l){u=A[f],h?(b[u.token].content=Cr(b[u.token].content,u.pos,e.options.quotes[2]),n.content=Cr(n.content,o.index,e.options.quotes[3])):(b[u.token].content=Cr(b[u.token].content,u.pos,e.options.quotes[0]),n.content=Cr(n.content,o.index,e.options.quotes[1])),A.length=f;continue e}p?A.push({token:t,pos:o.index,single:h,level:l}):d&&h&&(n.content=Cr(n.content,o.index,"’"))}else h&&(n.content=Cr(n.content,o.index,"’"))}}]];function xr(){this.options={},this.ruler=new ir;for(var e=0;e<kr.length;e++)this.ruler.push(kr[e][0],kr[e][1])}function Or(e,t,n,r,o){var i,a,l,s,c,u,p;for(this.src=e,this.parser=t,this.options=n,this.env=r,this.tokens=o,this.bMarks=[],this.eMarks=[],this.tShift=[],this.blkIndent=0,this.line=0,this.lineMax=0,this.tight=!1,this.parentType="root",this.ddIndent=-1,this.level=0,this.result="",u=0,p=!1,l=s=u=0,c=(a=this.src).length;s<c;s++){if(i=a.charCodeAt(s),!p){if(32===i){u++;continue}p=!0}10!==i&&s!==c-1||(10!==i&&s++,this.bMarks.push(l),this.eMarks.push(s),this.tShift.push(u),p=!1,u=0,l=s+1)}this.bMarks.push(a.length),this.eMarks.push(a.length),this.tShift.push(0),this.lineMax=this.bMarks.length-1}function Sr(e,t){var n,r,o;return(r=e.bMarks[t]+e.tShift[t])>=(o=e.eMarks[t])||42!==(n=e.src.charCodeAt(r++))&&45!==n&&43!==n||r<o&&32!==e.src.charCodeAt(r)?-1:r}function Br(e,t){var n,r=e.bMarks[t]+e.tShift[t],o=e.eMarks[t];if(r+1>=o)return-1;if((n=e.src.charCodeAt(r++))<48||n>57)return-1;for(;;){if(r>=o)return-1;if(!((n=e.src.charCodeAt(r++))>=48&&n<=57)){if(41===n||46===n)break;return-1}}return r<o&&32!==e.src.charCodeAt(r)?-1:r}xr.prototype.process=function(e){var t,n,r;for(t=0,n=(r=this.ruler.getRules("")).length;t<n;t++)r[t](e)},Or.prototype.isEmpty=function(e){return this.bMarks[e]+this.tShift[e]>=this.eMarks[e]},Or.prototype.skipEmptyLines=function(e){for(var t=this.lineMax;e<t&&!(this.bMarks[e]+this.tShift[e]<this.eMarks[e]);e++);return e},Or.prototype.skipSpaces=function(e){for(var t=this.src.length;e<t&&32===this.src.charCodeAt(e);e++);return e},Or.prototype.skipChars=function(e,t){for(var n=this.src.length;e<n&&this.src.charCodeAt(e)===t;e++);return e},Or.prototype.skipCharsBack=function(e,t,n){if(e<=n)return e;for(;e>n;)if(t!==this.src.charCodeAt(--e))return e+1;return e},Or.prototype.getLines=function(e,t,n,r){var o,i,a,l,s,c=e;if(e>=t)return"";if(c+1===t)return i=this.bMarks[c]+Math.min(this.tShift[c],n),a=r?this.eMarks[c]+1:this.eMarks[c],this.src.slice(i,a);for(l=new Array(t-e),o=0;c<t;c++,o++)(s=this.tShift[c])>n&&(s=n),s<0&&(s=0),i=this.bMarks[c]+s,a=c+1<t||r?this.eMarks[c]+1:this.eMarks[c],l[o]=this.src.slice(i,a);return l.join("")};var _r={};["article","aside","button","blockquote","body","canvas","caption","col","colgroup","dd","div","dl","dt","embed","fieldset","figcaption","figure","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","iframe","li","map","object","ol","output","p","pre","progress","script","section","style","table","tbody","td","textarea","tfoot","th","tr","thead","ul","video"].forEach((function(e){_r[e]=!0}));var jr=/^<([a-zA-Z]{1,15})[\s\/>]/,Pr=/^<\/([a-zA-Z]{1,15})[\s>]/;function Dr(e,t){var n=e.bMarks[t]+e.blkIndent,r=e.eMarks[t];return e.src.substr(n,r-n)}function Tr(e,t){var n,r,o=e.bMarks[t]+e.tShift[t],i=e.eMarks[t];return o>=i||126!==(r=e.src.charCodeAt(o++))&&58!==r||o===(n=e.skipSpaces(o))||n>=i?-1:n}var Ir=[["code",function(e,t,n){var r,o;if(e.tShift[t]-e.blkIndent<4)return!1;for(o=r=t+1;r<n;)if(e.isEmpty(r))r++;else{if(!(e.tShift[r]-e.blkIndent>=4))break;o=++r}return e.line=r,e.tokens.push({type:"code",content:e.getLines(t,o,4+e.blkIndent,!0),block:!0,lines:[t,e.line],level:e.level}),!0}],["fences",function(e,t,n,r){var o,i,a,l,s,c=!1,u=e.bMarks[t]+e.tShift[t],p=e.eMarks[t];if(u+3>p)return!1;if(126!==(o=e.src.charCodeAt(u))&&96!==o)return!1;if(s=u,(i=(u=e.skipChars(u,o))-s)<3)return!1;if((a=e.src.slice(u,p).trim()).indexOf("`")>=0)return!1;if(r)return!0;for(l=t;!(++l>=n||(u=s=e.bMarks[l]+e.tShift[l])<(p=e.eMarks[l])&&e.tShift[l]<e.blkIndent);)if(e.src.charCodeAt(u)===o&&!(e.tShift[l]-e.blkIndent>=4||(u=e.skipChars(u,o))-s<i||(u=e.skipSpaces(u))<p)){c=!0;break}return i=e.tShift[t],e.line=l+(c?1:0),e.tokens.push({type:"fence",params:a,content:e.getLines(t+1,l,i,!0),lines:[t,e.line],level:e.level}),!0},["paragraph","blockquote","list"]],["blockquote",function(e,t,n,r){var o,i,a,l,s,c,u,p,d,f,h,v=e.bMarks[t]+e.tShift[t],b=e.eMarks[t];if(v>b)return!1;if(62!==e.src.charCodeAt(v++))return!1;if(e.level>=e.options.maxNesting)return!1;if(r)return!0;for(32===e.src.charCodeAt(v)&&v++,s=e.blkIndent,e.blkIndent=0,l=[e.bMarks[t]],e.bMarks[t]=v,i=(v=v<b?e.skipSpaces(v):v)>=b,a=[e.tShift[t]],e.tShift[t]=v-e.bMarks[t],p=e.parser.ruler.getRules("blockquote"),o=t+1;o<n&&!((v=e.bMarks[o]+e.tShift[o])>=(b=e.eMarks[o]));o++)if(62!==e.src.charCodeAt(v++)){if(i)break;for(h=!1,d=0,f=p.length;d<f;d++)if(p[d](e,o,n,!0)){h=!0;break}if(h)break;l.push(e.bMarks[o]),a.push(e.tShift[o]),e.tShift[o]=-1337}else 32===e.src.charCodeAt(v)&&v++,l.push(e.bMarks[o]),e.bMarks[o]=v,i=(v=v<b?e.skipSpaces(v):v)>=b,a.push(e.tShift[o]),e.tShift[o]=v-e.bMarks[o];for(c=e.parentType,e.parentType="blockquote",e.tokens.push({type:"blockquote_open",lines:u=[t,0],level:e.level++}),e.parser.tokenize(e,t,o),e.tokens.push({type:"blockquote_close",level:--e.level}),e.parentType=c,u[1]=e.line,d=0;d<a.length;d++)e.bMarks[d+t]=l[d],e.tShift[d+t]=a[d];return e.blkIndent=s,!0},["paragraph","blockquote","list"]],["hr",function(e,t,n,r){var o,i,a,l=e.bMarks[t],s=e.eMarks[t];if((l+=e.tShift[t])>s)return!1;if(42!==(o=e.src.charCodeAt(l++))&&45!==o&&95!==o)return!1;for(i=1;l<s;){if((a=e.src.charCodeAt(l++))!==o&&32!==a)return!1;a===o&&i++}return!(i<3||(r||(e.line=t+1,e.tokens.push({type:"hr",lines:[t,e.line],level:e.level})),0))},["paragraph","blockquote","list"]],["list",function(e,t,n,r){var o,i,a,l,s,c,u,p,d,f,h,v,b,A,y,g,m,w,E,C,k,x=!0;if((p=Br(e,t))>=0)v=!0;else{if(!((p=Sr(e,t))>=0))return!1;v=!1}if(e.level>=e.options.maxNesting)return!1;if(h=e.src.charCodeAt(p-1),r)return!0;for(A=e.tokens.length,v?(u=e.bMarks[t]+e.tShift[t],f=Number(e.src.substr(u,p-u-1)),e.tokens.push({type:"ordered_list_open",order:f,lines:g=[t,0],level:e.level++})):e.tokens.push({type:"bullet_list_open",lines:g=[t,0],level:e.level++}),o=t,y=!1,w=e.parser.ruler.getRules("list");!(!(o<n)||((d=(b=e.skipSpaces(p))>=e.eMarks[o]?1:b-p)>4&&(d=1),d<1&&(d=1),i=p-e.bMarks[o]+d,e.tokens.push({type:"list_item_open",lines:m=[t,0],level:e.level++}),l=e.blkIndent,s=e.tight,a=e.tShift[t],c=e.parentType,e.tShift[t]=b-e.bMarks[t],e.blkIndent=i,e.tight=!0,e.parentType="list",e.parser.tokenize(e,t,n,!0),e.tight&&!y||(x=!1),y=e.line-t>1&&e.isEmpty(e.line-1),e.blkIndent=l,e.tShift[t]=a,e.tight=s,e.parentType=c,e.tokens.push({type:"list_item_close",level:--e.level}),o=t=e.line,m[1]=o,b=e.bMarks[t],o>=n)||e.isEmpty(o)||e.tShift[o]<e.blkIndent);){for(k=!1,E=0,C=w.length;E<C;E++)if(w[E](e,o,n,!0)){k=!0;break}if(k)break;if(v){if((p=Br(e,o))<0)break}else if((p=Sr(e,o))<0)break;if(h!==e.src.charCodeAt(p-1))break}return e.tokens.push({type:v?"ordered_list_close":"bullet_list_close",level:--e.level}),g[1]=o,e.line=o,x&&function(e,t){var n,r,o=e.level+2;for(n=t+2,r=e.tokens.length-2;n<r;n++)e.tokens[n].level===o&&"paragraph_open"===e.tokens[n].type&&(e.tokens[n+2].tight=!0,e.tokens[n].tight=!0,n+=2)}(e,A),!0},["paragraph","blockquote"]],["footnote",function(e,t,n,r){var o,i,a,l,s,c=e.bMarks[t]+e.tShift[t],u=e.eMarks[t];if(c+4>u)return!1;if(91!==e.src.charCodeAt(c))return!1;if(94!==e.src.charCodeAt(c+1))return!1;if(e.level>=e.options.maxNesting)return!1;for(l=c+2;l<u;l++){if(32===e.src.charCodeAt(l))return!1;if(93===e.src.charCodeAt(l))break}return!(l===c+2||l+1>=u||58!==e.src.charCodeAt(++l)||(r||(l++,e.env.footnotes||(e.env.footnotes={}),e.env.footnotes.refs||(e.env.footnotes.refs={}),s=e.src.slice(c+2,l-2),e.env.footnotes.refs[":"+s]=-1,e.tokens.push({type:"footnote_reference_open",label:s,level:e.level++}),o=e.bMarks[t],i=e.tShift[t],a=e.parentType,e.tShift[t]=e.skipSpaces(l)-l,e.bMarks[t]=l,e.blkIndent+=4,e.parentType="footnote",e.tShift[t]<e.blkIndent&&(e.tShift[t]+=e.blkIndent,e.bMarks[t]-=e.blkIndent),e.parser.tokenize(e,t,n,!0),e.parentType=a,e.blkIndent-=4,e.tShift[t]=i,e.bMarks[t]=o,e.tokens.push({type:"footnote_reference_close",level:--e.level})),0))},["paragraph"]],["heading",function(e,t,n,r){var o,i,a,l=e.bMarks[t]+e.tShift[t],s=e.eMarks[t];if(l>=s)return!1;if(35!==(o=e.src.charCodeAt(l))||l>=s)return!1;for(i=1,o=e.src.charCodeAt(++l);35===o&&l<s&&i<=6;)i++,o=e.src.charCodeAt(++l);return!(i>6||l<s&&32!==o||(r||(s=e.skipCharsBack(s,32,l),(a=e.skipCharsBack(s,35,l))>l&&32===e.src.charCodeAt(a-1)&&(s=a),e.line=t+1,e.tokens.push({type:"heading_open",hLevel:i,lines:[t,e.line],level:e.level}),l<s&&e.tokens.push({type:"inline",content:e.src.slice(l,s).trim(),level:e.level+1,lines:[t,e.line],children:[]}),e.tokens.push({type:"heading_close",hLevel:i,level:e.level})),0))},["paragraph","blockquote"]],["lheading",function(e,t,n){var r,o,i,a=t+1;return!(a>=n||e.tShift[a]<e.blkIndent||e.tShift[a]-e.blkIndent>3||(o=e.bMarks[a]+e.tShift[a])>=(i=e.eMarks[a])||45!==(r=e.src.charCodeAt(o))&&61!==r||(o=e.skipChars(o,r),(o=e.skipSpaces(o))<i||(o=e.bMarks[t]+e.tShift[t],e.line=a+1,e.tokens.push({type:"heading_open",hLevel:61===r?1:2,lines:[t,e.line],level:e.level}),e.tokens.push({type:"inline",content:e.src.slice(o,e.eMarks[t]).trim(),level:e.level+1,lines:[t,e.line-1],children:[]}),e.tokens.push({type:"heading_close",hLevel:61===r?1:2,level:e.level}),0)))}],["htmlblock",function(e,t,n,r){var o,i,a,l=e.bMarks[t],s=e.eMarks[t],c=e.tShift[t];if(l+=c,!e.options.html)return!1;if(c>3||l+2>=s)return!1;if(60!==e.src.charCodeAt(l))return!1;if(33===(o=e.src.charCodeAt(l+1))||63===o){if(r)return!0}else{if(47!==o&&!function(e){var t=32|e;return t>=97&&t<=122}(o))return!1;if(47===o){if(!(i=e.src.slice(l,s).match(Pr)))return!1}else if(!(i=e.src.slice(l,s).match(jr)))return!1;if(!0!==_r[i[1].toLowerCase()])return!1;if(r)return!0}for(a=t+1;a<e.lineMax&&!e.isEmpty(a);)a++;return e.line=a,e.tokens.push({type:"htmlblock",level:e.level,lines:[t,e.line],content:e.getLines(t,a,0,!0)}),!0},["paragraph","blockquote"]],["table",function(e,t,n,r){var o,i,a,l,s,c,u,p,d,f,h;if(t+2>n)return!1;if(s=t+1,e.tShift[s]<e.blkIndent)return!1;if((a=e.bMarks[s]+e.tShift[s])>=e.eMarks[s])return!1;if(124!==(o=e.src.charCodeAt(a))&&45!==o&&58!==o)return!1;if(i=Dr(e,t+1),!/^[-:| ]+$/.test(i))return!1;if((c=i.split("|"))<=2)return!1;for(p=[],l=0;l<c.length;l++){if(!(d=c[l].trim())){if(0===l||l===c.length-1)continue;return!1}if(!/^:?-+:?$/.test(d))return!1;58===d.charCodeAt(d.length-1)?p.push(58===d.charCodeAt(0)?"center":"right"):58===d.charCodeAt(0)?p.push("left"):p.push("")}if(-1===(i=Dr(e,t).trim()).indexOf("|"))return!1;if(c=i.replace(/^\||\|$/g,"").split("|"),p.length!==c.length)return!1;if(r)return!0;for(e.tokens.push({type:"table_open",lines:f=[t,0],level:e.level++}),e.tokens.push({type:"thead_open",lines:[t,t+1],level:e.level++}),e.tokens.push({type:"tr_open",lines:[t,t+1],level:e.level++}),l=0;l<c.length;l++)e.tokens.push({type:"th_open",align:p[l],lines:[t,t+1],level:e.level++}),e.tokens.push({type:"inline",content:c[l].trim(),lines:[t,t+1],level:e.level,children:[]}),e.tokens.push({type:"th_close",level:--e.level});for(e.tokens.push({type:"tr_close",level:--e.level}),e.tokens.push({type:"thead_close",level:--e.level}),e.tokens.push({type:"tbody_open",lines:h=[t+2,0],level:e.level++}),s=t+2;s<n&&!(e.tShift[s]<e.blkIndent)&&-1!==(i=Dr(e,s).trim()).indexOf("|");s++){for(c=i.replace(/^\||\|$/g,"").split("|"),e.tokens.push({type:"tr_open",level:e.level++}),l=0;l<c.length;l++)e.tokens.push({type:"td_open",align:p[l],level:e.level++}),u=c[l].substring(124===c[l].charCodeAt(0)?1:0,124===c[l].charCodeAt(c[l].length-1)?c[l].length-1:c[l].length).trim(),e.tokens.push({type:"inline",content:u,level:e.level,children:[]}),e.tokens.push({type:"td_close",level:--e.level});e.tokens.push({type:"tr_close",level:--e.level})}return e.tokens.push({type:"tbody_close",level:--e.level}),e.tokens.push({type:"table_close",level:--e.level}),f[1]=h[1]=s,e.line=s,!0},["paragraph"]],["deflist",function(e,t,n,r){var o,i,a,l,s,c,u,p,d,f,h,v,b,A;if(r)return!(e.ddIndent<0)&&Tr(e,t)>=0;if(u=t+1,e.isEmpty(u)&&++u>n)return!1;if(e.tShift[u]<e.blkIndent)return!1;if((o=Tr(e,u))<0)return!1;if(e.level>=e.options.maxNesting)return!1;c=e.tokens.length,e.tokens.push({type:"dl_open",lines:s=[t,0],level:e.level++}),a=t,i=u;e:for(;;){for(A=!0,b=!1,e.tokens.push({type:"dt_open",lines:[a,a],level:e.level++}),e.tokens.push({type:"inline",content:e.getLines(a,a+1,e.blkIndent,!1).trim(),level:e.level+1,lines:[a,a],children:[]}),e.tokens.push({type:"dt_close",level:--e.level});;){if(e.tokens.push({type:"dd_open",lines:l=[u,0],level:e.level++}),v=e.tight,d=e.ddIndent,p=e.blkIndent,h=e.tShift[i],f=e.parentType,e.blkIndent=e.ddIndent=e.tShift[i]+2,e.tShift[i]=o-e.bMarks[i],e.tight=!0,e.parentType="deflist",e.parser.tokenize(e,i,n,!0),e.tight&&!b||(A=!1),b=e.line-i>1&&e.isEmpty(e.line-1),e.tShift[i]=h,e.tight=v,e.parentType=f,e.blkIndent=p,e.ddIndent=d,e.tokens.push({type:"dd_close",level:--e.level}),l[1]=u=e.line,u>=n)break e;if(e.tShift[u]<e.blkIndent)break e;if((o=Tr(e,u))<0)break;i=u}if(u>=n)break;if(a=u,e.isEmpty(a))break;if(e.tShift[a]<e.blkIndent)break;if((i=a+1)>=n)break;if(e.isEmpty(i)&&i++,i>=n)break;if(e.tShift[i]<e.blkIndent)break;if((o=Tr(e,i))<0)break}return e.tokens.push({type:"dl_close",level:--e.level}),s[1]=u,e.line=u,A&&function(e,t){var n,r,o=e.level+2;for(n=t+2,r=e.tokens.length-2;n<r;n++)e.tokens[n].level===o&&"paragraph_open"===e.tokens[n].type&&(e.tokens[n+2].tight=!0,e.tokens[n].tight=!0,n+=2)}(e,c),!0},["paragraph"]],["paragraph",function(e,t){var n,r,o,i,a,l,s=t+1;if(s<(n=e.lineMax)&&!e.isEmpty(s))for(l=e.parser.ruler.getRules("paragraph");s<n&&!e.isEmpty(s);s++)if(!(e.tShift[s]-e.blkIndent>3)){for(o=!1,i=0,a=l.length;i<a;i++)if(l[i](e,s,n,!0)){o=!0;break}if(o)break}return r=e.getLines(t,s,e.blkIndent,!1).trim(),e.line=s,r.length&&(e.tokens.push({type:"paragraph_open",tight:!1,lines:[t,e.line],level:e.level}),e.tokens.push({type:"inline",content:r,level:e.level+1,lines:[t,e.line],children:[]}),e.tokens.push({type:"paragraph_close",tight:!1,level:e.level})),!0}]];function Fr(){this.ruler=new ir;for(var e=0;e<Ir.length;e++)this.ruler.push(Ir[e][0],Ir[e][1],{alt:(Ir[e][2]||[]).slice()})}Fr.prototype.tokenize=function(e,t,n){for(var r,o=this.ruler.getRules(""),i=o.length,a=t,l=!1;a<n&&(e.line=a=e.skipEmptyLines(a),!(a>=n))&&!(e.tShift[a]<e.blkIndent);){for(r=0;r<i&&!o[r](e,a,n,!1);r++);if(e.tight=!l,e.isEmpty(e.line-1)&&(l=!0),(a=e.line)<n&&e.isEmpty(a)){if(l=!0,++a<n&&"list"===e.parentType&&e.isEmpty(a))break;e.line=a}}};var Mr=/[\n\t]/g,Rr=/\r[\n\u0085]|[\u2424\u2028\u0085]/g,zr=/\u00a0/g;function Nr(e){switch(e){case 10:case 92:case 96:case 42:case 95:case 94:case 91:case 93:case 33:case 38:case 60:case 62:case 123:case 125:case 36:case 37:case 64:case 126:case 43:case 61:case 58:return!0;default:return!1}}Fr.prototype.parse=function(e,t,n,r){var o,i=0,a=0;if(!e)return[];(e=(e=e.replace(zr," ")).replace(Rr,"\n")).indexOf("\t")>=0&&(e=e.replace(Mr,(function(t,n){var r;return 10===e.charCodeAt(n)?(i=n+1,a=0,t):(r="    ".slice((n-i-a)%4),a=n-i+1,r)}))),o=new Or(e,this,t,n,r),this.tokenize(o,o.line,o.lineMax)};for(var Lr=[],qr=0;qr<256;qr++)Lr.push(0);function Ur(e){return e>=48&&e<=57||e>=65&&e<=90||e>=97&&e<=122}function Wr(e,t){var n,r,o,i=t,a=!0,l=!0,s=e.posMax,c=e.src.charCodeAt(t);for(n=t>0?e.src.charCodeAt(t-1):-1;i<s&&e.src.charCodeAt(i)===c;)i++;return i>=s&&(a=!1),(o=i-t)>=4?a=l=!1:(32!==(r=i<s?e.src.charCodeAt(i):-1)&&10!==r||(a=!1),32!==n&&10!==n||(l=!1),95===c&&(Ur(n)&&(a=!1),Ur(r)&&(l=!1))),{can_open:a,can_close:l,delims:o}}"\\!\"#$%&'()*+,./:;<=>?@[]^_`{|}~-".split("").forEach((function(e){Lr[e.charCodeAt(0)]=1}));var Vr=/\\([ \\!"#$%&'()*+,.\/:;<=>?@[\]^_`{|}~-])/g,Hr=/\\([ \\!"#$%&'()*+,.\/:;<=>?@[\]^_`{|}~-])/g,Yr=["coap","doi","javascript","aaa","aaas","about","acap","cap","cid","crid","data","dav","dict","dns","file","ftp","geo","go","gopher","h323","http","https","iax","icap","im","imap","info","ipp","iris","iris.beep","iris.xpc","iris.xpcs","iris.lwz","ldap","mailto","mid","msrp","msrps","mtqp","mupdate","news","nfs","ni","nih","nntp","opaquelocktoken","pop","pres","rtsp","service","session","shttp","sieve","sip","sips","sms","snmp","soap.beep","soap.beeps","tag","tel","telnet","tftp","thismessage","tn3270","tip","tv","urn","vemmi","ws","wss","xcon","xcon-userid","xmlrpc.beep","xmlrpc.beeps","xmpp","z39.50r","z39.50s","adiumxtra","afp","afs","aim","apt","attachment","aw","beshare","bitcoin","bolo","callto","chrome","chrome-extension","com-eventbrite-attendee","content","cvs","dlna-playsingle","dlna-playcontainer","dtn","dvb","ed2k","facetime","feed","finger","fish","gg","git","gizmoproject","gtalk","hcp","icon","ipn","irc","irc6","ircs","itms","jar","jms","keyparc","lastfm","ldaps","magnet","maps","market","message","mms","ms-help","msnim","mumble","mvn","notes","oid","palm","paparazzi","platform","proxy","psyc","query","res","resource","rmi","rsync","rtmp","secondlife","sftp","sgn","skype","smb","soldat","spotify","ssh","steam","svn","teamspeak","things","udp","unreal","ut2004","ventrilo","view-source","webcal","wtai","wyciwyg","xfire","xri","ymsgr"],Kr=/^<([a-zA-Z0-9.!#$%&'*+\/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*)>/,Zr=/^<([a-zA-Z.\-]{1,25}):([^<>\x00-\x20]*)>/;function Qr(e,t){return e=e.source,t=t||"",function n(r,o){return r?(o=o.source||o,e=e.replace(r,o),n):new RegExp(e,t)}}var $r=Qr(/(?:unquoted|single_quoted|double_quoted)/)("unquoted",/[^"'=<>`\x00-\x20]+/)("single_quoted",/'[^']*'/)("double_quoted",/"[^"]*"/)(),Gr=Qr(/(?:\s+attr_name(?:\s*=\s*attr_value)?)/)("attr_name",/[a-zA-Z_:][a-zA-Z0-9:._-]*/)("attr_value",$r)(),Jr=Qr(/<[A-Za-z][A-Za-z0-9]*attribute*\s*\/?>/)("attribute",Gr)(),Xr=Qr(/^(?:open_tag|close_tag|comment|processing|declaration|cdata)/)("open_tag",Jr)("close_tag",/<\/[A-Za-z][A-Za-z0-9]*\s*>/)("comment",/<!---->|<!--(?:-?[^>-])(?:-?[^-])*-->/)("processing",/<[?].*?[?]>/)("declaration",/<![A-Z]+\s+[^>]*>/)("cdata",/<!\[CDATA\[[\s\S]*?\]\]>/)(),eo=/^&#((?:x[a-f0-9]{1,8}|[0-9]{1,8}));/i,to=/^&([a-z][a-z0-9]{1,31});/i,no=[["text",function(e,t){for(var n=e.pos;n<e.posMax&&!Nr(e.src.charCodeAt(n));)n++;return n!==e.pos&&(t||(e.pending+=e.src.slice(e.pos,n)),e.pos=n,!0)}],["newline",function(e,t){var n,r,o=e.pos;if(10!==e.src.charCodeAt(o))return!1;if(n=e.pending.length-1,r=e.posMax,!t)if(n>=0&&32===e.pending.charCodeAt(n))if(n>=1&&32===e.pending.charCodeAt(n-1)){for(var i=n-2;i>=0;i--)if(32!==e.pending.charCodeAt(i)){e.pending=e.pending.substring(0,i+1);break}e.push({type:"hardbreak",level:e.level})}else e.pending=e.pending.slice(0,-1),e.push({type:"softbreak",level:e.level});else e.push({type:"softbreak",level:e.level});for(o++;o<r&&32===e.src.charCodeAt(o);)o++;return e.pos=o,!0}],["escape",function(e,t){var n,r=e.pos,o=e.posMax;if(92!==e.src.charCodeAt(r))return!1;if(++r<o){if((n=e.src.charCodeAt(r))<256&&0!==Lr[n])return t||(e.pending+=e.src[r]),e.pos+=2,!0;if(10===n){for(t||e.push({type:"hardbreak",level:e.level}),r++;r<o&&32===e.src.charCodeAt(r);)r++;return e.pos=r,!0}}return t||(e.pending+="\\"),e.pos++,!0}],["backticks",function(e,t){var n,r,o,i,a,l=e.pos;if(96!==e.src.charCodeAt(l))return!1;for(n=l,l++,r=e.posMax;l<r&&96===e.src.charCodeAt(l);)l++;for(o=e.src.slice(n,l),i=a=l;-1!==(i=e.src.indexOf("`",a));){for(a=i+1;a<r&&96===e.src.charCodeAt(a);)a++;if(a-i===o.length)return t||e.push({type:"code",content:e.src.slice(l,i).replace(/[ \n]+/g," ").trim(),block:!1,level:e.level}),e.pos=a,!0}return t||(e.pending+=o),e.pos+=o.length,!0}],["del",function(e,t){var n,r,o,i,a,l=e.posMax,s=e.pos;if(126!==e.src.charCodeAt(s))return!1;if(t)return!1;if(s+4>=l)return!1;if(126!==e.src.charCodeAt(s+1))return!1;if(e.level>=e.options.maxNesting)return!1;if(i=s>0?e.src.charCodeAt(s-1):-1,a=e.src.charCodeAt(s+2),126===i)return!1;if(126===a)return!1;if(32===a||10===a)return!1;for(r=s+2;r<l&&126===e.src.charCodeAt(r);)r++;if(r>s+3)return e.pos+=r-s,t||(e.pending+=e.src.slice(s,r)),!0;for(e.pos=s+2,o=1;e.pos+1<l;){if(126===e.src.charCodeAt(e.pos)&&126===e.src.charCodeAt(e.pos+1)&&(i=e.src.charCodeAt(e.pos-1),126!==(a=e.pos+2<l?e.src.charCodeAt(e.pos+2):-1)&&126!==i&&(32!==i&&10!==i?o--:32!==a&&10!==a&&o++,o<=0))){n=!0;break}e.parser.skipToken(e)}return n?(e.posMax=e.pos,e.pos=s+2,t||(e.push({type:"del_open",level:e.level++}),e.parser.tokenize(e),e.push({type:"del_close",level:--e.level})),e.pos=e.posMax+2,e.posMax=l,!0):(e.pos=s,!1)}],["ins",function(e,t){var n,r,o,i,a,l=e.posMax,s=e.pos;if(43!==e.src.charCodeAt(s))return!1;if(t)return!1;if(s+4>=l)return!1;if(43!==e.src.charCodeAt(s+1))return!1;if(e.level>=e.options.maxNesting)return!1;if(i=s>0?e.src.charCodeAt(s-1):-1,a=e.src.charCodeAt(s+2),43===i)return!1;if(43===a)return!1;if(32===a||10===a)return!1;for(r=s+2;r<l&&43===e.src.charCodeAt(r);)r++;if(r!==s+2)return e.pos+=r-s,t||(e.pending+=e.src.slice(s,r)),!0;for(e.pos=s+2,o=1;e.pos+1<l;){if(43===e.src.charCodeAt(e.pos)&&43===e.src.charCodeAt(e.pos+1)&&(i=e.src.charCodeAt(e.pos-1),43!==(a=e.pos+2<l?e.src.charCodeAt(e.pos+2):-1)&&43!==i&&(32!==i&&10!==i?o--:32!==a&&10!==a&&o++,o<=0))){n=!0;break}e.parser.skipToken(e)}return n?(e.posMax=e.pos,e.pos=s+2,t||(e.push({type:"ins_open",level:e.level++}),e.parser.tokenize(e),e.push({type:"ins_close",level:--e.level})),e.pos=e.posMax+2,e.posMax=l,!0):(e.pos=s,!1)}],["mark",function(e,t){var n,r,o,i,a,l=e.posMax,s=e.pos;if(61!==e.src.charCodeAt(s))return!1;if(t)return!1;if(s+4>=l)return!1;if(61!==e.src.charCodeAt(s+1))return!1;if(e.level>=e.options.maxNesting)return!1;if(i=s>0?e.src.charCodeAt(s-1):-1,a=e.src.charCodeAt(s+2),61===i)return!1;if(61===a)return!1;if(32===a||10===a)return!1;for(r=s+2;r<l&&61===e.src.charCodeAt(r);)r++;if(r!==s+2)return e.pos+=r-s,t||(e.pending+=e.src.slice(s,r)),!0;for(e.pos=s+2,o=1;e.pos+1<l;){if(61===e.src.charCodeAt(e.pos)&&61===e.src.charCodeAt(e.pos+1)&&(i=e.src.charCodeAt(e.pos-1),61!==(a=e.pos+2<l?e.src.charCodeAt(e.pos+2):-1)&&61!==i&&(32!==i&&10!==i?o--:32!==a&&10!==a&&o++,o<=0))){n=!0;break}e.parser.skipToken(e)}return n?(e.posMax=e.pos,e.pos=s+2,t||(e.push({type:"mark_open",level:e.level++}),e.parser.tokenize(e),e.push({type:"mark_close",level:--e.level})),e.pos=e.posMax+2,e.posMax=l,!0):(e.pos=s,!1)}],["emphasis",function(e,t){var n,r,o,i,a,l,s,c=e.posMax,u=e.pos,p=e.src.charCodeAt(u);if(95!==p&&42!==p)return!1;if(t)return!1;if(n=(s=Wr(e,u)).delims,!s.can_open)return e.pos+=n,t||(e.pending+=e.src.slice(u,e.pos)),!0;if(e.level>=e.options.maxNesting)return!1;for(e.pos=u+n,l=[n];e.pos<c;)if(e.src.charCodeAt(e.pos)!==p)e.parser.skipToken(e);else{if(r=(s=Wr(e,e.pos)).delims,s.can_close){for(i=l.pop(),a=r;i!==a;){if(a<i){l.push(i-a);break}if(a-=i,0===l.length)break;e.pos+=i,i=l.pop()}if(0===l.length){n=i,o=!0;break}e.pos+=r;continue}s.can_open&&l.push(r),e.pos+=r}return o?(e.posMax=e.pos,e.pos=u+n,t||(2!==n&&3!==n||e.push({type:"strong_open",level:e.level++}),1!==n&&3!==n||e.push({type:"em_open",level:e.level++}),e.parser.tokenize(e),1!==n&&3!==n||e.push({type:"em_close",level:--e.level}),2!==n&&3!==n||e.push({type:"strong_close",level:--e.level})),e.pos=e.posMax+n,e.posMax=c,!0):(e.pos=u,!1)}],["sub",function(e,t){var n,r,o=e.posMax,i=e.pos;if(126!==e.src.charCodeAt(i))return!1;if(t)return!1;if(i+2>=o)return!1;if(e.level>=e.options.maxNesting)return!1;for(e.pos=i+1;e.pos<o;){if(126===e.src.charCodeAt(e.pos)){n=!0;break}e.parser.skipToken(e)}return n&&i+1!==e.pos?(r=e.src.slice(i+1,e.pos)).match(/(^|[^\\])(\\\\)*\s/)?(e.pos=i,!1):(e.posMax=e.pos,e.pos=i+1,t||e.push({type:"sub",level:e.level,content:r.replace(Vr,"$1")}),e.pos=e.posMax+1,e.posMax=o,!0):(e.pos=i,!1)}],["sup",function(e,t){var n,r,o=e.posMax,i=e.pos;if(94!==e.src.charCodeAt(i))return!1;if(t)return!1;if(i+2>=o)return!1;if(e.level>=e.options.maxNesting)return!1;for(e.pos=i+1;e.pos<o;){if(94===e.src.charCodeAt(e.pos)){n=!0;break}e.parser.skipToken(e)}return n&&i+1!==e.pos?(r=e.src.slice(i+1,e.pos)).match(/(^|[^\\])(\\\\)*\s/)?(e.pos=i,!1):(e.posMax=e.pos,e.pos=i+1,t||e.push({type:"sup",level:e.level,content:r.replace(Hr,"$1")}),e.pos=e.posMax+1,e.posMax=o,!0):(e.pos=i,!1)}],["links",function(e,t){var n,r,o,i,a,l,s,c,u=!1,p=e.pos,d=e.posMax,f=e.pos,h=e.src.charCodeAt(f);if(33===h&&(u=!0,h=e.src.charCodeAt(++f)),91!==h)return!1;if(e.level>=e.options.maxNesting)return!1;if(n=f+1,(r=lr(e,f))<0)return!1;if((l=r+1)<d&&40===e.src.charCodeAt(l)){for(l++;l<d&&(32===(c=e.src.charCodeAt(l))||10===c);l++);if(l>=d)return!1;for(f=l,ur(e,l)?(i=e.linkContent,l=e.pos):i="",f=l;l<d&&(32===(c=e.src.charCodeAt(l))||10===c);l++);if(l<d&&f!==l&&pr(e,l))for(a=e.linkContent,l=e.pos;l<d&&(32===(c=e.src.charCodeAt(l))||10===c);l++);else a="";if(l>=d||41!==e.src.charCodeAt(l))return e.pos=p,!1;l++}else{if(e.linkLevel>0)return!1;for(;l<d&&(32===(c=e.src.charCodeAt(l))||10===c);l++);if(l<d&&91===e.src.charCodeAt(l)&&(f=l+1,(l=lr(e,l))>=0?o=e.src.slice(f,l++):l=f-1),o||(void 0===o&&(l=r+1),o=e.src.slice(n,r)),!(s=e.env.references[dr(o)]))return e.pos=p,!1;i=s.href,a=s.title}return t||(e.pos=n,e.posMax=r,u?e.push({type:"image",src:i,title:a,alt:e.src.substr(n,r-n),level:e.level}):(e.push({type:"link_open",href:i,title:a,level:e.level++}),e.linkLevel++,e.parser.tokenize(e),e.linkLevel--,e.push({type:"link_close",level:--e.level}))),e.pos=l,e.posMax=d,!0}],["footnote_inline",function(e,t){var n,r,o,i,a=e.posMax,l=e.pos;return!(l+2>=a||94!==e.src.charCodeAt(l)||91!==e.src.charCodeAt(l+1)||e.level>=e.options.maxNesting||(n=l+2,(r=lr(e,l+1))<0||(t||(e.env.footnotes||(e.env.footnotes={}),e.env.footnotes.list||(e.env.footnotes.list=[]),o=e.env.footnotes.list.length,e.pos=n,e.posMax=r,e.push({type:"footnote_ref",id:o,level:e.level}),e.linkLevel++,i=e.tokens.length,e.parser.tokenize(e),e.env.footnotes.list[o]={tokens:e.tokens.splice(i)},e.linkLevel--),e.pos=r+1,e.posMax=a,0)))}],["footnote_ref",function(e,t){var n,r,o,i,a=e.posMax,l=e.pos;if(l+3>a)return!1;if(!e.env.footnotes||!e.env.footnotes.refs)return!1;if(91!==e.src.charCodeAt(l))return!1;if(94!==e.src.charCodeAt(l+1))return!1;if(e.level>=e.options.maxNesting)return!1;for(r=l+2;r<a;r++){if(32===e.src.charCodeAt(r))return!1;if(10===e.src.charCodeAt(r))return!1;if(93===e.src.charCodeAt(r))break}return!(r===l+2||r>=a||(r++,n=e.src.slice(l+2,r-1),void 0===e.env.footnotes.refs[":"+n]||(t||(e.env.footnotes.list||(e.env.footnotes.list=[]),e.env.footnotes.refs[":"+n]<0?(o=e.env.footnotes.list.length,e.env.footnotes.list[o]={label:n,count:0},e.env.footnotes.refs[":"+n]=o):o=e.env.footnotes.refs[":"+n],i=e.env.footnotes.list[o].count,e.env.footnotes.list[o].count++,e.push({type:"footnote_ref",id:o,subId:i,level:e.level})),e.pos=r,e.posMax=a,0)))}],["autolink",function(e,t){var n,r,o,i,a,l=e.pos;return!(60!==e.src.charCodeAt(l)||(n=e.src.slice(l)).indexOf(">")<0||((r=n.match(Zr))?Yr.indexOf(r[1].toLowerCase())<0||(a=cr(i=r[0].slice(1,-1)),!e.parser.validateLink(i)||(t||(e.push({type:"link_open",href:a,level:e.level}),e.push({type:"text",content:i,level:e.level+1}),e.push({type:"link_close",level:e.level})),e.pos+=r[0].length,0)):!(o=n.match(Kr))||(a=cr("mailto:"+(i=o[0].slice(1,-1))),!e.parser.validateLink(a)||(t||(e.push({type:"link_open",href:a,level:e.level}),e.push({type:"text",content:i,level:e.level+1}),e.push({type:"link_close",level:e.level})),e.pos+=o[0].length,0))))}],["htmltag",function(e,t){var n,r,o,i=e.pos;return!(!e.options.html||(o=e.posMax,60!==e.src.charCodeAt(i)||i+2>=o||33!==(n=e.src.charCodeAt(i+1))&&63!==n&&47!==n&&!function(e){var t=32|e;return t>=97&&t<=122}(n)||!(r=e.src.slice(i).match(Xr))||(t||e.push({type:"htmltag",content:e.src.slice(i,i+r[0].length),level:e.level}),e.pos+=r[0].length,0)))}],["entity",function(e,t){var n,r,o=e.pos,i=e.posMax;if(38!==e.src.charCodeAt(o))return!1;if(o+1<i)if(35===e.src.charCodeAt(o+1)){if(r=e.src.slice(o).match(eo))return t||(n="x"===r[1][0].toLowerCase()?parseInt(r[1].slice(1),16):parseInt(r[1],10),e.pending+=Vn(n)?Hn(n):Hn(65533)),e.pos+=r[0].length,!0}else if(r=e.src.slice(o).match(to)){var a=Nn(r[1]);if(r[1]!==a)return t||(e.pending+=a),e.pos+=r[0].length,!0}return t||(e.pending+="&"),e.pos++,!0}]];function ro(){this.ruler=new ir;for(var e=0;e<no.length;e++)this.ruler.push(no[e][0],no[e][1]);this.validateLink=oo}function oo(e){var t=e.trim().toLowerCase();return-1===(t=Qn(t)).indexOf(":")||-1===["vbscript","javascript","file","data"].indexOf(t.split(":")[0])}ro.prototype.skipToken=function(e){var t,n,r=this.ruler.getRules(""),o=r.length,i=e.pos;if((n=e.cacheGet(i))>0)e.pos=n;else{for(t=0;t<o;t++)if(r[t](e,!0))return void e.cacheSet(i,e.pos);e.pos++,e.cacheSet(i,e.pos)}},ro.prototype.tokenize=function(e){for(var t,n,r=this.ruler.getRules(""),o=r.length,i=e.posMax;e.pos<i;){for(n=0;n<o&&!(t=r[n](e,!1));n++);if(t){if(e.pos>=i)break}else e.pending+=e.src[e.pos++]}e.pending&&e.pushPending()},ro.prototype.parse=function(e,t,n,r){var o=new ar(e,this,t,n,r);this.tokenize(o)};var io={default:{options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkTarget:"",typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["block","inline","references","replacements","smartquotes","references","abbr2","footnote_tail"]},block:{rules:["blockquote","code","fences","footnote","heading","hr","htmlblock","lheading","list","paragraph","table"]},inline:{rules:["autolink","backticks","del","emphasis","entity","escape","footnote_ref","htmltag","links","newline","text"]}}},full:{options:{html:!1,xhtmlOut:!1,breaks:!1,langPrefix:"language-",linkTarget:"",typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{},block:{},inline:{}}},commonmark:{options:{html:!0,xhtmlOut:!0,breaks:!1,langPrefix:"language-",linkTarget:"",typographer:!1,quotes:"“”‘’",highlight:null,maxNesting:20},components:{core:{rules:["block","inline","references","abbr2"]},block:{rules:["blockquote","code","fences","heading","hr","htmlblock","lheading","list","paragraph"]},inline:{rules:["autolink","backticks","emphasis","entity","escape","htmltag","links","newline","text"]}}}};function ao(e,t,n){this.src=t,this.env=n,this.options=e.options,this.tokens=[],this.inlineMode=!1,this.inline=e.inline,this.block=e.block,this.renderer=e.renderer,this.typographer=e.typographer}function lo(e,t){"string"!=typeof e&&(t=e,e="default"),t&&null!=t.linkify&&console.warn("linkify option is removed. Use linkify plugin instead:\n\nimport Remarkable from 'remarkable';\nimport linkify from 'remarkable/linkify';\nnew Remarkable().use(linkify)\n"),this.inline=new ro,this.block=new Fr,this.core=new xr,this.renderer=new or,this.ruler=new ir,this.options={},this.configure(io[e]),this.set(t||{})}lo.prototype.set=function(e){qn(this.options,e)},lo.prototype.configure=function(e){var t=this;if(!e)throw new Error("Wrong `remarkable` preset, check name/content");e.options&&t.set(e.options),e.components&&Object.keys(e.components).forEach((function(n){e.components[n].rules&&t[n].ruler.enable(e.components[n].rules,!0)}))},lo.prototype.use=function(e,t){return e(this,t),this},lo.prototype.parse=function(e,t){var n=new ao(this,e,t);return this.core.process(n),n.tokens},lo.prototype.render=function(e,t){return t=t||{},this.renderer.render(this.parse(e,t),this.options,t)},lo.prototype.parseInline=function(e,t){var n=new ao(this,e,t);return n.inlineMode=!0,this.core.process(n),n.tokens},lo.prototype.renderInline=function(e,t){return t=t||{},this.renderer.render(this.parseInline(e,t),this.options,t)};var so=function(e){return e.map((function(e,t){return t?e.charAt(0).toUpperCase()+e.substring(1):e})).join("")},co=function(e){return e.join("-")},uo=function(e){return e.join("_")},po=[],fo=[];[["align","content"],["align","items"],["alignment","adjust"],["alignment","baseline"],["align","self"],["animation","delay"],["animation","direction"],["animation","iteration","count"],["animation","name"],["animation","play","state"],["appearance"],["backface","visibility"],["background"],["background","attachment"],["background","blend","mode"],["background","color"],["background","composite"],["background","image"],["background","origin"],["background","position"],["background","repeat"],["baseline","shift"],["behavior"],["border"],["border","bottom"],["border","bottom","color"],["border","bottom","left","radius"],["border","bottom","right","radius"],["border","bottom","style"],["border","bottom","width"],["border","collapse"],["border","color"],["border","corner","shape"],["border","image","source"],["border","image","width"],["border","left"],["border","left","color"],["border","left","style"],["border","left","width"],["border","radius"],["border","right"],["border","right","color"],["border","right","style"],["border","right","width"],["border","spacing"],["border","style"],["border","top"],["border","top","color"],["border","top","left","radius"],["border","top","right","radius"],["border","top","style"],["border","top","width"],["border","width"],["bottom"],["box","align"],["box","decoration","break"],["box","direction"],["box","flex"],["box","flex","group"],["box","line","progression"],["box","lines"],["box","ordinal","group"],["box","shadow"],["break","after"],["break","before"],["break","inside"],["clear"],["clip"],["clip","rule"],["color"],["column","count"],["column","fill"],["column","gap"],["column","rule"],["column","rule","color"],["column","rule","width"],["columns"],["column","span"],["column","width"],["counter","increment"],["counter","reset"],["cue"],["cue","after"],["cursor"],["direction"],["display"],["fill"],["fill","opacity"],["fill","rule"],["filter"],["flex"],["flex","align"],["flex","basis"],["flex","direction"],["flex","flow"],["flex","grow"],["flex","item","align"],["flex","line","pack"],["flex","order"],["flex","shrink"],["flex","wrap"],["float"],["flow","from"],["font"],["font","family"],["font","kerning"],["font","size"],["font","size","adjust"],["font","stretch"],["font","style"],["font","synthesis"],["font","variant"],["font","variant","alternates"],["font","weight"],["grid","area"],["grid","column"],["grid","column","end"],["grid","column","start"],["grid","row"],["grid","row","end"],["grid","row","position"],["grid","row","span"],["grid","template","areas"],["grid","template","columns"],["grid","template","rows"],["height"],["hyphenate","limit","chars"],["hyphenate","limit","lines"],["hyphenate","limit","zone"],["hyphens"],["ime","mode"],["justify","content"],["layout","grid"],["layout","grid","char"],["layout","grid","line"],["layout","grid","mode"],["layout","grid","type"],["left"],["letter","spacing"],["line","break"],["line","clamp"],["line","height"],["list","style"],["list","style","image"],["list","style","position"],["list","style","type"],["margin"],["margin","bottom"],["margin","left"],["margin","right"],["margin","top"],["marquee","direction"],["marquee","style"],["mask"],["mask","border"],["mask","border","repeat"],["mask","border","slice"],["mask","border","source"],["mask","border","width"],["mask","clip"],["mask","origin"],["max","font","size"],["max","height"],["max","width"],["min","height"],["min","width"],["opacity"],["order"],["orphans"],["outline"],["outline","color"],["outline","offset"],["overflow"],["overflow","style"],["overflow","x"],["overflow","y"],["padding"],["padding","bottom"],["padding","left"],["padding","right"],["padding","top"],["page","break","after"],["page","break","before"],["page","break","inside"],["pause"],["pause","after"],["pause","before"],["perspective"],["perspective","origin"],["pointer","events"],["position"],["punctuation","trim"],["quotes"],["region","fragment"],["rest","after"],["rest","before"],["right"],["ruby","align"],["ruby","position"],["shape","image","threshold"],["shape","inside"],["shape","margin"],["shape","outside"],["speak"],["speak","as"],["stroke","opacity"],["stroke","width"],["table","layout"],["tab","size"],["text","align"],["text","align","last"],["text","decoration"],["text","decoration","color"],["text","decoration","line"],["text","decoration","line","through"],["text","decoration","none"],["text","decoration","overline"],["text","decoration","skip"],["text","decoration","style"],["text","decoration","underline"],["text","emphasis"],["text","emphasis","color"],["text","emphasis","style"],["text","height"],["text","indent"],["text","justify","trim"],["text","kashida","space"],["text","line","through"],["text","line","through","color"],["text","line","through","mode"],["text","line","through","style"],["text","line","through","width"],["text","overflow"],["text","overline"],["text","overline","color"],["text","overline","mode"],["text","overline","style"],["text","overline","width"],["text","rendering"],["text","script"],["text","shadow"],["text","transform"],["text","underline","position"],["text","underline","style"],["top"],["touch","action"],["transform"],["transform","origin"],["transform","origin","z"],["transform","style"],["transition"],["transition","delay"],["transition","duration"],["transition","property"],["transition","timing","function"],["unicode","bidi"],["unicode","range"],["user","focus"],["user","input"],["vertical","align"],["visibility"],["voice","balance"],["voice","duration"],["voice","family"],["voice","pitch"],["voice","range"],["voice","rate"],["voice","stress"],["voice","volume"],["white","space"],["white","space","treatment"],["widows"],["width"],["word","break"],["word","spacing"],["word","wrap"],["wrap","flow"],["wrap","margin"],["wrap","option"],["writing","mode"],["z","index"],["zoom"]].forEach((function(e){var t=so(e);po.push(t),fo.push([t,t]),fo.push([co(e),t]),fo.push([uo(e),t])}));var ho=new Map(fo),vo=po;function bo(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Ao,yo=function e(t){return null!==t&&"object"==typeof t?(0,i.u4g)((function(t,n){var r=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],s=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return bo(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?bo(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(n,2),o=r[0],a=r[1];return(0,i.yGi)(so(o.split("_")),e(a),t)}),{},(0,i.Zpf)(t)):Array.isArray(t)?t.map(e,t):t};function go(){go=function(){return t};var e,t={},n=Object.prototype,r=n.hasOwnProperty,o=Object.defineProperty||function(e,t,n){e[t]=n.value},i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",l=i.asyncIterator||"@@asyncIterator",s=i.toStringTag||"@@toStringTag";function c(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{c({},"")}catch(e){c=function(e,t,n){return e[t]=n}}function u(e,t,n,r){var i=t&&t.prototype instanceof A?t:A,a=Object.create(i.prototype),l=new j(r||[]);return o(a,"_invoke",{value:O(e,n,l)}),a}function p(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}t.wrap=u;var d="suspendedStart",f="suspendedYield",h="executing",v="completed",b={};function A(){}function y(){}function g(){}var m={};c(m,a,(function(){return this}));var w=Object.getPrototypeOf,E=w&&w(w(P([])));E&&E!==n&&r.call(E,a)&&(m=E);var C=g.prototype=A.prototype=Object.create(m);function k(e){["next","throw","return"].forEach((function(t){c(e,t,(function(e){return this._invoke(t,e)}))}))}function x(e,t){function n(o,i,a,l){var s=p(e[o],e,i);if("throw"!==s.type){var c=s.arg,u=c.value;return u&&"object"==typeof u&&r.call(u,"__await")?t.resolve(u.__await).then((function(e){n("next",e,a,l)}),(function(e){n("throw",e,a,l)})):t.resolve(u).then((function(e){c.value=e,a(c)}),(function(e){return n("throw",e,a,l)}))}l(s.arg)}var i;o(this,"_invoke",{value:function(e,r){function o(){return new t((function(t,o){n(e,r,t,o)}))}return i=i?i.then(o,o):o()}})}function O(t,n,r){var o=d;return function(i,a){if(o===h)throw new Error("Generator is already running");if(o===v){if("throw"===i)throw a;return{value:e,done:!0}}for(r.method=i,r.arg=a;;){var l=r.delegate;if(l){var s=S(l,r);if(s){if(s===b)continue;return s}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if(o===d)throw o=v,r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);o=h;var c=p(t,n,r);if("normal"===c.type){if(o=r.done?v:f,c.arg===b)continue;return{value:c.arg,done:r.done}}"throw"===c.type&&(o=v,r.method="throw",r.arg=c.arg)}}}function S(t,n){var r=n.method,o=t.iterator[r];if(o===e)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=e,S(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),b;var i=p(o,t.iterator,n.arg);if("throw"===i.type)return n.method="throw",n.arg=i.arg,n.delegate=null,b;var a=i.arg;return a?a.done?(n[t.resultName]=a.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=e),n.delegate=null,b):a:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,b)}function B(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function _(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function j(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(B,this),this.reset(!0)}function P(t){if(t||""===t){var n=t[a];if(n)return n.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var o=-1,i=function n(){for(;++o<t.length;)if(r.call(t,o))return n.value=t[o],n.done=!1,n;return n.value=e,n.done=!0,n};return i.next=i}}throw new TypeError(typeof t+" is not iterable")}return y.prototype=g,o(C,"constructor",{value:g,configurable:!0}),o(g,"constructor",{value:y,configurable:!0}),y.displayName=c(g,s,"GeneratorFunction"),t.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===y||"GeneratorFunction"===(t.displayName||t.name))},t.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,g):(e.__proto__=g,c(e,s,"GeneratorFunction")),e.prototype=Object.create(C),e},t.awrap=function(e){return{__await:e}},k(x.prototype),c(x.prototype,l,(function(){return this})),t.AsyncIterator=x,t.async=function(e,n,r,o,i){void 0===i&&(i=Promise);var a=new x(u(e,n,r,o),i);return t.isGeneratorFunction(n)?a:a.next().then((function(e){return e.done?e.value:a.next()}))},k(C),c(C,s,"Generator"),c(C,a,(function(){return this})),c(C,"toString",(function(){return"[object Generator]"})),t.keys=function(e){var t=Object(e),n=[];for(var r in t)n.push(r);return n.reverse(),function e(){for(;n.length;){var r=n.pop();if(r in t)return e.value=r,e.done=!1,e}return e.done=!0,e}},t.values=P,j.prototype={constructor:j,reset:function(t){if(this.prev=0,this.next=0,this.sent=this._sent=e,this.done=!1,this.delegate=null,this.method="next",this.arg=e,this.tryEntries.forEach(_),!t)for(var n in this)"t"===n.charAt(0)&&r.call(this,n)&&!isNaN(+n.slice(1))&&(this[n]=e)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(t){if(this.done)throw t;var n=this;function o(r,o){return l.type="throw",l.arg=t,n.next=r,o&&(n.method="next",n.arg=e),!!o}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],l=a.completion;if("root"===a.tryLoc)return o("end");if(a.tryLoc<=this.prev){var s=r.call(a,"catchLoc"),c=r.call(a,"finallyLoc");if(s&&c){if(this.prev<a.catchLoc)return o(a.catchLoc,!0);if(this.prev<a.finallyLoc)return o(a.finallyLoc)}else if(s){if(this.prev<a.catchLoc)return o(a.catchLoc,!0)}else{if(!c)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return o(a.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&r.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var a=i?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,b):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),b},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),_(n),b}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var r=n.completion;if("throw"===r.type){var o=r.arg;_(n)}return o}}throw new Error("illegal catch attempt")},delegateYield:function(t,n,r){return this.delegate={iterator:P(t),resultName:n,nextLoc:r},"next"===this.method&&(this.arg=e),b}},t}function mo(e,t,n,r,o,i,a){try{var l=e[i](a),s=l.value}catch(e){return void n(e)}l.done?t(s):Promise.resolve(s).then(r,o)}function wo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Eo(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,ko(r.key),r)}}function Co(e,t,n){return(t=ko(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ko(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var xo=function(){function e(t){var n=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Co(this,"options",void 0),Co(this,"md",void 0),Co(this,"render",(function(e){return n.md.render(e)})),this.options=t,this.md=new lo(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?wo(Object(n),!0).forEach((function(t){Co(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):wo(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({highlight:function(t,n){if(e.hljs){if(n&&e.hljs.getLanguage(n))try{return e.hljs.highlight(n,t).value}catch(e){}try{return e.hljs.highlightAuto(t).value}catch(e){}}else e.loadhljs();return""}},yo(this.options)))}var t,n,r,o;return t=e,null,n=[{key:"isReady",get:function(){return e._isReady}},{key:"loadhljs",value:(r=go().mark((function t(){return go().wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,j.Z.hljs;case 2:e.hljs=t.sent,e.hljsResolve(),e._isReady=!0;case 5:case"end":return t.stop()}}),t)})),o=function(){var e=this,t=arguments;return new Promise((function(n,o){var i=r.apply(e,t);function a(e){mo(i,n,o,a,l,"next",e)}function l(e){mo(i,n,o,a,l,"throw",e)}a(void 0)}))},function(){return o.apply(this,arguments)})}],n&&Eo(t,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();Ao=xo,Co(xo,"hljs",void 0),Co(xo,"hljsResolve",void 0),Co(xo,"_isReady",new Promise((function(e){Ao.hljsResolve=e})));var Oo=xo;function So(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Bo(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,To(r.key),r)}}function _o(e,t){return _o=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},_o(e,t)}function jo(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Po(e){return Po=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Po(e)}function Do(e,t,n){return(t=To(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function To(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var Io=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&_o(e,t)}(s,e);var t,n,r,i,l=(r=s,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Po(r);if(i){var n=Po(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return jo(e)}(this,e)});function s(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),Do(jo(t=l.call(this,e)),"getMarkdown",(0,a.qe)((function(e,t,n){return{dangerouslySetInnerHTML:{__html:t.render(String(e))}}}))),!0!==Oo.isReady&&Oo.isReady.then((function(){t.setState({})})),t}return t=s,n=[{key:"componentDidUpdate",value:function(){this.setFocus()}},{key:"componentDidMount",value:function(){this.setFocus()}},{key:"render",value:function(){var e=this.props,t=e.className,n=e.markdown,r=e.value;return o().createElement("div",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?So(Object(n),!0).forEach((function(t){Do(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):So(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({ref:"el",className:[t,"cell-markdown"].join(" ")},this.getMarkdown(r,n,Oo.isReady)))}},{key:"setFocus",value:function(){var e=this.props,t=e.active,n=e.applyFocus;if(t){var r=this.refs.el;if(n&&r&&document.activeElement!==r){var o=_n.getFirstParentOfType(r,"td");o&&-1!==o.className.indexOf("phantom-cell")&&o.focus()}}}}],n&&Bo(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),s}(r.PureComponent);function Fo(e){return function(e){if(Array.isArray(e))return Mo(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return Mo(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Mo(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Mo(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ro(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,No(r.key),r)}}function zo(e,t,n){return(t=No(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function No(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var Lo,qo=i.h0F(i.UID),Uo=i.h0F(i.UID);!function(e){e[e.Dropdown=0]="Dropdown",e[e.DropdownLabel=1]="DropdownLabel",e[e.Input=2]="Input",e[e.Label=3]="Label",e[e.Markdown=4]="Markdown"}(Lo||(Lo={}));var Wo=function(){function e(t){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:mt(t);!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),zo(this,"handlers",void 0),zo(this,"cell_selectable",void 0),zo(this,"partialGet",(0,a.qe)((function(e,t,r,o,a,l,s){var c=i.UID(zn,e);return qo((function(t,r){return Uo((function(e,i){return n.getContent(!1,!1,o,e,a&&a[r][i],i,r,t,c,l,s)}),e)}),t)}))),zo(this,"get",(0,a.qe)((function(e,t,r,o,a,l,s,c,u,p){if(!t)return e;var d=t.row,f=t.column,h=d-l.rows,v=f-l.columns;if(h<0||v<0||a.length<=h||o.length<=v)return e;var b=i.UID(zn,o);return(e=Ve(e))[h][v]=n.getContent(!0,r||!1,s,o[v],c&&c[h][v],f,d,a[h],b,u,p),e}))),this.handlers=r,this.cell_selectable=t().cell_selectable}var t,n;return t=e,n=[{key:"getContent",value:function(e,t,n,r,i,a,l,s,c,u,p){var d=[].concat(Fo(e?["input-active"]:[]),[n?"focused":"unfocused"],Fo(this.cell_selectable?["selectable"]:[]),["dash-cell-value"]).join(" "),f=function(e,t,n,r,o){switch(r){case _.Ap.Input:return e&&t&&!o?Lo.Input:Lo.Label;case _.Ap.Dropdown:return n&&t?Lo.Dropdown:Lo.DropdownLabel;case _.Ap.Markdown:return Lo.Markdown;default:return e&&t&&!o?Lo.Input:Lo.Label}}(e,r.editable,i&&i.options,r.presentation,u);switch(f){case Lo.Dropdown:return o().createElement(Mn,{key:"column-".concat(a),active:e,applyFocus:t,clearable:i&&i.clearable,dropdown:i&&i.options,onChange:this.handlers(nt.Change,l,a),value:s[r.id],disabled:u});case Lo.Input:return o().createElement(Dt,{key:"column-".concat(a),active:e,applyFocus:t,className:d,focused:n,onChange:this.handlers(nt.Change,l,a),onMouseUp:this.handlers(nt.MouseUp,l,a),onPaste:this.handlers(nt.Paste,l,a),type:r.type,value:s[r.id]});case Lo.Markdown:return o().createElement(Io,{key:"column-".concat(a),active:e,applyFocus:t,className:d,markdown:p,value:s[r.id]});case Lo.DropdownLabel:case Lo.Label:default:var h=f===Lo.DropdownLabel?this.resolveDropdownLabel(i,s[r.id]):c[a](s[r.id]);return o().createElement(Mt,{active:e,applyFocus:t,className:d,key:"column-".concat(a),value:h})}}},{key:"resolveDropdownLabel",value:function(e,t){var n=e&&e.options&&e.options.find((function(e){return e.value===t}));return n?n.label:t}}],n&&Ro(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Vo(e){return function(e){if(Array.isArray(e))return Ho(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return Ho(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ho(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ho(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Yo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ko(e,t,n,r,a,l){return o().createElement("td",{key:"select",className:"dash-select-cell",style:{width:"30px",maxWidth:"30px",minWidth:"30px",textAlign:"center"}},o().createElement("input",{type:"single"===n?"radio":"checkbox",style:{verticalAlign:"middle"},name:"row-select-".concat(e),checked:i.q9t(t,r),onChange:function(){var e="single"===n?[t]:i.KJl(i.q9t(t),i.zud([t]),i.R3I(t))(r);a({selected_rows:e,selected_row_ids:i.UID((function(e){return l[e].id}),e)})}}))}var Zo,Qo=(0,a.Pi)((function(e,t,n,r,a,l,s,c){return i.h0F(i.UID)((function(n,u){return[].concat(Vo(l?[(p=function(){return c(function(e,t,n){var r=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Yo(Object(n),!0).forEach((function(t){var r,o,i;r=e,o=t,i=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Yo(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({data:i.OdJ(e,1,t)},B);return i.is(Array,n)&&i.YjB((function(t){return t>=e}),n)&&(r.selected_rows=i.UID((function(t){return t>e?t-1:t}),i.zud([e],n)),r.selected_row_ids=i.UID((function(e){return r.data[e].id}),r.selected_rows)),r}(r[u],t,s))},o().createElement("td",{key:"delete",className:"dash-delete-cell",onClick:function(){return p()},style:{width:"30px",maxWidth:"30px",minWidth:"30px"}},"×"))]:[]),Vo(a?[Ko(e,r[u],a,s,c,t)]:[]));var p}),n)}));function $o(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Go(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?$o(Object(n),!0).forEach((function(t){var r,o,i;r=e,o=t,i=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):$o(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function Jo(e){return Go(Go({},e),{},{if:function(){return!1},terminal:!1})}!function(e){e.BlockClose="close-block",e.BlockOpen="open-block",e.LogicalOperator="logical-operator",e.RelationalOperator="relational-operator",e.UnaryOperator="unary-operator",e.Expression="expression"}(Zo||(Zo={}));var Xo=function e(t){var n=0,r=t.map((function(e){var t=Object.assign({},e,{nesting:n});return n+=e.lexeme.nesting||0,t})),o=r.filter((function(e){return 0===e.nesting&&"number"==typeof e.lexeme.priority})).sort((function(e,t){return(t.lexeme.priority||-1)-(e.lexeme.priority||-1)}))[0];l.ZP.trace("parser -> pivot",o,t);var i=r.indexOf(o);if(o.lexeme.syntaxer){var a=o.lexeme.syntaxer(t,o,i);return Array.isArray(a.left)&&(a.left=e(a.left)),Array.isArray(a.right)&&(a.right=e(a.right)),Array.isArray(a.block)&&(a.block=e(a.block)),a}throw new Error(o.lexeme.type)};function ei(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,ni(r.key),r)}}function ti(e,t,n){return(t=ni(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ni(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}function ri(e){var t=e.block,n=e.left,r=e.lexeme,o=e.right,i=e.value,a={subType:r.subType,type:r.type,value:r.present?r.present(e):i};return t&&(a.block=ri(t)),n&&(a.left=ri(n)),o&&(a.right=ri(o)),a}var oi,ii=function(){function e(t,n){var r=this,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:function(e){return e};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),ti(this,"lexicon",void 0),ti(this,"query",void 0),ti(this,"lexerResult",void 0),ti(this,"syntaxerResult",void 0),ti(this,"evaluate",(function(e){if(!r.isValid){var t="DataTable filtering syntax is invalid for query: ".concat(r.query);throw l.ZP.error(t),new Error(t)}return!(r.tree&&r.tree.lexeme&&r.tree.lexeme.evaluate)||r.tree.lexeme.evaluate(e,r.tree)})),ti(this,"filter",(function(e){return e.filter(r.evaluate)})),this.lexicon=t,this.query=n,this.lexerResult=o(G(this.lexicon,this.query)),this.syntaxerResult=function(e){var t=e.lexemes;if(!e.valid)return{valid:!1,error:"lexer -- ".concat(e.error)};if(0===e.lexemes.length)return{valid:!0};try{return{tree:Xo(t),valid:!0}}catch(e){return{valid:!1,error:String(e)}}}(this.lexerResult)}var t,n;return t=e,(n=[{key:"isValid",get:function(){return this.syntaxerResult.valid}},{key:"tree",get:function(){return this.syntaxerResult.tree}},{key:"toQueryString",value:function(){return this.lexerResult.valid?i.UID((function(e){return e.lexeme.transform?e.lexeme.transform(e.value):e.value}),this.lexerResult.lexemes).join(" "):""}},{key:"toStructure",value:function(){return this.isValid&&this.syntaxerResult.tree?ri(this.syntaxerResult.tree):null}}])&&ei(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),ai=/^{(([^{}\\]|\\.)+)}/,li=/^(('([^'\\]|\\.)*')|("([^"\\]|\\.)*")|(`([^`\\]|\\.)*`))/,si=function(e){return e.slice(1,e.length-1).replace(/\\(.)/g,"$1")},ci={present:function(e){return si(e.value)},resolve:function(e,t){if(ai.test(t.value))return e[si(t.value)];throw new Error},regexp:ai,subType:"field",type:Zo.Expression},ui=function(e){return e.slice(1,e.length-1).replace(/\\(.)/g,"$1")},pi={present:function(e){return ui(e.value)},resolve:function(e,t){if(li.test(t.value))return ui(t.value);throw new Error},regexp:li,subType:"value",type:Zo.Expression},di=function(e,t){var n=function(e){return function(t){return t=t.match(e)[1],he()(t)?+t:t.replace(/\\(.)/g,"$1")}}(e);return{present:function(e){return n(e.value)},resolve:function(t,r){if(e.test(r.value))return n(r.value);throw new Error},regexp:e,regexpMatch:1,subType:"value",transform:t,type:Zo.Expression}},fi=di(/^(([^\s'"`{}()\\]|\\.)+)(?:[\s)]|$)/),hi=di(/^(([^'"`{}()\\]|\\.)+)$/,(function(e){return"string"==typeof e&&-1!==e.indexOf(" ")?'"'.concat(e,'"'):e}));!function(e){e.And="&&",e.Or="||"}(oi||(oi={}));var vi,bi={evaluate:function(e,t){l.ZP.trace("evaluate -> &&",e,t);var n=t,r=n.left.lexeme.evaluate(e,n.left),o=n.right.lexeme.evaluate(e,n.right);return r&&o},type:Zo.LogicalOperator,priority:2,regexp:/^(and\s|&&)/i,subType:oi.And,syntaxer:function(e,t,n){return Object.assign({left:e.slice(0,n),right:e.slice(n+1)},t)}},Ai={evaluate:function(e,t){l.ZP.trace("evaluate -> ||",e,t);var n=t;return n.left.lexeme.evaluate(e,n.left)||n.right.lexeme.evaluate(e,n.right)},type:Zo.LogicalOperator,subType:oi.Or,priority:3,regexp:/^(or\s|\|\|)/i,syntaxer:function(e,t,n){return Object.assign({left:e.slice(0,n),right:e.slice(n+1)},t)}};function yi(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],s=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return gi(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?gi(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function gi(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function mi(e){return function(t,n){return e(function(e,t){l.ZP.trace("evaluate -> relational",e,t);var n=t,r=n.left.lexeme.resolve(e,n.left),o=n.right.lexeme.resolve(e,n.right);return l.ZP.trace("opValue: ".concat(r,", expValue: ").concat(o)),[r,o,t.value]}(t,n))}}!function(e){e.Contains="contains",e.DateStartsWith="datestartswith",e.Equal="=",e.GreaterOrEqual=">=",e.GreaterThan=">",e.LessOrEqual="<=",e.LessThan="<",e.NotEqual="!="}(vi||(vi={}));var wi,Ei={priority:0,syntaxer:function(e){var t=yi(e,3),n=t[0],r=t[1],o=t[2];return Object.assign({left:n,right:o},r)},type:Zo.RelationalOperator},Ci=function(e,t,n,r){return"i"==r[0]?e(t.toString().toUpperCase(),n.toString().toUpperCase()):e(t,n)},ki=i.ATH({evaluate:mi((function(e){var t=yi(e,3),n=t[0],r=t[1],o=t[2];return!i.kKJ(r)&&!i.kKJ(n)&&("String"===i.dt8(r)||"String"===i.dt8(n))&&function(e,t,n){return"i"==n[0]?-1!==e.toString().toUpperCase().indexOf(t.toString().toUpperCase()):-1!==e.toString().indexOf(t.toString())}(n,r,o)})),subType:vi.Contains,regexp:/^((i|s)?contains)(?=\s|$)/i,regexpFlags:2,regexpMatch:1},Ei),xi=i.ATH({evaluate:mi((function(e){var t,n,r,o=yi(e,3);return t=o[0],n=o[1],r=o[2],he()(t)&&he()(n)?+t==+n:"i"==r[0]?t.toString().toUpperCase()===n.toString().toUpperCase():t===n})),subType:vi.Equal,regexp:/^((i|s)?(=|(eq)(?=\s|$)))/i,regexpFlags:2,regexpMatch:1},Ei),Oi=i.ATH({evaluate:mi((function(e){var t=yi(e,3),n=t[0],r=t[1],o=t[2];return Ci((function(e,t){return e>=t}),n,r,o)})),subType:vi.GreaterOrEqual,regexp:/^((i|s)?(>=|(ge)(?=\s|$)))/i,regexpFlags:2,regexpMatch:1},Ei),Si=i.ATH({evaluate:mi((function(e){var t=yi(e,3),n=t[0],r=t[1],o=t[2];return Ci((function(e,t){return e>t}),n,r,o)})),subType:vi.GreaterThan,regexp:/^((i|s)?(>|(gt)(?=\s|$)))/i,regexpFlags:2,regexpMatch:1},Ei),Bi={allow_YY:!0},_i=i.ATH({evaluate:mi((function(e){var t=yi(e,2),n=t[0],r=t[1];n="number"==typeof n?n.toString():n,r="number"==typeof r?r.toString():r;var o=Oe(n,Bi),a=Oe(r,Bi);return!i.kKJ(o)&&!i.kKJ(a)&&0===o.indexOf(a)})),subType:vi.DateStartsWith,regexp:/^((datestartswith)(?=\s|$))/i,regexpMatch:1},Ei),ji=i.ATH({evaluate:mi((function(e){var t=yi(e,3),n=t[0],r=t[1],o=t[2];return Ci((function(e,t){return e<=t}),n,r,o)})),subType:vi.LessOrEqual,regexp:/^((i|s)?(<=|(le)(?=\s|$)))/i,regexpFlags:2,regexpMatch:1},Ei),Pi=i.ATH({evaluate:mi((function(e){var t=yi(e,3),n=t[0],r=t[1],o=t[2];return Ci((function(e,t){return e<t}),n,r,o)})),subType:vi.LessThan,regexp:/^((i|s)?(<|(lt)(?=\s|$)))/i,regexpFlags:2,regexpMatch:1},Ei),Di=i.ATH({evaluate:mi((function(e){var t=yi(e,3),n=t[0],r=t[1],o=t[2];return Ci((function(e,t){return e!==t}),n,r,o)})),subType:vi.NotEqual,regexp:/^((i|s)?(!=|(ne)(?=\s|$)))/i,regexpFlags:2,regexpMatch:1},Ei);function Ti(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ii(e){return function(t,n){return e(function(e,t){l.ZP.trace("evaluate -> unary",e,t),l.ZP.trace("evaluate -> unary",e,t);var n=t;return n.left.lexeme.resolve(e,n.left)}(t,n))}}!function(e){e.Not="!"}(wi||(wi={}));var Fi={present:function(e){return e.value},priority:0,syntaxer:function(e){var t=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],s=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Ti(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ti(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(e,2),n=t[0],r=t[1];return Object.assign({left:n},r)},type:Zo.UnaryOperator},Mi={evaluate:function(e,t){l.ZP.trace("evaluate -> unary not",e,t);var n=t;return!n.right.lexeme.evaluate(e,n.right)},type:Zo.UnaryOperator,subType:wi.Not,priority:1.5,regexp:/^!/,syntaxer:function(e){return Object.assign({right:e.slice(1,e.length)},e[0])}},Ri=i.ATH({evaluate:Ii((function(e){return"boolean"==typeof e})),regexp:/^(is bool)/i},Fi),zi=i.ATH({evaluate:Ii((function(e){return"number"==typeof e&&e%2==0})),regexp:/^(is even)/i},Fi),Ni=i.ATH({evaluate:Ii((function(e){return null==e||""===e})),regexp:/^(is blank)/i},Fi),Li=i.ATH({evaluate:Ii((function(e){return null==e})),regexp:/^(is nil)/i},Fi),qi=i.ATH({evaluate:Ii((function(e){return"number"==typeof e})),regexp:/^(is num)/i},Fi),Ui=i.ATH({evaluate:Ii((function(e){return null!==e&&"object"==typeof e})),regexp:/^(is object)/i},Fi),Wi=i.ATH({evaluate:Ii((function(e){return"number"==typeof e&&e%2==1})),regexp:/^(is odd)/i},Fi),Vi=i.ATH({evaluate:Ii((function(e){return"number"==typeof e&&function(e){if(2===e)return!0;if(e<2||e%2==0)return!1;for(var t=3;t*t<=e;t+=2)if(e%t==0)return!1;return!0}(e)})),regexp:/^(is prime)/i},Fi),Hi=i.ATH({evaluate:Ii((function(e){return"string"==typeof e})),regexp:/^(is str)/i},Fi),Yi=i.u4g((function(e,t){return e+(t.lexeme.nesting||0)})),Ki=function(e,t){return 0===Yi(0,e)},Zi=function(e,t){return Ki(e)&&!!t&&i.q9t(t.lexeme.type,[Zo.RelationalOperator])},Qi=function(e,t){return!t||i.q9t(t.lexeme.type,[Zo.BlockOpen,Zo.LogicalOperator,Zo.RelationalOperator])},$i=function(e,t){return!t},Gi=function(e,t){return!!t&&i.q9t(t.lexeme.type,[Zo.BlockClose,Zo.Expression,Zo.UnaryOperator])},Ji=function(e,t){return!!t&&i.q9t(t.lexeme.type,[Zo.Expression])},Xi=Ji;function ea(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ta(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ea(Object(n),!0).forEach((function(t){var r,o,i;r=e,o=t,i=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ea(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function na(e){return function(e){if(Array.isArray(e))return ra(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return ra(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ra(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ra(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function oa(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(void 0,"symbol"==typeof(o=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(r.key))?o:String(o)),r)}var o}function ia(){return ia="undefined"!=typeof Reflect&&Reflect.get?Reflect.get.bind():function(e,t,n){var r=function(e,t){for(;!Object.prototype.hasOwnProperty.call(e,t)&&null!==(e=la(e)););return e}(e,t);if(r){var o=Object.getOwnPropertyDescriptor(r,t);return o.get?o.get.call(arguments.length<3?e:n):o.value}},ia.apply(this,arguments)}function aa(e,t){return aa=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},aa(e,t)}function la(e){return la=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},la(e)}var sa=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&aa(e,t)}(l,e);var t,n,r,o,a=(r=l,o=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=la(r);if(o){var n=la(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,e)});function l(e,t){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),a.call(this,function(e){return[ta(ta({},e===_.J2.And?bi:Ai),{},{if:Gi,terminal:!1})].concat(na([ki,_i,xi,Oi,Si,ji,Pi,Di].map((function(e){return ta(ta({},e),{},{if:Ji,terminal:!1})}))),na([Ni,Ri,zi,Li,qi,Ui,Wi,Vi,Hi].map((function(e){return ta(ta({},e),{},{if:Xi,terminal:!0})}))),na([ci,pi,fi].map((function(e){return ta(ta({},e),{},{if:Qi,terminal:Zi})}))))}(t),e)}return t=l,n=[{key:"isValid",get:function(){return ia(la(l.prototype),"isValid",this)&&this.respectsBasicSyntax()}},{key:"statements",get:function(){if(this.syntaxerResult.tree){for(var e=[],t=[this.syntaxerResult.tree];t.length;){var n=t.pop();n&&(e.push(n),n.left&&t.push(n.left),n.block&&t.push(n.block),n.right&&t.push(n.right))}return e}}},{key:"respectsBasicSyntax",value:function(){var e=i.UID((function(e){return e.value}),i.hXT((function(e){return e.lexeme.type===Zo.Expression&&"field"===e.lexeme.subType}),this.lexerResult.lexemes)),t=i.jj$(e);return e.length===t.length}}],n&&oa(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),l}(ii),ca={nesting:-1,regexp:/^\)/,type:Zo.BlockClose},ua={evaluate:function(e,t){l.ZP.trace("evaluate -> ()",e,t);var n=t;return n.block.lexeme.evaluate(e,n.block)},type:Zo.BlockOpen,nesting:1,subType:"()",priority:1,regexp:/^\(/,syntaxer:function(e){return Object.assign({block:e.slice(1,e.length-1)},e[0])}};function pa(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function da(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?pa(Object(n),!0).forEach((function(t){var r,o,i;r=e,o=t,i=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):pa(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function fa(e){return function(e){if(Array.isArray(e))return ha(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return ha(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ha(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ha(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var va=[].concat(fa([bi,Ai].map((function(e){return da(da({},e),{},{if:Gi,terminal:!1})}))),[da(da({},ca),{},{if:function(e,t){return!!t&&i.q9t(t.lexeme.type,[Zo.BlockClose,Zo.BlockOpen,Zo.Expression,Zo.UnaryOperator])&&Yi(0,e)>0},terminal:Ki}),da(da({},ua),{},{if:function(e,t){return!t||i.q9t(t.lexeme.type,[Zo.BlockOpen,Zo.LogicalOperator,Zo.UnaryOperator])},terminal:!1})],fa([ki,_i,xi,Oi,Si,ji,Pi,Di].map((function(e){return da(da({},e),{},{if:Ji,terminal:!1})}))),fa([Ni,Ri,zi,Li,qi,Ui,Wi,Vi,Hi].map((function(e){return da(da({},e),{},{if:Xi,terminal:Ki})}))),[da(da({},Mi),{},{if:function(e,t){return!t||i.q9t(t.lexeme.type,[Zo.LogicalOperator,Zo.UnaryOperator])},terminal:!1})],fa([ci,pi,fi].map((function(e){return da(da({},e),{},{if:Qi,terminal:Zi})}))));function ba(e,t){return ba=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},ba(e,t)}function Aa(e){return Aa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Aa(e)}var ya=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ba(e,t)}(i,e);var t,n,r,o=(n=i,r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Aa(n);if(r){var o=Aa(this).constructor;e=Reflect.construct(t,arguments,o)}else e=t.apply(this,arguments);return function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,e)});function i(e){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),o.call(this,va,e)}return t=i,Object.defineProperty(t,"prototype",{writable:!1}),t}(ii);function ga(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ma(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ga(Object(n),!0).forEach((function(t){var r,o,i;r=e,o=t,i=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ga(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function wa(e){return function(e){if(Array.isArray(e))return Ea(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return Ea(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Ea(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Ea(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var Ca=[].concat(wa([ki,_i,xi,Oi,Si,ji,Pi,Di].map((function(e){return ma(ma({},e),{},{if:$i,terminal:!1})}))),wa([Ni,Ri,zi,Li,qi,Ui,Wi,Vi,Hi].map((function(e){return ma(ma({},e),{},{if:$i,terminal:!0})}))),wa([ci,hi,pi].map((function(e){return ma(ma({},e),{},{if:Qi,terminal:!0})}))));function ka(e,t){return ka=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},ka(e,t)}function xa(e){return xa=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},xa(e)}function Oa(e){return function(e){if(Array.isArray(e))return Sa(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return Sa(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Sa(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Sa(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Ba(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Ba(Object(n),!0).forEach((function(t){var r,o,i;r=e,o=t,i=n[t],(o=ja(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ba(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function ja(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var Pa=[vi.Contains,vi.Equal,vi.GreaterOrEqual,vi.GreaterThan,vi.LessOrEqual,vi.LessThan,vi.NotEqual];function Da(e,t){var n=i.kKJ(e)?"":e.case===_.oN.Insensitive?"i":"s";return t.lexeme.type===Zo.RelationalOperator&&t.lexeme.subType&&-1!==Pa.indexOf(t.lexeme.subType)&&t.value&&-1===["i","s"].indexOf(t.value[0])?_a(_a({},t),{},{value:"".concat(n).concat(t.value)}):t}function Ta(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:_.QD.Any,n=i.kKJ(e)?"":e.case===_.oN.Insensitive?"i":"s";switch(t){case _.QD.Any:case _.QD.Text:return{lexeme:Jo(ki),value:"".concat(n).concat(vi.Contains)};case _.QD.Datetime:return{lexeme:Jo(_i),value:vi.DateStartsWith};case _.QD.Numeric:return{lexeme:Jo(xi),value:"".concat(n).concat(vi.Equal)}}}function Ia(e,t){return t.valid?(2===t.lexemes.length?t.lexemes=[{lexeme:Jo(ci),value:"{".concat(e.id,"}")},Da(e.filter_options,t.lexemes[0]),t.lexemes[1]]:1!==(n=t.lexemes).length||n[0].lexeme.type!==Zo.UnaryOperator?function(e){return 1===e.length&&e[0].lexeme.type===Zo.Expression}(t.lexemes)&&(t.lexemes=[{lexeme:Jo(ci),value:"{".concat(e.id,"}")},Ta(e.filter_options,e.type)].concat(Oa(t.lexemes))):t.lexemes=[{lexeme:Jo(ci),value:"{".concat(e.id,"}")}].concat(Oa(t.lexemes)),t):t;var n}var Fa=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&ka(e,t)}(i,e);var t,n,r,o=(n=i,r=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=xa(n);if(r){var o=xa(this).constructor;e=Reflect.construct(t,arguments,o)}else e=t.apply(this,arguments);return function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,e)});function i(e,t){return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,i),o.call(this,Ca,e,Ia.bind(void 0,t))}return t=i,Object.defineProperty(t,"prototype",{writable:!1}),t}(ii);function Ma(e,t){return!e||void 0===e.column_id||(Array.isArray(e.column_id)?i.q9t(t,e.column_id):e.column_id===t)}function Ra(e,t){if(!e||void 0===e.row_index)return!0;var n=e.row_index;return"string"==typeof n?t%2==("odd"===n?1:0):Array.isArray(n)?i.q9t(t,n):t===n}var za=function(e,t,n,r,o){return i.hXT((function(i){return!i.checksHeaderRow()&&i.matchesActive(r)&&i.matchesSelected(o)&&i.matchesDataRow(t)&&i.matchesColumn(n)&&i.matchesFilter(e)}))},Na=function(e){return i.hXT((function(t){return!t.checksState()&&!t.checksDataRow()&&!t.checksHeaderRow()&&t.matchesColumn(e)}))},La=function(e,t){return i.hXT((function(n){return!n.checksState()&&!n.checksDataRow()&&n.matchesHeaderRow(e)&&n.matchesColumn(t)}))},qa=function(e,t){return i.hXT((function(n){return!n.checksState()&&!n.checksColumn()&&!n.checksHeaderRow()&&n.matchesDataRow(t)&&n.matchesFilter(e)}))},Ua=i.hXT((function(e){return!(e.checksState()||e.checksDataRow()||e.checksHeaderRow()||e.checksColumn())})),Wa=function(e){return i.hXT((function(t){return!t.checksDataRow()&&!t.checksState()&&!t.checksColumn()&&t.matchesHeaderRow(e)}))};function Va(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function Ha(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Za(r.key),r)}}function Ya(e,t,n){return t&&Ha(e.prototype,t),n&&Ha(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function Ka(e,t,n){return(t=Za(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Za(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var Qa=["borderBottom","borderLeft","borderRight","borderTop"],$a=i.hXT((function(e){return 0===e.indexOf("border")}),vo),Ga=function(){function e(t,n,r){var o=this;if(Va(this,e),Ka(this,"weights",void 0),Ka(this,"edges",void 0),Ka(this,"rows",void 0),Ka(this,"columns",void 0),Ka(this,"defaultEdge",void 0),Ka(this,"getEdge",(function(e,t){return o.edges[e][t]})),Ka(this,"getEdges",(function(){return o.edges})),Ka(this,"getWeight",(function(e,t){return o.weights[e][t]})),Ka(this,"isDefault",(function(e,t){return o.weights[e][t]===-1/0})),Ka(this,"clone",(function(){return new e(o)})),"number"==typeof t&&void 0!==n){var a=t;this.rows=a,this.columns=n,this.defaultEdge=r,this.weights=i.UID((function(){return new Array(n).fill(-1/0)}),i.w6H(0,a)),this.edges=i.UID((function(){return new Array(n).fill(r)}),i.w6H(0,a))}else{var l=t;this.rows=l.rows,this.columns=l.columns,this.defaultEdge=l.defaultEdge,this.weights=Ve(l.weights),this.edges=Ve(l.edges)}}return Ya(e,[{key:"setEdge",value:function(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]&&arguments[4];e<0||t<0||e>=this.rows||t>=this.columns||!o&&(i.kKJ(n)||r<=this.weights[e][t])||(this.weights[e][t]=r,this.edges[e][t]=n)}}]),e}(),Ja=function(){function e(t,n,r,o,a){var l=this;if(Va(this,e),Ka(this,"horizontal",void 0),Ka(this,"vertical",void 0),Ka(this,"horizontalEdges",void 0),Ka(this,"verticalEdges",void 0),Ka(this,"rows",void 0),Ka(this,"columns",void 0),Ka(this,"defaultEdge",void 0),Ka(this,"getEdges",(function(){return{horizontal:l.horizontal.getEdges(),vertical:l.vertical.getEdges()}})),Ka(this,"getMatrices",(function(){return{horizontal:l.horizontal,vertical:l.vertical}})),Ka(this,"getStyle",(function(e,t){return{borderBottom:l.horizontal.getEdge(e+1,t)||null,borderTop:l.horizontal.getEdge(e,t)||null,borderLeft:l.vertical.getEdge(e,t)||null,borderRight:l.vertical.getEdge(e,t+1)||null}})),Ka(this,"clone",(function(){return new e(l)})),"number"==typeof t&&void 0!==n){var s=t;this.rows=s,this.columns=n,this.defaultEdge=r,this.horizontalEdges=i.kKJ(o)||o,this.verticalEdges=i.kKJ(a)||a,this.horizontal=new Ga(s+1,n,this.horizontalEdges?r:void 0),this.vertical=new Ga(s,n+1,this.verticalEdges?r:void 0)}else{var c=t;this.rows=c.rows,this.columns=c.columns,this.defaultEdge=c.defaultEdge,this.horizontal=c.horizontal.clone(),this.vertical=c.vertical.clone(),this.horizontalEdges=c.horizontalEdges,this.verticalEdges=c.verticalEdges}}return Ya(e,[{key:"setEdges",value:function(e,t,n){this.horizontalEdges&&(n.borderTop&&this.horizontal.setEdge(e,t,n.borderTop[0],n.borderTop[1]),n.borderBottom&&this.horizontal.setEdge(e+1,t,n.borderBottom[0],n.borderBottom[1])),this.verticalEdges&&(n.borderLeft&&this.vertical.setEdge(e,t,n.borderLeft[0],n.borderLeft[1]),n.borderRight&&this.vertical.setEdge(e,t+1,n.borderRight[0],n.borderRight[1]))}}]),e}();function Xa(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function el(e){var t;return{checksColumn:function(){return!(i.kKJ(e.if)||i.kKJ(e.if.column_id)&&i.kKJ(e.if.column_type)&&i.kKJ(e.if.column_editable))},checksFilter:function(){return!i.kKJ(e.if)&&!i.kKJ(e.if.filter_query)},checksDataRow:function(){return!i.kKJ(e.if)&&!i.kKJ(e.if.row_index)},checksHeaderRow:function(){return!i.kKJ(e.if)&&!i.kKJ(e.if.header_index)},checksState:function(){var t;return!i.kKJ(null===(t=e.if)||void 0===t?void 0:t.state)},checksStateActive:function(){var t;return"active"===(null===(t=e.if)||void 0===t?void 0:t.state)},checksStateSelected:function(){var t;return"selected"===(null===(t=e.if)||void 0===t?void 0:t.state)},matchesActive:function(t){return function(e,t){return"active"!==(null==e?void 0:e.state)||t}(e.if,t)},matchesColumn:function(t){return!e.if||!i.kKJ(t)&&Ma(e.if,t&&t.id)&&(n=e.if,r=t&&t.type,!n||void 0===n.column_type||n.column_type===(r||_.QD.Any))&&function(e,t){return!e||void 0===e.column_editable||t===e.column_editable}(e.if,t&&t.editable);var n,r},matchesFilter:function(n){return!e.if||void 0===e.if.filter_query||(t=t||new ya(e.if.filter_query)).evaluate(n)},matchesDataRow:function(t){return Ra(e.if,t)},matchesHeaderRow:function(t){return function(e,t){if(!e||void 0===e.header_index)return!0;var n=e.header_index;return"string"==typeof n?t%2==("odd"===n?1:0):Array.isArray(n)?i.q9t(t,n):t===n}(e.if,t)},matchesSelected:function(t){return function(e,t){return"selected"!==(null==e?void 0:e.state)||t}(e.if,t)},style:tl(e)}}function tl(e){return i.u4g((function(e,t){var n=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],s=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return Xa(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Xa(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(t,2),r=n[0],o=n[1];return ho.has(r)&&(e[ho.get(r)]=o),e}),{},i.Zpf(e))}var nl=(0,a.Pi)((function(e,t,n,r){return i.UWY([e?[el(e)]:[],i.UID(el,n||[]),t?[el(t)]:[],i.UID(el,r||[])])})),rl=(0,a.Pi)((function(e,t,n,r){return i.UWY([e?[el(e)]:[],i.UID(el,n||[]),t?[el(t)]:[],i.UID(el,r||[])])})),ol=(0,a.Pi)((function(e,t,n,r){return i.UWY([e?[el(e)]:[],i.UID(el,n||[]),t?[el(t)]:[],i.UID(el,r||[])])})),il=(0,a.Pi)((function(e,t){return[tl(e),tl(t)]}));function al(e){for(var t={},n=0;n<e.length;++n)Object.assign(t,e[n].style);return i.CEd($a,t)}var ll=function(e,t,n,r,o){return function(i){return al(za(e,t,n,r,o)(i))}},sl=n(3419);function cl(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function ul(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?cl(Object(n),!0).forEach((function(t){var r,o,i;r=e,o=t,i=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):cl(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var pl={backgroundColor:sl.Z.supportsCssVariables?"var(--selected-background)":"rgba(255, 65, 54, 0.2)"},dl=(0,a.Pi)((function(e,t,n,r){return Ye(n,e,(function(e,n,o){return ll(e,o+r.rows,n,!1,!1)(t)}))})),fl=(0,a.Pi)((function(e,t,n,r,o,i,a){e=Ve(e);var l=a.length?a:i?[i]:[],s=n.filter((function(e){return!e.checksState()})),c=n.filter((function(e){return e.checksStateSelected()})),u=n.filter((function(e){return e.checksStateActive()}));return l.forEach((function(n){var a=n.row,l=n.column,p=a-o.rows,d=l-o.columns;if(!(p<0||d<0||e.length<=p||e[p].length<=d)){var f=it(i,a,l),h=ul(ul(ul(ul({},ll(r[a],a+o.rows,t[l],f,!0)(s)),pl),ll(r[a],a+o.rows,t[l],f,!0)(c)),ll(r[a],a+o.rows,t[l],f,!0)(u));e[p][d]=h}})),e})),hl=(0,a.Pi)((function(e,t,n,r){return Ye(n,i.w6H(0,e),(function(e,n,o){return function(e,t){return function(n){return al(qa(e,t)(n))}}(e,o+r.rows)(t)}))}));function vl(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function bl(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,gl(r.key),r)}}function Al(e,t,n){return t&&bl(e.prototype,t),n&&bl(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function yl(e,t,n){return(t=gl(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function gl(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var ml=i.h0F(i.UID),wl=Al((function e(){var t=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),yl(this,"get",(0,a.qe)((function(e,n,r,o,a,l){return ml((function(n,s){return i.UID((function(e){var i=r[s],c=l&&l.length>i&&l[i]&&l[i][e.id]||a[e.id];return t.dropdown.get(e.id,s)(c,o,e,n)}),e)}),n)}))),yl(this,"dropdown",$e()((function(e,n,r,o){var a=i.dFj((function(e){var n=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],s=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return vl(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?vl(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(e,2),a=n[0],l=n[1];return Ma(a.if,r.id)&&(i.kKJ(a.if)||i.kKJ(a.if.filter_query)||t.evaluation.get(r.id,l)(t.ast.get(r.id,l)(a.if.filter_query),o))}),i.h0F(i.UID)((function(e,t){return[e,t]}),n));return a&&a[0]||e||void 0}))),yl(this,"ast",$e()((function(e){return new ya(e)}))),yl(this,"evaluation",$e()((function(e,t){return e.evaluate(t)})))}));function El(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,kl(r.key),r)}}function Cl(e,t,n){return(t=kl(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function kl(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var xl=function(){function e(t){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(e){return new Wo(e)}(t),l=arguments.length>2&&void 0!==arguments[2]?arguments[2]:(new wl).get,s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:Qo(),c=arguments.length>4&&void 0!==arguments[4]?arguments[4]:dl(),u=arguments.length>5&&void 0!==arguments[5]?arguments[5]:fl(),p=arguments.length>6&&void 0!==arguments[6]?arguments[6]:hl(),d=arguments.length>7&&void 0!==arguments[7]?arguments[7]:function(e){return new xt(e)}(t),f=arguments.length>8&&void 0!==arguments[8]?arguments[8]:nl();!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Cl(this,"propsFn",void 0),Cl(this,"cellContents",void 0),Cl(this,"cellDropdowns",void 0),Cl(this,"cellOperations",void 0),Cl(this,"dataPartialStyles",void 0),Cl(this,"dataStyles",void 0),Cl(this,"dataOpStyles",void 0),Cl(this,"cellWrappers",void 0),Cl(this,"relevantStyles",void 0),Cl(this,"getMarkdown",(0,a.qe)((function(e){return new Oo(e)}))),Cl(this,"getCells",(0,a.qe)((function(e,t){return Q(e,t,(function(e,t){return e.length?e.concat(t):t}))}))),Cl(this,"getDataOpCell",$e()((function(e,t,n,r,a,l){return o().cloneElement(e,{style:i.Jnq([{borderBottom:n,borderLeft:r,borderRight:a,borderTop:l},t,e.props.style])})}))),Cl(this,"getDataOpCells",(0,a.qe)((function(e,t,r){return Ke(e,t,(function(e,t,o,i){var a=r&&r.getStyle(o,i);return n.getDataOpCell.get(o,i)(e,t,a&&a.borderBottom,a&&a.borderLeft,a&&a.borderRight,a&&a.borderTop)}))}))),Cl(this,"getDataCell",$e()((function(e,t,n,r,a,l,s){return o().cloneElement(e,{children:[t],style:i.ATH(n||{},{borderBottom:r,borderLeft:a,borderRight:l,borderTop:s})})}))),Cl(this,"getDataCells",(0,a.qe)((function(e,t,r,o){return Ze(e,r,t,(function(e,t,r,i,a){var l=o&&o.getStyle(i,a);return n.getDataCell.get(i,a)(e,r,t,l&&l.borderBottom,l&&l.borderLeft,l&&l.borderRight,l&&l.borderTop)}))}))),this.propsFn=t,this.cellContents=r,this.cellDropdowns=l,this.cellOperations=s,this.dataPartialStyles=c,this.dataStyles=u,this.dataOpStyles=p,this.cellWrappers=d,this.relevantStyles=f}var t,n;return t=e,n=[{key:"props",get:function(){return this.propsFn()}},{key:"createCells",value:function(e,t){var n=this.props,r=n.active_cell,o=n.applyFocus,i=n.dropdown_conditional,a=n.dropdown,l=n.data,s=n.dropdown_data,c=n.id,u=n.is_focused,p=n.loading_state,d=n.markdown_options,f=n.row_deletable,h=n.row_selectable,v=n.selected_cells,b=n.selected_rows,A=n.setProps,y=n.style_cell,g=n.style_cell_conditional,m=n.style_data,w=n.style_data_conditional,E=n.virtualized,C=n.visibleColumns,k=this.relevantStyles(y,m,g,w),x=this.dataPartialStyles(C,k,E.data,E.offset),O=this.dataStyles(x,C,k,E.data,E.offset,r,v),S=this.dataOpStyles((h?1:0)+(f?1:0),k,E.data,E.offset),B=this.cellDropdowns(C,E.data,E.indices,i,a,s),_=this.cellOperations(c,l,E.data,E.indices,h,f,b,A),j=this.cellWrappers.partialGet(C,E.data,E.offset),P=this.cellWrappers.get(j,E.offset,r,v),D=this.getMarkdown(d),T=this.cellContents.partialGet(C,E.data,E.offset,!!u,B,p,D),I=this.cellContents.get(T,r,o||!1,C,E.data,E.offset,!!u,B,p,D),F=this.getDataOpCells(_,S,t),M=this.getDataCells(P,I,O,e);return this.getCells(F,M)}}],n&&El(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Ol(e,t){var n={};return He(e,Qa,(function(e,r,o){var i=e.style[r]||e.style.border;i&&(n[r]=[i,null!=t?t:o])})),n}var Sl=function(e,t,n,r,o,i){return function(a){return Ol(za(e,t,n,r,o)(a),i)}};function Bl(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function _l(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Bl(Object(n),!0).forEach((function(t){var r,o,i;r=e,o=t,i=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Bl(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var jl=Number.MAX_SAFE_INTEGER,Pl=Number.MAX_SAFE_INTEGER-1,Dl=(0,a.Pi)((function(e,t,n,r,o){if(0!==n.length&&0!==e.length){var i=new Ja(n.length,e.length,sl.Z.defaultEdge,!0,!o);return He(n,e,(function(e,n,o,a){return i.setEdges(o,a,Sl(e,o+r.rows,n,!1,!1)(t))})),i}})),Tl=(0,a.Pi)((function(e,t,n,r,o,i,a){if(!e)return e;var l=e.clone(),s=a.length?a:i?[i]:[],c=n.filter((function(e){return!e.checksState()})),u=n.filter((function(e){return e.checksStateSelected()})),p=n.filter((function(e){return e.checksStateActive()}));return s.forEach((function(e){var n=e.row,a=e.column,s=n-o.rows,d=a-o.columns;if(!(s<0||d<0||r.length<=s)){var f=it(i,n,a),h=f?jl:Pl,v=f?sl.Z.activeEdge:sl.Z.defaultEdge,b=_l(_l(_l({},Sl(r[s],s,t[a],f,!0,h)(c)),{},{borderBottom:[v,h],borderLeft:[v,h],borderRight:[v,h],borderTop:[v,h]},Sl(r[s],s,t[a],f,!0,h)(u)),Sl(r[s],s,t[a],f,!0,h)(p));l.setEdges(s,a,b)}})),l})),Il=(0,a.Pi)((function(e,t,n,r,o){if(0!==n.length&&0!==e){var a=new Ja(n.length,e,sl.Z.defaultEdge,!0,!o);return He(n,i.w6H(0,e),(function(e,n,o,i){return a.setEdges(o,i,function(e,t){return function(n){return Ol(qa(e,t)(n))}}(e,o+r.rows)(t))})),a}})),Fl=(0,a.Pi)((function(e,t,n,r,o){if(t&&0!==e.length){var a=new Ja(1,e.length,sl.Z.defaultEdge,!0,!o);return He(i.w6H(0,1),e,(function(e,t,o,i){a.setEdges(o,i,function(e){return function(t){return Ol(Na(e)(t))}}(t)(r));var l=n.get(t.id.toString());l&&!l.isValid&&a.setEdges(o,i,{borderBottom:[sl.Z.activeEdge,1/0],borderLeft:[sl.Z.activeEdge,1/0],borderRight:[sl.Z.activeEdge,1/0],borderTop:[sl.Z.activeEdge,1/0]})})),a}})),Ml=(0,a.Pi)((function(e,t,n,r){if(t&&0!==e){var o=new Ja(1,e,sl.Z.defaultEdge,!0,!r);return He(i.w6H(0,1),i.w6H(0,e),(function(e,t){return o.setEdges(e,t,function(e){return Ol(Ua(e))}(n))})),o}})),Rl=(0,a.Pi)((function(e,t,n,r){if(0!==t&&0!==e.length){var o=new Ja(t,e.length,sl.Z.defaultEdge,!0,!r);return He(i.w6H(0,t),e,(function(e,t,r,i){return o.setEdges(r,i,function(e,t){return function(n){return Ol(La(e,t)(n))}}(r,t)(n))})),o}})),zl=(0,a.Pi)((function(e,t,n,r){if(0!==t&&0!==e){var o=new Ja(t,e,sl.Z.defaultEdge,!0,!r);return He(i.w6H(0,t),i.w6H(0,e),(function(e,t){return o.setEdges(e,t,function(e){return function(t){return Ol(Wa(e)(t))}}(e)(n))})),o}}));function Nl(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,ql(r.key),r)}}function Ll(e,t,n){return(t=ql(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ql(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var Ul=function(){function e(t){var n=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Ll(this,"propsFn",void 0),Ll(this,"dataStyles",nl()),Ll(this,"filterStyles",rl()),Ll(this,"headerStyles",ol()),Ll(this,"getPartialDataEdges",Dl()),Ll(this,"getDataEdges",Tl()),Ll(this,"getDataOpEdges",Il()),Ll(this,"getFilterEdges",Fl()),Ll(this,"getFilterOpEdges",Ml()),Ll(this,"getHeaderEdges",Rl()),Ll(this,"getHeaderOpEdges",zl()),Ll(this,"memoizedCreateEdges",(0,a.qe)((function(t,r,o,i,a,l,s,c,u,p,d,f,h,v,b,A,y,g,m,E){var C=n.dataStyles(d,h,f,v),k=n.filterStyles(d,b,f,A),x=n.headerStyles(d,y,f,g),O=w(r),S=n.getPartialDataEdges(o,C,m,E,p),B=n.getDataEdges(S,o,C,m,E,t,u),_=n.getDataOpEdges(i,C,m,E,p),j=n.getFilterEdges(o,a,l,k,p),P=n.getFilterOpEdges(i,a,k,p),D=n.getHeaderEdges(o,O,x,p),T=n.getHeaderOpEdges(i,O,x,p),I=(d?1:0)+f.length-1;return D=e.clone(D),T=e.clone(T),j=e.clone(j),P=e.clone(P),B=e.clone(B),_=e.clone(_),n.hReconcile(D,j||B,I),n.hReconcile(T,P||_,I),n.hReconcile(j,B,I),n.hReconcile(P,_,I),n.vReconcile(T,D,I),n.vReconcile(P,j,I),n.vReconcile(_,B,I),c===O?a?(n.hOverride(D,j,I),n.hOverride(T,P,I)):(n.hOverride(D,B,I),n.hOverride(T,_,I)):a&&c===O+1&&(n.hOverride(j,B,I),n.hOverride(P,_,I)),s===i&&(n.vOverride(T,D,I),n.vOverride(P,j,I),n.vOverride(_,B,I)),{dataEdges:B,dataOpEdges:_,filterEdges:j,filterOpEdges:P,headerEdges:D,headerOpEdges:T}}))),this.propsFn=t}var t,n,r;return t=e,n=[{key:"hOverride",value:function(t,n,r){if(t&&n)for(var o=t.getMatrices().horizontal,i=n.getMatrices().horizontal,a=o.rows-1,l=0;l<o.columns;l++)e.hasPrecedence(o.getWeight(a,l),i.getWeight(0,l),r)&&i.setEdge(0,l,o.getEdge(a,l),1/0,!0),o.setEdge(a,l,"none",-1/0,!0)}},{key:"vOverride",value:function(t,n,r){if(t&&n)for(var o=t.getMatrices().vertical,i=n.getMatrices().vertical,a=o.columns-1,l=0;l<o.rows;l++)e.hasPrecedence(o.getWeight(l,a),i.getWeight(l,0),r)&&i.setEdge(l,0,o.getEdge(l,a),1/0,!0),o.setEdge(l,a,"none",-1/0,!0)}},{key:"hReconcile",value:function(t,n,r){if(t&&n){var o=n.getMatrices().horizontal,i=t.getMatrices().horizontal,a=i.rows-1;if(isFinite(a))for(var l=0;l<i.columns;l++)e.hasPrecedence(i.getWeight(a,l),o.getWeight(0,l),r)||i.setEdge(a,l,"none",-1/0,!0)}}},{key:"vReconcile",value:function(t,n,r){if(t&&n)for(var o=n.getMatrices().vertical,i=t.getMatrices().vertical,a=i.columns-1,l=0;l<i.rows;l++)e.hasPrecedence(i.getWeight(l,a),o.getWeight(l,0),r)||i.setEdge(l,a,"none",-1/0,!0)}},{key:"props",get:function(){return this.propsFn()}},{key:"createEdges",value:function(){var e=this.props,t=e.active_cell,n=e.columns,r=e.filter_action,o=e.workFilter,i=e.fixed_columns,a=e.fixed_rows,l=e.row_deletable,s=e.row_selectable,c=e.selected_cells,u=e.style_as_list_view,p=e.style_cell,d=e.style_cell_conditional,f=e.style_data,h=e.style_data_conditional,v=e.style_filter,b=e.style_filter_conditional,A=e.style_header,y=e.style_header_conditional,g=e.virtualized,m=e.visibleColumns;return this.memoizedCreateEdges(t,n,m,(l?1:0)+(s?1:0),r.type!==_.p9.None,o.map,i,a,c,u,p,d,f,h,v,b,A,y,g.data,g.offset)}}],r=[{key:"clone",value:function(e){return e&&e.clone()}},{key:"hasPrecedence",value:function(e,t,n){return(t<=n||e===1/0)&&t<=e}}],n&&Nl(t.prototype,n),r&&Nl(t,r),Object.defineProperty(t,"prototype",{writable:!1}),e}();function Wl(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Vl(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Ql(r.key),r)}}function Hl(e,t){return Hl=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Hl(e,t)}function Yl(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Kl(e){return Kl=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Kl(e)}function Zl(e,t,n){return(t=Ql(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ql(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var $l=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Hl(e,t)}(l,e);var t,n,r,i,a=(r=l,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Kl(r);if(i){var n=Kl(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Yl(e)}(this,e)});function l(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),Zl(Yl(t=a.call(this,e)),"handleKeyDown",(function(e){var n=t.propsWithDefaults,r=n.stopPropagation,o=n.updateOnEnter;r&&e.stopPropagation(),o&&e.keyCode===v.ENTER&&t.submit()})),Zl(Yl(t),"handleChange",(function(e){t.setState({value:e.target.value})})),Zl(Yl(t),"submit",(function(){return t.state.value!==t.props.value&&t.props.submit(t.state.value)})),t.state={value:e.value},t}return t=l,n=[{key:"propsWithDefaults",get:function(){return this.props}},{key:"UNSAFE_componentWillReceiveProps",value:function(e){var t=this.props.value,n=e.value;t!==n&&this.setState({value:n})}},{key:"render",value:function(){var e=this.propsWithDefaults,t=e.onCopy,n=e.onPaste,r=e.placeholder,i=e.updateOnBlur,a=e.updateOnSubmit,l={onBlur:i?this.submit:void 0,onKeyDown:this.handleKeyDown,onSubmit:a?this.submit:void 0};return o().createElement("input",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Wl(Object(n),!0).forEach((function(t){Zl(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Wl(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({ref:"input",type:"text",value:this.state.value||"",onChange:this.handleChange,onCopy:t,onPaste:n,placeholder:r},l))}}],n&&Vl(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),l}(r.PureComponent);Zl($l,"defaultProps",{stopPropagation:!1,updateOnEnter:!0,updateOnBlur:!0,updateOnSubmit:!0});var Gl=$l,Jl=function(e){var t=e.filterOptions,n=e.toggleFilterOptions;return o().createElement("input",{type:"button",className:"dash-filter--case ".concat(t.case===_.oN.Sensitive?"dash-filter--case--sensitive":"dash-filter--case--insensitive"),onClick:n,title:"Toggle filter case sensitivity",value:"Aa"})};function Xl(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,rs(r.key),r)}}function es(e,t){return es=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},es(e,t)}function ts(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ns(e){return ns=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ns(e)}function rs(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var os=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&es(e,t)}(l,e);var t,n,r,i,a=(r=l,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=ns(r);if(i){var n=ns(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ts(e)}(this,e)});function l(e){var t,n,r,o;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),n=ts(t=a.call(this,e)),o=function(e){(0,t.props.setFilter)({target:{value:e}})},(r=rs(r="submit"))in n?Object.defineProperty(n,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[r]=o,t.state={value:e.value},t}return t=l,n=[{key:"render",value:function(){var e=this.props,t=e.className,n=e.columnId,r=e.filterOptions,i=e.isValid,a=e.style,l=e.toggleFilterOptions,s=e.value;return o().createElement("th",{className:t+(i?"":" invalid"),"data-dash-column":n,style:a},o().createElement("div",null,o().createElement(Gl,{onCopy:function(e){e.stopPropagation(),Le.clearClipboard()},onPaste:function(e){e.stopPropagation()},value:s,placeholder:r.placeholder_text,stopPropagation:!0,submit:this.submit}),o().createElement(Jl,{filterOptions:r,toggleFilterOptions:l})))}}],n&&Xl(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),l}(r.PureComponent),is=(0,a.Pi)((function(e,t){return i.UID((function(e){return function(e){return function(t){return al(Na(e)(t))}}(e)(t)}),e)})),as=(0,a.Pi)((function(e,t,n){return Ye(i.w6H(0,e),i.w6H(0,t),(function(){return function(e){return al(Ua(e))}(n)}))}));function ls(e){return function(e){if(Array.isArray(e))return ss(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return ss(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?ss(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function ss(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var cs=(0,a.Pi)((function(e,t,n){return i.h0F(i.UID)((function(){return[].concat(ls(n?[o().createElement("th",{key:"delete",className:"expanded-row--empty-cell dash-delete-header",style:{width:"30px",maxWidth:"30px",minWidth:"30px"}})]:[]),ls(t?[o().createElement("th",{key:"select",className:"expanded-row--empty-cell dash-select-header",style:{width:"30px",maxWidth:"30px",minWidth:"30px"}})]:[]))}),i.w6H(0,e))})),us=function(e,t){return e===t?new Map(t):e},ps=(0,a.Pi)((function(e,t,n,r){var o=function(e,t){if(e.isValid){var n=new Map,r=e.statements;return r?(r.forEach((function(e){if(e.lexeme.type===Zo.UnaryOperator&&e.left){var r=e.left.lexeme.present?e.left.lexeme.present(e.left):e.left.value,o=i.sEJ((function(e){return e.id.toString()===r}),t);if(!o)throw new Error("column ".concat(r," not found"));n.set(r,new Fa(e.value,o))}else if(e.lexeme.type===Zo.RelationalOperator&&e.left&&e.right){var a=e.left.lexeme.present?e.left.lexeme.present(e.left):e.left.value,l=i.sEJ((function(e){return e.id.toString()===a}),t);if(!l)throw new Error("column ".concat(a," not found"));e.lexeme.present&&e.lexeme.present(e)===vi.Equal?n.set(a,new Fa("".concat(e.right.value),l)):n.set(a,new Fa("".concat(e.value," ").concat(e.right.value),l))}})),n):n}}(new sa(n,t),r);if(!o)return e;var a=e;return i.jj$(i.zoF(Array.from(e.keys()),Array.from(o.keys()))).forEach((function(t){var n=e.get(t),r=o.get(t);i.kKJ(r)?(a=us(a,e)).delete(t):(i.kKJ(n)||r.toQueryString()!==n.toQueryString())&&(a=us(a,e)).set(t,r)})),a}));function ds(e,t,n){var r=t.id.toString(),o=new Map(e);return n&&n.length?o.set(r,new Fa(n,t)):o.delete(r),o}function fs(e,t,n){var r=Array.from(e.values()),o=function(e,t){return i.UID((function(e){return e.toQueryString()}),i.hXT((function(e){return Boolean(null==e?void 0:e.query)&&e.isValid}))(e)).join(" ".concat(t===_.J2.And?"&&":"||"," "))}(r,t);n(o,i.UID((function(e){return e.query}),i.hXT((function(e){return Boolean(null==e?void 0:e.query)}))(r)).join(t===_.J2.And?" && ":" || "),e)}var hs=function(e,t,n,r,o){fs(e=ds(e,t,r),n,o)};function vs(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,As(r.key),r)}}function bs(e,t,n){return(t=As(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function As(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var ys,gs=[],ms=function(){function e(t){var n=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),bs(this,"propsFn",void 0),bs(this,"filterStyles",is()),bs(this,"filterOpStyles",as()),bs(this,"relevantStyles",rl()),bs(this,"headerOperations",cs()),bs(this,"onChange",(function(e,t,n,r,o){l.ZP.debug("Filter -- onChange",e.id,o.target.value&&o.target.value.trim());var i=o.target.value.trim();hs(t,e,n,i,r)})),bs(this,"onToggleChange",(function(e,t,n,r,o,i){var a=o(e);hs(t,a,n,i,r)})),bs(this,"filter",$e()((function(e,t,r,i,a,l){var s=r.get(e.id.toString());return o().createElement(os,{key:"column-".concat(t),className:"dash-filter column-".concat(t),columnId:e.id,filterOptions:e.filter_options,isValid:!s||s.isValid,setFilter:n.onChange.bind(n,e,r,i,a),toggleFilterOptions:n.onToggleChange.bind(n,e,r,i,a).bind(n,l,s&&s.query),value:s&&s.query})}))),bs(this,"wrapperStyles",(0,a.qe)((function(e,t){return function(e,n){for(var r=e.length,o=new Array(r),a=0;a<r;++a)o[a]=(l=e[a],s=a,i.ATH(l,t&&t.getStyle(0,s)||{}));var l,s;return o}(e)}))),bs(this,"getCells",(0,a.qe)((function(e,t){return[e.concat(t)]}))),bs(this,"getFilterCells",(0,a.qe)((function(e,t,n){return Q(e,t,(function(e,t,r){return o().cloneElement(e,{style:i.Jnq([n&&n.getStyle(0,r),t,e.props.style])})}))}))),bs(this,"getOpFilterCells",(0,a.qe)((function(e,t,n){return Q(e,t,(function(e,t,r){return o().cloneElement(e,{style:i.Jnq([n&&n.getStyle(0,r),t,e.props.style])})}))}))),this.propsFn=t}var t,n;return t=e,n=[{key:"props",get:function(){return this.propsFn()}},{key:"createFilters",value:function(e,t){var n=this,r=this.props,o=r.filter_action,a=r.map,l=r.row_deletable,s=r.row_selectable,c=r.setFilter,u=r.style_cell,p=r.style_cell_conditional,d=r.style_filter,f=r.style_filter_conditional,h=r.toggleFilterOptions,v=r.visibleColumns;if(o.type===_.p9.None)return gs;var b=this.relevantStyles(u,d,p,f),A=this.wrapperStyles(this.filterStyles(v,b),e),y=this.filterOpStyles(1,(s?1:0)+(l?1:0),b)[0],g=i.h0F(i.UID)((function(e,t){return n.filter.get(e.id,t)(e,t,a,o.operator,c,h)}),v),m=this.getFilterCells(g,A,e),w=this.headerOperations(1,s,l)[0],E=this.getOpFilterCells(w,y,t);return this.getCells(E,m)}}],n&&vs(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();!function(e){e.Ascending="asc",e.Descending="desc",e.None="none"}(ys||(ys={}));var ws=function(e,t){return i.kKJ(e)},Es=function(e,t){if(l.ZP.trace("multi - update sortBy",e,t),e=i.d9v(e),t.direction===ys.None){var n=i.cxD((function(e){return e.column_id===t.column_id}),e);-1!==n&&e.splice(n,1)}else{var r=i.sEJ((function(e){return e.column_id===t.column_id}),e);r?r.direction=t.direction:e.push(t)}return e},Cs=function(e,t){return l.ZP.trace("single - update sortBy",e,t),t.direction===ys.None?[]:[t]};function ks(e){return ks="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ks(e)}function xs(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function Os(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ss(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Os(e,t,n[t])}))}return e}function Bs(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=[],r=!0,o=!1,i=void 0;try{for(var a,l=e[Symbol.iterator]();!(r=(a=l.next()).done)&&(n.push(a.value),!t||n.length!==t);r=!0);}catch(e){o=!0,i=e}finally{try{r||null==l.return||l.return()}finally{if(o)throw i}}return n}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}var _s=function(){},js={},Ps={},Ds={mark:_s,measure:_s};try{"undefined"!=typeof window&&(js=window),"undefined"!=typeof document&&(Ps=document),"undefined"!=typeof MutationObserver&&MutationObserver,"undefined"!=typeof performance&&(Ds=performance)}catch(e){}var Ts=(js.navigator||{}).userAgent,Is=void 0===Ts?"":Ts,Fs=js,Ms=Ps,Rs=Ds,zs=(Fs.document,!!Ms.documentElement&&!!Ms.head&&"function"==typeof Ms.addEventListener&&"function"==typeof Ms.createElement),Ns=(~Is.indexOf("MSIE")||Is.indexOf("Trident/"),"___FONT_AWESOME___"),Ls="fa",qs="svg-inline--fa",Us=(function(){try{return!0}catch(e){return!1}}(),[1,2,3,4,5,6,7,8,9,10]),Ws=Us.concat([11,12,13,14,15,16,17,18,19,20]),Vs={GROUP:"group",SWAP_OPACITY:"swap-opacity",PRIMARY:"primary",SECONDARY:"secondary"},Hs=(["xs","sm","lg","fw","ul","li","border","pull-left","pull-right","spin","pulse","rotate-90","rotate-180","rotate-270","flip-horizontal","flip-vertical","flip-both","stack","stack-1x","stack-2x","inverse","layers","layers-text","layers-counter",Vs.GROUP,Vs.SWAP_OPACITY,Vs.PRIMARY,Vs.SECONDARY].concat(Us.map((function(e){return"".concat(e,"x")}))).concat(Ws.map((function(e){return"w-".concat(e)}))),Fs.FontAwesomeConfig||{});Ms&&"function"==typeof Ms.querySelector&&[["data-family-prefix","familyPrefix"],["data-replacement-class","replacementClass"],["data-auto-replace-svg","autoReplaceSvg"],["data-auto-add-css","autoAddCss"],["data-auto-a11y","autoA11y"],["data-search-pseudo-elements","searchPseudoElements"],["data-observe-mutations","observeMutations"],["data-mutate-approach","mutateApproach"],["data-keep-original-source","keepOriginalSource"],["data-measure-performance","measurePerformance"],["data-show-missing-icons","showMissingIcons"]].forEach((function(e){var t=Bs(e,2),n=t[0],r=t[1],o=function(e){return""===e||"false"!==e&&("true"===e||e)}(function(e){var t=Ms.querySelector("script["+e+"]");if(t)return t.getAttribute(e)}(n));null!=o&&(Hs[r]=o)}));var Ys=Ss({},{familyPrefix:Ls,replacementClass:qs,autoReplaceSvg:!0,autoAddCss:!0,autoA11y:!0,searchPseudoElements:!1,observeMutations:!0,mutateApproach:"async",keepOriginalSource:!0,measurePerformance:!1,showMissingIcons:!0},Hs);Ys.autoReplaceSvg||(Ys.observeMutations=!1);var Ks=Ss({},Ys);Fs.FontAwesomeConfig=Ks;var Zs=Fs||{};Zs[Ns]||(Zs[Ns]={}),Zs[Ns].styles||(Zs[Ns].styles={}),Zs[Ns].hooks||(Zs[Ns].hooks={}),Zs[Ns].shims||(Zs[Ns].shims=[]);var Qs=Zs[Ns],$s=[];zs&&((Ms.documentElement.doScroll?/^loaded|^c/:/^loaded|^i|^c/).test(Ms.readyState)||Ms.addEventListener("DOMContentLoaded",(function e(){Ms.removeEventListener("DOMContentLoaded",e),$s.map((function(e){return e()}))})));var Gs,Js="pending",Xs="settled",ec="fulfilled",tc="rejected",nc=function(){},rc=void 0!==n.g&&void 0!==n.g.process&&"function"==typeof n.g.process.emit,oc="undefined"==typeof setImmediate?setTimeout:setImmediate,ic=[];function ac(){for(var e=0;e<ic.length;e++)ic[e][0](ic[e][1]);ic=[],Gs=!1}function lc(e,t){ic.push([e,t]),Gs||(Gs=!0,oc(ac,0))}function sc(e){var t=e.owner,n=t._state,r=t._data,o=e[n],i=e.then;if("function"==typeof o){n=ec;try{r=o(r)}catch(e){dc(i,e)}}cc(i,r)||(n===ec&&uc(i,r),n===tc&&dc(i,r))}function cc(e,t){var n;try{if(e===t)throw new TypeError("A promises callback cannot return that same promise.");if(t&&("function"==typeof t||"object"===ks(t))){var r=t.then;if("function"==typeof r)return r.call(t,(function(r){n||(n=!0,t===r?pc(e,r):uc(e,r))}),(function(t){n||(n=!0,dc(e,t))})),!0}}catch(t){return n||dc(e,t),!0}return!1}function uc(e,t){e!==t&&cc(e,t)||pc(e,t)}function pc(e,t){e._state===Js&&(e._state=Xs,e._data=t,lc(hc,e))}function dc(e,t){e._state===Js&&(e._state=Xs,e._data=t,lc(vc,e))}function fc(e){e._then=e._then.forEach(sc)}function hc(e){e._state=ec,fc(e)}function vc(e){e._state=tc,fc(e),!e._handled&&rc&&n.g.process.emit("unhandledRejection",e._data,e)}function bc(e){n.g.process.emit("rejectionHandled",e)}function Ac(e){if("function"!=typeof e)throw new TypeError("Promise resolver "+e+" is not a function");if(this instanceof Ac==0)throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.");this._then=[],function(e,t){function n(e){dc(t,e)}try{e((function(e){uc(t,e)}),n)}catch(e){n(e)}}(e,this)}Ac.prototype={constructor:Ac,_state:Js,_then:null,_data:void 0,_handled:!1,then:function(e,t){var n={owner:this,then:new this.constructor(nc),fulfilled:e,rejected:t};return!t&&!e||this._handled||(this._handled=!0,this._state===tc&&rc&&lc(bc,this)),this._state===ec||this._state===tc?lc(sc,n):this._then.push(n),n.then},catch:function(e){return this.then(null,e)}},Ac.all=function(e){if(!Array.isArray(e))throw new TypeError("You must pass an array to Promise.all().");return new Ac((function(t,n){var r=[],o=0;function i(e){return o++,function(n){r[e]=n,--o||t(r)}}for(var a,l=0;l<e.length;l++)(a=e[l])&&"function"==typeof a.then?a.then(i(l),n):r[l]=a;o||t(r)}))},Ac.race=function(e){if(!Array.isArray(e))throw new TypeError("You must pass an array to Promise.race().");return new Ac((function(t,n){for(var r,o=0;o<e.length;o++)(r=e[o])&&"function"==typeof r.then?r.then(t,n):t(r)}))},Ac.resolve=function(e){return e&&"object"===ks(e)&&e.constructor===Ac?e:new Ac((function(t){t(e)}))},Ac.reject=function(e){return new Ac((function(t,n){n(e)}))};var yc={size:16,x:0,y:0,rotate:0,flipX:!1,flipY:!1};var gc="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";function mc(){for(var e=12,t="";e-- >0;)t+=gc[62*Math.random()|0];return t}function wc(e){return"".concat(e).replace(/&/g,"&amp;").replace(/"/g,"&quot;").replace(/'/g,"&#39;").replace(/</g,"&lt;").replace(/>/g,"&gt;")}function Ec(e){return Object.keys(e||{}).reduce((function(t,n){return t+"".concat(n,": ").concat(e[n],";")}),"")}function Cc(e){return e.size!==yc.size||e.x!==yc.x||e.y!==yc.y||e.rotate!==yc.rotate||e.flipX||e.flipY}function kc(e){var t=e.transform,n=e.containerWidth,r=e.iconWidth,o={transform:"translate(".concat(n/2," 256)")},i="translate(".concat(32*t.x,", ").concat(32*t.y,") "),a="scale(".concat(t.size/16*(t.flipX?-1:1),", ").concat(t.size/16*(t.flipY?-1:1),") "),l="rotate(".concat(t.rotate," 0 0)");return{outer:o,inner:{transform:"".concat(i," ").concat(a," ").concat(l)},path:{transform:"translate(".concat(r/2*-1," -256)")}}}var xc={x:0,y:0,width:"100%",height:"100%"};function Oc(e){var t=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];return e.attributes&&(e.attributes.fill||t)&&(e.attributes.fill="black"),e}var Sc=(Ks.measurePerformance&&Rs&&Rs.mark&&Rs.measure,function(e,t,n,r){var o,i,a,l=Object.keys(e),s=l.length,c=void 0!==r?function(e,t){return function(n,r,o,i){return e.call(t,n,r,o,i)}}(t,r):t;for(void 0===n?(o=1,a=e[l[0]]):(o=0,a=n);o<s;o++)a=c(a,e[i=l[o]],i,e);return a});function Bc(e,t){var n=(arguments.length>2&&void 0!==arguments[2]?arguments[2]:{}).skipHooks,r=void 0!==n&&n,o=Object.keys(t).reduce((function(e,n){var r=t[n];return r.icon?e[r.iconName]=r.icon:e[n]=r,e}),{});"function"!=typeof Qs.hooks.addPack||r?Qs.styles[e]=Ss({},Qs.styles[e]||{},o):Qs.hooks.addPack(e,o),"fas"===e&&Bc("fa",t)}var _c=Qs.styles,jc=Qs.shims,Pc=function(){var e=function(e){return Sc(_c,(function(t,n,r){return t[r]=Sc(n,e,{}),t}),{})};e((function(e,t,n){return t[3]&&(e[t[3]]=n),e})),e((function(e,t,n){var r=t[2];return e[n]=n,r.forEach((function(t){e[t]=n})),e}));var t="far"in _c;Sc(jc,(function(e,n){var r=n[0],o=n[1],i=n[2];return"far"!==o||t||(o="fas"),e[r]={prefix:o,iconName:i},e}),{})};function Dc(e,t,n){if(e&&e[t]&&e[t][n])return{prefix:t,iconName:n,icon:e[t][n]}}function Tc(e){var t=e.tag,n=e.attributes,r=void 0===n?{}:n,o=e.children,i=void 0===o?[]:o;return"string"==typeof e?wc(e):"<".concat(t," ").concat(function(e){return Object.keys(e||{}).reduce((function(t,n){return t+"".concat(n,'="').concat(wc(e[n]),'" ')}),"").trim()}(r),">").concat(i.map(Tc).join(""),"</").concat(t,">")}Pc(),Qs.styles;function Ic(e){this.name="MissingIcon",this.message=e||"Icon unavailable",this.stack=(new Error).stack}Ic.prototype=Object.create(Error.prototype),Ic.prototype.constructor=Ic;var Fc={fill:"currentColor"},Mc={attributeType:"XML",repeatCount:"indefinite",dur:"2s"},Rc=(Ss({},Fc,{d:"M156.5,447.7l-12.6,29.5c-18.7-9.5-35.9-21.2-51.5-34.9l22.7-22.7C127.6,430.5,141.5,440,156.5,447.7z M40.6,272H8.5 c1.4,21.2,5.4,41.7,11.7,61.1L50,321.2C45.1,305.5,41.8,289,40.6,272z M40.6,240c1.4-18.8,5.2-37,11.1-54.1l-29.5-12.6 C14.7,194.3,10,216.7,8.5,240H40.6z M64.3,156.5c7.8-14.9,17.2-28.8,28.1-41.5L69.7,92.3c-13.7,15.6-25.5,32.8-34.9,51.5 L64.3,156.5z M397,419.6c-13.9,12-29.4,22.3-46.1,30.4l11.9,29.8c20.7-9.9,39.8-22.6,56.9-37.6L397,419.6z M115,92.4 c13.9-12,29.4-22.3,46.1-30.4l-11.9-29.8c-20.7,9.9-39.8,22.6-56.8,37.6L115,92.4z M447.7,355.5c-7.8,14.9-17.2,28.8-28.1,41.5 l22.7,22.7c13.7-15.6,25.5-32.9,34.9-51.5L447.7,355.5z M471.4,272c-1.4,18.8-5.2,37-11.1,54.1l29.5,12.6 c7.5-21.1,12.2-43.5,13.6-66.8H471.4z M321.2,462c-15.7,5-32.2,8.2-49.2,9.4v32.1c21.2-1.4,41.7-5.4,61.1-11.7L321.2,462z M240,471.4c-18.8-1.4-37-5.2-54.1-11.1l-12.6,29.5c21.1,7.5,43.5,12.2,66.8,13.6V471.4z M462,190.8c5,15.7,8.2,32.2,9.4,49.2h32.1 c-1.4-21.2-5.4-41.7-11.7-61.1L462,190.8z M92.4,397c-12-13.9-22.3-29.4-30.4-46.1l-29.8,11.9c9.9,20.7,22.6,39.8,37.6,56.9 L92.4,397z M272,40.6c18.8,1.4,36.9,5.2,54.1,11.1l12.6-29.5C317.7,14.7,295.3,10,272,8.5V40.6z M190.8,50 c15.7-5,32.2-8.2,49.2-9.4V8.5c-21.2,1.4-41.7,5.4-61.1,11.7L190.8,50z M442.3,92.3L419.6,115c12,13.9,22.3,29.4,30.5,46.1 l29.8-11.9C470,128.5,457.3,109.4,442.3,92.3z M397,92.4l22.7-22.7c-15.6-13.7-32.8-25.5-51.5-34.9l-12.6,29.5 C370.4,72.1,384.4,81.5,397,92.4z"}),Ss({},Mc,{attributeName:"opacity"}));function zc(e){var t=e[0],n=e[1],r=Bs(e.slice(4),1)[0];return{found:!0,width:t,height:n,icon:Array.isArray(r)?{tag:"g",attributes:{class:"".concat(Ks.familyPrefix,"-").concat(Vs.GROUP)},children:[{tag:"path",attributes:{class:"".concat(Ks.familyPrefix,"-").concat(Vs.SECONDARY),fill:"currentColor",d:r[0]}},{tag:"path",attributes:{class:"".concat(Ks.familyPrefix,"-").concat(Vs.PRIMARY),fill:"currentColor",d:r[1]}}]}:{tag:"path",attributes:{fill:"currentColor",d:r}}}}Ss({},Fc,{cx:"256",cy:"364",r:"28"}),Ss({},Mc,{attributeName:"r",values:"28;14;28;28;14;28;"}),Ss({},Rc,{values:"1;0;1;1;0;1;"}),Ss({},Fc,{opacity:"1",d:"M263.7,312h-16c-6.6,0-12-5.4-12-12c0-71,77.4-63.9,77.4-107.8c0-20-17.8-40.2-57.4-40.2c-29.1,0-44.3,9.6-59.2,28.7 c-3.9,5-11.1,6-16.2,2.4l-13.1-9.2c-5.6-3.9-6.9-11.8-2.6-17.2c21.2-27.2,46.4-44.7,91.2-44.7c52.3,0,97.4,29.8,97.4,80.2 c0,67.6-77.4,63.5-77.4,107.8C275.7,306.6,270.3,312,263.7,312z"}),Ss({},Rc,{values:"1;0;0;0;0;1;"}),Ss({},Fc,{opacity:"0",d:"M232.5,134.5l7,168c0.3,6.4,5.6,11.5,12,11.5h9c6.4,0,11.7-5.1,12-11.5l7-168c0.3-6.8-5.2-12.5-12-12.5h-23 C237.7,122,232.2,127.7,232.5,134.5z"}),Ss({},Rc,{values:"0;0;1;1;0;0;"}),Qs.styles,Qs.styles;var Nc='svg:not(:root).svg-inline--fa {\n  overflow: visible;\n}\n\n.svg-inline--fa {\n  display: inline-block;\n  font-size: inherit;\n  height: 1em;\n  overflow: visible;\n  vertical-align: -0.125em;\n}\n.svg-inline--fa.fa-lg {\n  vertical-align: -0.225em;\n}\n.svg-inline--fa.fa-w-1 {\n  width: 0.0625em;\n}\n.svg-inline--fa.fa-w-2 {\n  width: 0.125em;\n}\n.svg-inline--fa.fa-w-3 {\n  width: 0.1875em;\n}\n.svg-inline--fa.fa-w-4 {\n  width: 0.25em;\n}\n.svg-inline--fa.fa-w-5 {\n  width: 0.3125em;\n}\n.svg-inline--fa.fa-w-6 {\n  width: 0.375em;\n}\n.svg-inline--fa.fa-w-7 {\n  width: 0.4375em;\n}\n.svg-inline--fa.fa-w-8 {\n  width: 0.5em;\n}\n.svg-inline--fa.fa-w-9 {\n  width: 0.5625em;\n}\n.svg-inline--fa.fa-w-10 {\n  width: 0.625em;\n}\n.svg-inline--fa.fa-w-11 {\n  width: 0.6875em;\n}\n.svg-inline--fa.fa-w-12 {\n  width: 0.75em;\n}\n.svg-inline--fa.fa-w-13 {\n  width: 0.8125em;\n}\n.svg-inline--fa.fa-w-14 {\n  width: 0.875em;\n}\n.svg-inline--fa.fa-w-15 {\n  width: 0.9375em;\n}\n.svg-inline--fa.fa-w-16 {\n  width: 1em;\n}\n.svg-inline--fa.fa-w-17 {\n  width: 1.0625em;\n}\n.svg-inline--fa.fa-w-18 {\n  width: 1.125em;\n}\n.svg-inline--fa.fa-w-19 {\n  width: 1.1875em;\n}\n.svg-inline--fa.fa-w-20 {\n  width: 1.25em;\n}\n.svg-inline--fa.fa-pull-left {\n  margin-right: 0.3em;\n  width: auto;\n}\n.svg-inline--fa.fa-pull-right {\n  margin-left: 0.3em;\n  width: auto;\n}\n.svg-inline--fa.fa-border {\n  height: 1.5em;\n}\n.svg-inline--fa.fa-li {\n  width: 2em;\n}\n.svg-inline--fa.fa-fw {\n  width: 1.25em;\n}\n\n.fa-layers svg.svg-inline--fa {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n}\n\n.fa-layers {\n  display: inline-block;\n  height: 1em;\n  position: relative;\n  text-align: center;\n  vertical-align: -0.125em;\n  width: 1em;\n}\n.fa-layers svg.svg-inline--fa {\n  -webkit-transform-origin: center center;\n          transform-origin: center center;\n}\n\n.fa-layers-counter, .fa-layers-text {\n  display: inline-block;\n  position: absolute;\n  text-align: center;\n}\n\n.fa-layers-text {\n  left: 50%;\n  top: 50%;\n  -webkit-transform: translate(-50%, -50%);\n          transform: translate(-50%, -50%);\n  -webkit-transform-origin: center center;\n          transform-origin: center center;\n}\n\n.fa-layers-counter {\n  background-color: #ff253a;\n  border-radius: 1em;\n  -webkit-box-sizing: border-box;\n          box-sizing: border-box;\n  color: #fff;\n  height: 1.5em;\n  line-height: 1;\n  max-width: 5em;\n  min-width: 1.5em;\n  overflow: hidden;\n  padding: 0.25em;\n  right: 0;\n  text-overflow: ellipsis;\n  top: 0;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: top right;\n          transform-origin: top right;\n}\n\n.fa-layers-bottom-right {\n  bottom: 0;\n  right: 0;\n  top: auto;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: bottom right;\n          transform-origin: bottom right;\n}\n\n.fa-layers-bottom-left {\n  bottom: 0;\n  left: 0;\n  right: auto;\n  top: auto;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: bottom left;\n          transform-origin: bottom left;\n}\n\n.fa-layers-top-right {\n  right: 0;\n  top: 0;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: top right;\n          transform-origin: top right;\n}\n\n.fa-layers-top-left {\n  left: 0;\n  right: auto;\n  top: 0;\n  -webkit-transform: scale(0.25);\n          transform: scale(0.25);\n  -webkit-transform-origin: top left;\n          transform-origin: top left;\n}\n\n.fa-lg {\n  font-size: 1.3333333333em;\n  line-height: 0.75em;\n  vertical-align: -0.0667em;\n}\n\n.fa-xs {\n  font-size: 0.75em;\n}\n\n.fa-sm {\n  font-size: 0.875em;\n}\n\n.fa-1x {\n  font-size: 1em;\n}\n\n.fa-2x {\n  font-size: 2em;\n}\n\n.fa-3x {\n  font-size: 3em;\n}\n\n.fa-4x {\n  font-size: 4em;\n}\n\n.fa-5x {\n  font-size: 5em;\n}\n\n.fa-6x {\n  font-size: 6em;\n}\n\n.fa-7x {\n  font-size: 7em;\n}\n\n.fa-8x {\n  font-size: 8em;\n}\n\n.fa-9x {\n  font-size: 9em;\n}\n\n.fa-10x {\n  font-size: 10em;\n}\n\n.fa-fw {\n  text-align: center;\n  width: 1.25em;\n}\n\n.fa-ul {\n  list-style-type: none;\n  margin-left: 2.5em;\n  padding-left: 0;\n}\n.fa-ul > li {\n  position: relative;\n}\n\n.fa-li {\n  left: -2em;\n  position: absolute;\n  text-align: center;\n  width: 2em;\n  line-height: inherit;\n}\n\n.fa-border {\n  border: solid 0.08em #eee;\n  border-radius: 0.1em;\n  padding: 0.2em 0.25em 0.15em;\n}\n\n.fa-pull-left {\n  float: left;\n}\n\n.fa-pull-right {\n  float: right;\n}\n\n.fa.fa-pull-left,\n.fas.fa-pull-left,\n.far.fa-pull-left,\n.fal.fa-pull-left,\n.fab.fa-pull-left {\n  margin-right: 0.3em;\n}\n.fa.fa-pull-right,\n.fas.fa-pull-right,\n.far.fa-pull-right,\n.fal.fa-pull-right,\n.fab.fa-pull-right {\n  margin-left: 0.3em;\n}\n\n.fa-spin {\n  -webkit-animation: fa-spin 2s infinite linear;\n          animation: fa-spin 2s infinite linear;\n}\n\n.fa-pulse {\n  -webkit-animation: fa-spin 1s infinite steps(8);\n          animation: fa-spin 1s infinite steps(8);\n}\n\n@-webkit-keyframes fa-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n            transform: rotate(360deg);\n  }\n}\n\n@keyframes fa-spin {\n  0% {\n    -webkit-transform: rotate(0deg);\n            transform: rotate(0deg);\n  }\n  100% {\n    -webkit-transform: rotate(360deg);\n            transform: rotate(360deg);\n  }\n}\n.fa-rotate-90 {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";\n  -webkit-transform: rotate(90deg);\n          transform: rotate(90deg);\n}\n\n.fa-rotate-180 {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";\n  -webkit-transform: rotate(180deg);\n          transform: rotate(180deg);\n}\n\n.fa-rotate-270 {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";\n  -webkit-transform: rotate(270deg);\n          transform: rotate(270deg);\n}\n\n.fa-flip-horizontal {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";\n  -webkit-transform: scale(-1, 1);\n          transform: scale(-1, 1);\n}\n\n.fa-flip-vertical {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";\n  -webkit-transform: scale(1, -1);\n          transform: scale(1, -1);\n}\n\n.fa-flip-both, .fa-flip-horizontal.fa-flip-vertical {\n  -ms-filter: "progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";\n  -webkit-transform: scale(-1, -1);\n          transform: scale(-1, -1);\n}\n\n:root .fa-rotate-90,\n:root .fa-rotate-180,\n:root .fa-rotate-270,\n:root .fa-flip-horizontal,\n:root .fa-flip-vertical,\n:root .fa-flip-both {\n  -webkit-filter: none;\n          filter: none;\n}\n\n.fa-stack {\n  display: inline-block;\n  height: 2em;\n  position: relative;\n  width: 2.5em;\n}\n\n.fa-stack-1x,\n.fa-stack-2x {\n  bottom: 0;\n  left: 0;\n  margin: auto;\n  position: absolute;\n  right: 0;\n  top: 0;\n}\n\n.svg-inline--fa.fa-stack-1x {\n  height: 1em;\n  width: 1.25em;\n}\n.svg-inline--fa.fa-stack-2x {\n  height: 2em;\n  width: 2.5em;\n}\n\n.fa-inverse {\n  color: #fff;\n}\n\n.sr-only {\n  border: 0;\n  clip: rect(0, 0, 0, 0);\n  height: 1px;\n  margin: -1px;\n  overflow: hidden;\n  padding: 0;\n  position: absolute;\n  width: 1px;\n}\n\n.sr-only-focusable:active, .sr-only-focusable:focus {\n  clip: auto;\n  height: auto;\n  margin: 0;\n  overflow: visible;\n  position: static;\n  width: auto;\n}\n\n.svg-inline--fa .fa-primary {\n  fill: var(--fa-primary-color, currentColor);\n  opacity: 1;\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa .fa-secondary {\n  fill: var(--fa-secondary-color, currentColor);\n  opacity: 0.4;\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-primary {\n  opacity: 0.4;\n  opacity: var(--fa-secondary-opacity, 0.4);\n}\n\n.svg-inline--fa.fa-swap-opacity .fa-secondary {\n  opacity: 1;\n  opacity: var(--fa-primary-opacity, 1);\n}\n\n.svg-inline--fa mask .fa-primary,\n.svg-inline--fa mask .fa-secondary {\n  fill: black;\n}\n\n.fad.fa-inverse {\n  color: #fff;\n}';var Lc=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.definitions={}}var t,n;return t=e,n=[{key:"add",value:function(){for(var e=this,t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];var o=n.reduce(this._pullDefinitions,{});Object.keys(o).forEach((function(t){e.definitions[t]=Ss({},e.definitions[t]||{},o[t]),Bc(t,o[t]),Pc()}))}},{key:"reset",value:function(){this.definitions={}}},{key:"_pullDefinitions",value:function(e,t){var n=t.prefix&&t.iconName&&t.icon?{0:t}:t;return Object.keys(n).map((function(t){var r=n[t],o=r.prefix,i=r.iconName,a=r.icon;e[o]||(e[o]={}),e[o][i]=a})),e}}],n&&xs(t.prototype,n),e}();function qc(){Ks.autoAddCss&&!Hc&&(function(e){if(e&&zs){var t=Ms.createElement("style");t.setAttribute("type","text/css"),t.innerHTML=e;for(var n=Ms.head.childNodes,r=null,o=n.length-1;o>-1;o--){var i=n[o],a=(i.tagName||"").toUpperCase();["STYLE","LINK"].indexOf(a)>-1&&(r=i)}Ms.head.insertBefore(t,r)}}(function(){var e=Ls,t=qs,n=Ks.familyPrefix,r=Ks.replacementClass,o=Nc;if(n!==e||r!==t){var i=new RegExp("\\.".concat(e,"\\-"),"g"),a=new RegExp("\\--".concat(e,"\\-"),"g"),l=new RegExp("\\.".concat(t),"g");o=o.replace(i,".".concat(n,"-")).replace(a,"--".concat(n,"-")).replace(l,".".concat(r))}return o}()),Hc=!0)}function Uc(e){var t=e.prefix,n=void 0===t?"fa":t,r=e.iconName;if(r)return Dc(Vc.definitions,n,r)||Dc(Qs.styles,n,r)}var Wc,Vc=new Lc,Hc=!1,Yc={transform:function(e){return function(e){var t={size:16,x:0,y:0,flipX:!1,flipY:!1,rotate:0};return e?e.toLowerCase().split(" ").reduce((function(e,t){var n=t.toLowerCase().split("-"),r=n[0],o=n.slice(1).join("-");if(r&&"h"===o)return e.flipX=!0,e;if(r&&"v"===o)return e.flipY=!0,e;if(o=parseFloat(o),isNaN(o))return e;switch(r){case"grow":e.size=e.size+o;break;case"shrink":e.size=e.size-o;break;case"left":e.x=e.x-o;break;case"right":e.x=e.x+o;break;case"up":e.y=e.y-o;break;case"down":e.y=e.y+o;break;case"rotate":e.rotate=e.rotate+o}return e}),t):t}(e)}},Kc=(Wc=function(e){var t,n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=r.transform,i=void 0===o?yc:o,a=r.symbol,l=void 0!==a&&a,s=r.mask,c=void 0===s?null:s,u=r.maskId,p=void 0===u?null:u,d=r.title,f=void 0===d?null:d,h=r.titleId,v=void 0===h?null:h,b=r.classes,A=void 0===b?[]:b,y=r.attributes,g=void 0===y?{}:y,m=r.styles,w=void 0===m?{}:m;if(e){var E=e.prefix,C=e.iconName,k=e.icon;return t=Ss({type:"icon"},e),n=function(){return qc(),Ks.autoA11y&&(f?g["aria-labelledby"]="".concat(Ks.replacementClass,"-title-").concat(v||mc()):(g["aria-hidden"]="true",g.focusable="false")),function(e){var t=e.icons,n=t.main,r=t.mask,o=e.prefix,i=e.iconName,a=e.transform,l=e.symbol,s=e.title,c=e.maskId,u=e.titleId,p=e.extra,d=e.watchable,f=void 0!==d&&d,h=r.found?r:n,v=h.width,b=h.height,A="fak"===o,y=A?"":"fa-w-".concat(Math.ceil(v/b*16)),g=[Ks.replacementClass,i?"".concat(Ks.familyPrefix,"-").concat(i):"",y].filter((function(e){return-1===p.classes.indexOf(e)})).filter((function(e){return""!==e||!!e})).concat(p.classes).join(" "),m={children:[],attributes:Ss({},p.attributes,{"data-prefix":o,"data-icon":i,class:g,role:p.attributes.role||"img",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 ".concat(v," ").concat(b)})},w=A&&!~p.classes.indexOf("fa-fw")?{width:"".concat(v/b*16*.0625,"em")}:{};f&&(m.attributes["data-fa-i2svg"]=""),s&&m.children.push({tag:"title",attributes:{id:m.attributes["aria-labelledby"]||"title-".concat(u||mc())},children:[s]});var E=Ss({},m,{prefix:o,iconName:i,main:n,mask:r,maskId:c,transform:a,symbol:l,styles:Ss({},w,p.styles)}),C=r.found&&n.found?function(e){var t,n=e.children,r=e.attributes,o=e.main,i=e.mask,a=e.maskId,l=e.transform,s=o.width,c=o.icon,u=i.width,p=i.icon,d=kc({transform:l,containerWidth:u,iconWidth:s}),f={tag:"rect",attributes:Ss({},xc,{fill:"white"})},h=c.children?{children:c.children.map(Oc)}:{},v={tag:"g",attributes:Ss({},d.inner),children:[Oc(Ss({tag:c.tag,attributes:Ss({},c.attributes,d.path)},h))]},b={tag:"g",attributes:Ss({},d.outer),children:[v]},A="mask-".concat(a||mc()),y="clip-".concat(a||mc()),g={tag:"mask",attributes:Ss({},xc,{id:A,maskUnits:"userSpaceOnUse",maskContentUnits:"userSpaceOnUse"}),children:[f,b]},m={tag:"defs",children:[{tag:"clipPath",attributes:{id:y},children:(t=p,"g"===t.tag?t.children:[t])},g]};return n.push(m,{tag:"rect",attributes:Ss({fill:"currentColor","clip-path":"url(#".concat(y,")"),mask:"url(#".concat(A,")")},xc)}),{children:n,attributes:r}}(E):function(e){var t=e.children,n=e.attributes,r=e.main,o=e.transform,i=Ec(e.styles);if(i.length>0&&(n.style=i),Cc(o)){var a=kc({transform:o,containerWidth:r.width,iconWidth:r.width});t.push({tag:"g",attributes:Ss({},a.outer),children:[{tag:"g",attributes:Ss({},a.inner),children:[{tag:r.icon.tag,children:r.icon.children,attributes:Ss({},r.icon.attributes,a.path)}]}]})}else t.push(r.icon);return{children:t,attributes:n}}(E),k=C.children,x=C.attributes;return E.children=k,E.attributes=x,l?function(e){var t=e.prefix,n=e.iconName,r=e.children,o=e.attributes,i=e.symbol;return[{tag:"svg",attributes:{style:"display: none;"},children:[{tag:"symbol",attributes:Ss({},o,{id:!0===i?"".concat(t,"-").concat(Ks.familyPrefix,"-").concat(n):i}),children:r}]}]}(E):function(e){var t=e.children,n=e.main,r=e.mask,o=e.attributes,i=e.styles,a=e.transform;if(Cc(a)&&n.found&&!r.found){var l={x:n.width/n.height/2,y:.5};o.style=Ec(Ss({},i,{"transform-origin":"".concat(l.x+a.x/16,"em ").concat(l.y+a.y/16,"em")}))}return[{tag:"svg",attributes:o,children:t}]}(E)}({icons:{main:zc(k),mask:c?zc(c.icon):{found:!1,width:null,height:null,icon:{}}},prefix:E,iconName:C,transform:Ss({},yc,i),symbol:l,title:f,maskId:p,titleId:v,extra:{attributes:g,styles:w,classes:A}})},Object.defineProperty(t,"abstract",{get:n}),Object.defineProperty(t,"html",{get:function(){return t.abstract.map((function(e){return Tc(e)}))}}),Object.defineProperty(t,"node",{get:function(){if(zs){var e=Ms.createElement("div");return e.innerHTML=t.html,e.children}}}),t}},function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=(e||{}).icon?e:Uc(e||{}),r=t.mask;return r&&(r=(r||{}).icon?r:Uc(r||{})),Wc(n,Ss({},t,{mask:r}))});function Zc(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Qc(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Zc(Object(n),!0).forEach((function(t){Gc(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Zc(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function $c(e){return $c="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},$c(e)}function Gc(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Jc(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n,r,o={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(o[n]=e[n]);return o}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}function Xc(e){return function(e){if(Array.isArray(e))return eu(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return eu(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?eu(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function eu(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function tu(e){return t=e,(t-=0)==t?e:(e=e.replace(/[\-_\s]+(.)?/g,(function(e,t){return t?t.toUpperCase():""}))).substr(0,1).toLowerCase()+e.substr(1);var t}var nu=["style"],ru=!1;try{ru=!0}catch(e){}function ou(e){return e&&"object"===$c(e)&&e.prefix&&e.iconName&&e.icon?e:Yc.icon?Yc.icon(e):null===e?null:e&&"object"===$c(e)&&e.prefix&&e.iconName?e:Array.isArray(e)&&2===e.length?{prefix:e[0],iconName:e[1]}:"string"==typeof e?{prefix:"fas",iconName:e}:void 0}function iu(e,t){return Array.isArray(t)&&t.length>0||!Array.isArray(t)&&t?Gc({},e,t):{}}var au=["forwardedRef"];function lu(e){var t=e.forwardedRef,n=Jc(e,au),r=n.icon,o=n.mask,i=n.symbol,a=n.className,l=n.title,s=n.titleId,c=n.maskId,u=ou(r),p=iu("classes",[].concat(Xc(function(e){var t,n=e.beat,r=e.fade,o=e.beatFade,i=e.bounce,a=e.shake,l=e.flash,s=e.spin,c=e.spinPulse,u=e.spinReverse,p=e.pulse,d=e.fixedWidth,f=e.inverse,h=e.border,v=e.listItem,b=e.flip,A=e.size,y=e.rotation,g=e.pull,m=(Gc(t={"fa-beat":n,"fa-fade":r,"fa-beat-fade":o,"fa-bounce":i,"fa-shake":a,"fa-flash":l,"fa-spin":s,"fa-spin-reverse":u,"fa-spin-pulse":c,"fa-pulse":p,"fa-fw":d,"fa-inverse":f,"fa-border":h,"fa-li":v,"fa-flip":!0===b,"fa-flip-horizontal":"horizontal"===b||"both"===b,"fa-flip-vertical":"vertical"===b||"both"===b},"fa-".concat(A),null!=A),Gc(t,"fa-rotate-".concat(y),null!=y&&0!==y),Gc(t,"fa-pull-".concat(g),null!=g),Gc(t,"fa-swap-opacity",e.swapOpacity),t);return Object.keys(m).map((function(e){return m[e]?e:null})).filter((function(e){return e}))}(n)),Xc(a.split(" ")))),d=iu("transform","string"==typeof n.transform?Yc.transform(n.transform):n.transform),f=iu("mask",ou(o)),h=Kc(u,Qc(Qc(Qc(Qc({},p),d),f),{},{symbol:i,title:l,titleId:s,maskId:c}));if(!h)return function(){var e;!ru&&console&&"function"==typeof console.error&&(e=console).error.apply(e,arguments)}("Could not find icon",u),null;var v=h.abstract,b={ref:t};return Object.keys(n).forEach((function(e){lu.defaultProps.hasOwnProperty(e)||(b[e]=n[e])})),su(v[0],b)}lu.displayName="FontAwesomeIcon",lu.propTypes={beat:qt().bool,border:qt().bool,beatFade:qt().bool,bounce:qt().bool,className:qt().string,fade:qt().bool,flash:qt().bool,mask:qt().oneOfType([qt().object,qt().array,qt().string]),maskId:qt().string,fixedWidth:qt().bool,inverse:qt().bool,flip:qt().oneOf([!0,!1,"horizontal","vertical","both"]),icon:qt().oneOfType([qt().object,qt().array,qt().string]),listItem:qt().bool,pull:qt().oneOf(["right","left"]),pulse:qt().bool,rotation:qt().oneOf([0,90,180,270]),shake:qt().bool,size:qt().oneOf(["2xs","xs","sm","lg","xl","2xl","1x","2x","3x","4x","5x","6x","7x","8x","9x","10x"]),spin:qt().bool,spinPulse:qt().bool,spinReverse:qt().bool,symbol:qt().oneOfType([qt().bool,qt().string]),title:qt().string,titleId:qt().string,transform:qt().oneOfType([qt().string,qt().object]),swapOpacity:qt().bool},lu.defaultProps={border:!1,className:"",mask:null,maskId:null,fixedWidth:!1,inverse:!1,flip:!1,icon:null,listItem:!1,pull:null,pulse:!1,rotation:null,size:null,spin:!1,spinPulse:!1,spinReverse:!1,beat:!1,fade:!1,beatFade:!1,bounce:!1,shake:!1,symbol:!1,title:"",titleId:null,transform:null,swapOpacity:!1};var su=function e(t,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if("string"==typeof n)return n;var o=(n.children||[]).map((function(n){return e(t,n)})),i=Object.keys(n.attributes||{}).reduce((function(e,t){var r=n.attributes[t];switch(t){case"class":e.attrs.className=r,delete n.attributes.class;break;case"style":e.attrs.style=r.split(";").map((function(e){return e.trim()})).filter((function(e){return e})).reduce((function(e,t){var n,r=t.indexOf(":"),o=tu(t.slice(0,r)),i=t.slice(r+1).trim();return o.startsWith("webkit")?e[(n=o,n.charAt(0).toUpperCase()+n.slice(1))]=i:e[o]=i,e}),{});break;default:0===t.indexOf("aria-")||0===t.indexOf("data-")?e.attrs[t.toLowerCase()]=r:e.attrs[tu(t)]=r}return e}),{attrs:{}}),a=r.style,l=void 0===a?{}:a,s=Jc(r,nu);return i.attrs.style=Qc(Qc({},i.attrs.style),l),t.apply(void 0,[n.tag,Qc(Qc({},i.attrs),s)].concat(Xc(o)))}.bind(null,o().createElement);function cu(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function uu(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}var pu=function(e,t,n,r,o,a,l,s,c,u,p,d){return function(){var f=e(n,r,a,l,s,d),h=k(n,r,l,s);e===O&&i.jVA(t,h).length>0&&(f.selected_columns=i.zud(h,t)),u(f);var v=[];i.Ed_((function(e){var t=r.find((function(t){return t.id===e}));t&&v.push(t)}),h),function(e,t,n,r){t.forEach((function(t){e=ds(e,t,"")})),fs(e,n,r)}(p,v,o,c)}};function du(e,t,n,r){return function(){var o;switch(vu(e,t)){case ys.Descending:o=ys.None;break;case ys.Ascending:o=ys.Descending;break;case ys.None:default:o=ys.Ascending}var i=n===_.h8.Single?Cs:Es;r(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?uu(Object(n),!0).forEach((function(t){var r,o,i;r=e,o=t,i=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):uu(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({sort_by:i(t,{column_id:e,direction:o})},B))}}function fu(e,t,n,r,o){return function(){var a=function(e,t,n,r){var o=window.prompt("Enter a new column name");return null===o?null:function(e,t,n,r,o){var a=t,l=w(a),s=a.findIndex((function(t){return t.id===e.id}));if("string"==typeof e.name&&l>1){var c=Array(l).fill(e.name),u=i.ATH(e,{name:c});(a=a.slice(0))[s]=u}var p=C(e,a,n,r,s,!0),d=p.groupIndexFirst,f=p.groupIndexLast;return i.w6H(d,f+1).map((function(e){var t=[e,"name"];"Array"===i.dt8(a[e].name)&&t.push(n),a=i.t8m(i.QMA(t),o,a)})),{columns:a}}(e,t,n,r,o)}(e,t,n,o);a&&r(a)}}function hu(e,t,n,r,o,a,l,s){if(l&&!s)return function(){};var c=k(t,n,r,a,!0);return l?function(){return o({selected_columns:c})}:s?function(){return o({selected_columns:i.G0j(e,c)})}:function(){return o({selected_columns:i.zud(c,e)})}}function vu(e,t){var n=i.sEJ((function(t){return t.column_id===e}),t);return n?n.direction:ys.None}function bu(e,t){switch(vu(e,t)){case ys.Descending:return"sort-down";case ys.Ascending:return"sort-up";case ys.None:default:return"sort"}}var Au=(0,a.Pi)((function(e,t,n,r,a,l,s,c,u,p,d,f,h,v,b,A,y){return i.h0F(i.UID)((function(g,m){var w=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],s=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return cu(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?cu(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(g,2),E=w[0],C=w[1],B=l.length-1,j=m===B;return i.h0F(i.UID)((function(l,g){var w,P=t[l];w=y?l===i.Z$Q(C)?E.length-l:C[g+1]-l:1;var D=v!==_.p9.Custom&&Ue(m,B,P.clearable),T=v!==_.p9.Custom&&Ue(m,B,P.deletable),I=Ue(m,B,P.hideable),F=Ue(m,B,P.renamable),M=Ue(m,B,P.selectable),R=t.length===w,z=k(P,n,m,y,!0),N=M&&("single"!==c||u.length===z.length)&&i.$6P((function(e){return-1!==u.indexOf(e)}),z);return o().createElement("div",{key:l},o().createElement("div",{className:"column-actions"},c&&M?o().createElement("span",{className:"column-header--select"},o().createElement("input",{checked:N,onChange:hu(u,P,n,m,A,y,"single"===c,!N),name:"column-select-".concat(e),type:"single"===c?"radio":"checkbox"})):null,p!==_.p9.None&&j?o().createElement("span",{className:"column-header--sort",onClick:du(P.id,f,d,A)},o().createElement(lu,{icon:bu(P.id,f)})):null,F?o().createElement("span",{className:"column-header--edit",onClick:fu(P,n,m,A,y)},o().createElement(lu,{icon:"pencil-alt"})):null,D?o().createElement("span",{className:"column-header--clear",onClick:pu(x,u,P,n,h,t,m,y,b,A,s,a)},o().createElement(lu,{icon:"eraser"})):null,T?o().createElement("span",{className:"column-header--delete"+(R?" disabled":""),onClick:R?void 0:pu(O,u,P,n,h,t,m,y,b,A,s,a)},o().createElement(lu,{icon:["far","trash-alt"]})):null,I?o().createElement("span",{className:"column-header--hide"+(R?" disabled":""),onClick:R?void 0:function(){var e=S(P,t,m,y),n=r?i.G0j(r,e):e;A({hidden_columns:n})}},o().createElement(lu,{icon:["far","eye-slash"]})):null),o().createElement("span",{className:"column-header-name"},E[l]))}),C)}),l)}));function yu(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function gu(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Eu(r.key),r)}}function mu(e,t,n){return t&&gu(e.prototype,t),n&&gu(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function wu(e,t,n){return(t=Eu(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Eu(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var Cu=mu((function e(t){var n=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:mt(t);!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),wu(this,"handlers",void 0),wu(this,"get",(function(e,t,r){return t.map((function(t,o){var a=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],s=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||function(e,t){if(e){if("string"==typeof e)return yu(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?yu(e,t):void 0}}(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(t,2),l=a[0],s=a[1];return s.map((function(t,a){var c,u=e[t];return c=r?t===i.Z$Q(s)?l.length-t:s[a+1]-t:1,n.wrapper.get(o,t)(t,u.id,c,t===e.length-1||t===i.Z$Q(s),n.handlers(nt.EnterHeader,o,t),n.handlers(nt.Leave,o,t),n.handlers(nt.MoveHeader,o,t))}))}))})),wu(this,"wrapper",$e()((function(e,t,n,r,i,a,l){return o().createElement("th",{key:"header-cell-".concat(e),"data-dash-column":t,colSpan:n,className:"dash-header "+"column-".concat(e," ")+(r?"cell--right-last ":""),onMouseEnter:i,onMouseLeave:a,onMouseMove:l})}))),this.handlers=r})),ku=(0,a.Pi)((function(e,t,n){return Ye(i.w6H(0,t),e,(function(e,t){return function(e,t){return function(n){return al(La(e,t)(n))}}(e,t)(n)}))})),xu=(0,a.Pi)((function(e,t,n){return Ye(i.w6H(0,e),i.w6H(0,t),(function(e){return function(e){return function(t){return al(Wa(e)(t))}}(e)(n)}))}));function Ou(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Bu(r.key),r)}}function Su(e,t,n){return(t=Bu(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Bu(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var _u=function(){function e(t){var n=this;!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),Su(this,"propsFn",void 0),Su(this,"headerContent",Au()),Su(this,"headerOperations",cs()),Su(this,"headerStyles",ku()),Su(this,"headerOpStyles",xu()),Su(this,"headerWrappers",new Cu((function(){return n.props}))),Su(this,"relevantStyles",ol()),Su(this,"labelsAndIndices",We()),Su(this,"filterMergedCells",(0,a.qe)((function(e,t){for(var n=[],r=0;r<e.length;r++){for(var o=[],i=0;i<e[r].length;i++)t[r][1].includes(i)&&o.push(e[r][i]);n.push(o)}return n}))),Su(this,"getCells",(0,a.qe)((function(e,t){return Q(e,t,(function(e,t){return Array.prototype.concat(e,t)}))}))),Su(this,"getHeaderOpCells",(0,a.qe)((function(e,t,n){return Ke(e,t,(function(e,t,r,a){return o().cloneElement(e,{style:i.Jnq([n&&n.getStyle(r,a),t,e.props.style])})}))}))),Su(this,"getHeaderCells",(0,a.qe)((function(e,t,n,r){return Ze(e,n,t,(function(e,t,n,a,l){return o().cloneElement(e,{children:[n],style:i.ATH(t||{},r&&r.getStyle(a,l)||{})})}))}))),this.propsFn=t}var t,n;return t=e,n=[{key:"props",get:function(){return this.propsFn()}},{key:"createHeaders",value:function(e,t){var n=this.props,r=n.column_selectable,o=n.columns,i=n.data,a=n.filter_action,l=n.hidden_columns,s=n.id,c=n.map,u=n.merge_duplicate_headers,p=n.page_action,d=n.row_deletable,f=n.row_selectable,h=n.selected_columns,v=n.setFilter,b=n.setProps,A=n.sort_action,y=n.sort_by,g=n.sort_mode,m=n.style_cell,w=n.style_cell_conditional,E=n.style_header,C=n.style_header_conditional,k=n.visibleColumns,x=this.labelsAndIndices(o,k,u),O=x.length,S=this.relevantStyles(m,E,w,C),B=this.headerOperations(O,f,d),_=this.headerStyles(k,O,S),j=this.headerOpStyles(O,(f?1:0)+(d?1:0),S),P=this.headerWrappers.get(k,x,u),D=this.headerContent(s,k,o,l,i,x,c,r,h,A,g,y,a.operator,p,v,b,u),T=this.getHeaderOpCells(B,j,t),I=this.filterMergedCells(_,x),F=this.getHeaderCells(P,D,I,e);return this.getCells(T,F)}}],n&&Ou(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}();function ju(e){return function(e){if(Array.isArray(e))return Pu(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||function(e,t){if(e){if("string"==typeof e)return Pu(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?Pu(e,t):void 0}}(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function Pu(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function Du(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Tu(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Du(Object(n),!0).forEach((function(t){var r,o,i;r=e,o=t,i=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Du(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var Iu=function(e,t,n,r,o){e(Tu({filter_query:n},B)),t({workFilter:{map:o,value:n},rawFilterQuery:r})},Fu=function(e){var t=(0,a.qe)((function(e,t){return Iu.bind(void 0,e,t)})),n=(0,a.qe)((function(e,t){return function(n){var r=ju(t),o=t.indexOf(n),i=Tu({},r[o]);return i.filter_options=Tu(Tu({},i.filter_options),{},{case:i.filter_options.case===_.oN.Insensitive?_.oN.Sensitive:_.oN.Insensitive}),r.splice(o,1,i),e({columns:r}),i}})),r=new xl(e),o=function(){var r=e();return function(e,t,n){var r=e();return i.ATH(r,{map:r.workFilter.map,setFilter:t,toggleFilterOptions:n})}(e,t(r.setProps,r.setState),n(r.setProps,r.columns))},l=new ms(o),s=new _u(o),c=new Ul(e),u=(0,a.qe)((function(e,t,n){var r=[];return r.push.apply(r,ju(n)),r.push.apply(r,ju(t)),r.push.apply(r,ju(e)),r}));return function(){var e=c.createEdges(),t=r.createCells(e.dataEdges,e.dataOpEdges),n=l.createFilters(e.filterEdges,e.filterOpEdges),o=s.createHeaders(e.headerEdges,e.headerOpEdges);return u(t,n,o)}};function Mu(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ru(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Mu(Object(n),!0).forEach((function(t){var r,o,i;r=e,o=t,i=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Mu(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function zu(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;return e?o().createElement("table",{className:"cell-table",tabIndex:-1},o().createElement("tbody",null,e.map((function(e,n){return o().createElement("tr",{key:"row-".concat(n+t)},e)})))):null}var Nu=function(e){return o().cloneElement(e,Ru(Ru({},e.props),{},{className:e.props.className?"".concat(e.props.className," phantom-cell"):"phantom-cell"}),"th"===e.type||"td"===e.type?null:e.props.children)},Lu=function(e){return!e||0===e.length||0===e[0].length},qu=(0,a.Pi)((function(e,t,n,r){var a=function(t){return i.jQz((function(t){return t.count<e}),(function(e,t){return e.cells++,e.count+=t.props.colSpan||1,e}),{cells:0,count:0},t).cells},l=e?i.UID((function(t){var n,r=a(t),l=t.slice(0,r).map((function(t,n){return r=t,a=e-n-1,o().cloneElement(r,Ru(Ru({},r.props),{},{colSpan:i.kKJ(r.props.colSpan)?r.props.colSpan:Math.min(r.props.colSpan,a)}));var r,a})).concat(t.slice(r).map(Nu));return l[r-1]=(n=l[r-1],o().cloneElement(n,Ru(Ru({},n.props),{},{className:n.props.className?"".concat(n.props.className," last-of-type"):"last-of-type"}))),l}),n):null;n=i.kKJ(l)?n:i.UID((function(e){var t=a(e);return e.slice(0,t).map(Nu).concat(e.slice(t))}),n);var s=t?n.slice(0,t):null;n=n.slice(t);var c=t&&l?l.slice(0,t):null;return l=l&&l.slice(t),{grid:[[zu(c),zu(s)],[zu(l),zu(n,r)]],empty:[[Lu(c),Lu(s)],[Lu(l),Lu(n)]]}})),Uu=n(335),Wu=2147483647;function Vu(e){return"number"==typeof e?e:0}function Hu(e){return"number"==typeof e?e:Wu}var Yu,Ku=(0,a.qe)((function(e,t,n,r,o,a,l,s){var c,u=function(e,t,n,r,o,a){if(e){var l=e.header,s=e.id,c=e.row;if(void 0!==s&&void 0!==c){var u,p=l?void 0:i.dFj((function(e){return!e.if||Ma(e.if,s)&&Ra(e.if,c)&&(t=e.if,n=a.data[c-a.offset.rows],!t||void 0===t.filter_query||function(e,t){return e.isValid&&e.evaluate(t)}(new ya(t.filter_query),n));var t,n}),r);if(p)return p;if(l){var d=null==n?void 0:n[s];u=Array.isArray(d)?null==d?void 0:d[c]:d}else{var f;u=null==t||null===(f=t[c])||void 0===f?void 0:f[s]}if(u)return u;var h=null==o?void 0:o[s],v=h&&"string"!=typeof h?h.use_with:Uu.a.Both;return v===Uu.a.Both||v===Uu.a.Header===l?h:void 0}}}(e,t,n,r,o,a),p=Vu(l),d=Hu(s),f=Uu.v.Text;return u&&("string"==typeof u?c=u:(p=function(e,t){return"number"==typeof e||null===e?Vu(e):t}(u.delay,p),d=function(e,t){return"number"==typeof e||null===e?Hu(e):t}(u.duration,d),f=u.type||Uu.v.Text,c=u.value)),{delay:p,duration:d,type:f,value:c}})),Zu=n(70);function Qu(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function $u(e,t,n){return(t=Ju(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Gu(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Ju(r.key),r)}}function Ju(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}function Xu(e,t){return Xu=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Xu(e,t)}function ep(e){return ep=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ep(e)}!function(e){e.Bottom="bottom",e.Left="left",e.Right="right",e.Top="top"}(Yu||(Yu={}));var tp=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Xu(e,t)}(s,e);var t,n,r,a,l=(r=s,a=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=ep(r);if(a){var n=ep(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,e)});function s(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,s),(t=l.call(this,e)).state={md:new lo},t}return t=s,n=[{key:"UNSAFE_componentWillReceiveProps",value:function(e){var t=this,n=e.tooltip,r=n.delay,o=n.duration;(0,Zu.X)(i.CEd(["arrow"],this.props),i.CEd(["arrow"],e))||this.setState({display:!1,displayTooltipId:Boolean(clearTimeout(this.state.displayTooltipId))||setTimeout((function(){return t.setState({display:!0})}),r),hideTooltipId:Boolean(clearTimeout(this.state.hideTooltipId))||setTimeout((function(){return t.setState({display:!1})}),Math.min(r+o,Wu))})}},{key:"render",value:function(){var e=this.props,t=e.arrow,n=e.className,r=this.props.tooltip,i=r.type,a=r.value,l=this.state.md;if(!i||!a)return null;var s=i===Uu.v.Text?{children:a}:{dangerouslySetInnerHTML:{__html:l.render(a)}},c=this.state.display;return o().createElement("div",{className:"dash-tooltip","data-attr-anchor":t,style:{visibility:c?"visible":"hidden"}},o().createElement("div",function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Qu(Object(n),!0).forEach((function(t){$u(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Qu(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({className:n},s)))}}],n&&Gu(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),s}(r.PureComponent);function np(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function rp(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,sp(r.key),r)}}function op(e,t){return op=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},op(e,t)}function ip(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ap(e){return ap=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},ap(e)}function lp(e,t,n){return(t=sp(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function sp(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var cp=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&op(e,t)}(l,e);var t,n,r,i,a=(r=l,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=ap(r);if(i){var n=ap(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ip(e)}(this,e)});function l(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),lp(ip(t=a.call(this,e)),"updateBounds",(function(e){t.setState({cell:e})})),t.state={arrow:Yu.Bottom},t}return t=l,n=[{key:"shouldComponentUpdate",value:function(e,t){return this.adjustPosition(),!(0,Zu.X)(this.props,e)||!(0,Zu.X)(this.state,t)}},{key:"componentDidUpdate",value:function(){this.adjustPosition()}},{key:"render",value:function(){var e=this.state.arrow;return o().createElement(tp,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?np(Object(n),!0).forEach((function(t){lp(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):np(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({key:"tooltip",ref:"tooltip",arrow:e},this.props))}},{key:"adjustPosition",value:function(){var e=this.state.cell,t=Wt().findDOMNode(this.refs.tooltip),n=function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;if(e){for(var t=e;"relative"!==getComputedStyle(t).position&&"sticky"!==getComputedStyle(t).position&&t.parentElement;)t=t.parentElement;return t}}(t);if(n&&e&&t){var r=n.getBoundingClientRect(),o=e.getBoundingClientRect(),i=t.clientWidth,a=t.clientHeight,l=Math.max(parseFloat(getComputedStyle(t,":before").borderWidth||"0"),parseFloat(getComputedStyle(t,":after").borderWidth||"0")),s=(o.width-i)/2,c=o.left-r.left+n.scrollLeft+s,u=o.top-r.top+n.scrollTop+o.height,p=c+r.left,d=p+i,f=u+r.top+a+l,h=Yu.Top;c-=Math.min(0,p),c-=Math.max(0,d-document.documentElement.clientWidth),f>document.documentElement.clientHeight&&(u-=a+l+o.height,h=Yu.Bottom),t.style.top="".concat(u,"px"),t.style.left="".concat(c,"px"),t.style.position="absolute",this.state.arrow!==h&&this.setState({arrow:h})}}}],n&&rp(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),l}(r.Component);function up(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,hp(r.key),r)}}function pp(e,t){return pp=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},pp(e,t)}function dp(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function fp(e){return fp=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},fp(e)}function hp(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var vp=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&pp(e,t)}(l,e);var t,n,r,i,a=(r=l,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=fp(r);if(i){var n=fp(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return dp(e)}(this,e)});function l(e){var t,n,r,o;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),n=dp(t=a.call(this,e)),o=function(e){var n=t.props.paginator,r=parseInt(e,10);isNaN(r)||n.loadPage(r-1)},(r=hp(r="goToPage"))in n?Object.defineProperty(n,r,{value:o,enumerable:!0,configurable:!0,writable:!0}):n[r]=o,t}return t=l,n=[{key:"render",value:function(){var e,t=this,n=this.props,r=n.paginator,i=n.page_current;if(void 0!==r.lastPage&&r.lastPage<=0)return null;var a=Math.max(3,((null!==(e=r.lastPage)&&void 0!==e?e:0)+1).toString().length),l="".concat(a+1,"ch");return o().createElement("div",{className:"previous-next-container"},o().createElement("button",{className:"first-page",onClick:r.loadFirst,disabled:!r.hasPrevious()},o().createElement(lu,{icon:"angle-double-left"})),o().createElement("button",{className:"previous-page",onClick:r.loadPrevious,disabled:!r.hasPrevious()},o().createElement(lu,{icon:"angle-left"})),o().createElement("div",{className:"page-number"},o().createElement("div",{className:"current-page-container"},o().createElement("div",{className:"current-page-shadow",style:{minWidth:l}},(i+1).toString()),o().createElement("input",{type:"text",className:"current-page",style:{minWidth:l},onBlur:function(e){t.goToPage(e.target.value),e.target.value=""},onKeyDown:function(e){e.keyCode===v.ENTER&&e.currentTarget.blur()},placeholder:(i+1).toString(),defaultValue:""})),void 0!==r.lastPage?" / ":"",void 0!==r.lastPage?o().createElement("div",{className:"last-page",style:{minWidth:l}},r.lastPage+1):""),o().createElement("button",{className:"next-page",onClick:r.loadNext,disabled:!r.hasNext()},o().createElement(lu,{icon:"angle-right"})),o().createElement("button",{className:"last-page",onClick:r.loadLast,disabled:void 0===r.lastPage||r.isLast()},o().createElement(lu,{icon:"angle-double-right"})))}}],n&&up(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),l}(r.Component);function bp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Ap(e){return function(e){if(Array.isArray(e))return gp(e)}(e)||function(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}(e)||yp(e)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}function yp(e,t){if(e){if("string"==typeof e)return gp(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?gp(e,t):void 0}}function gp(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function mp(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,xp(r.key),r)}}function wp(e,t){return wp=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},wp(e,t)}function Ep(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Cp(e){return Cp=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Cp(e)}function kp(e,t,n){return(t=xp(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function xp(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var Op={width:"100%"},Sp={minHeight:"100%",minWidth:"100%"},Bp=function(e){return'[data-dash-column="'.concat(CSS.escape(e),'"]:not(.phantom-cell)')},_p=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&wp(e,t)}(u,e);var t,n,r,s,c=(r=u,s=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Cp(r);if(s){var n=Cp(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return Ep(e)}(this,e)});function u(e){var t;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,u),kp(Ep(t=c.call(this,e)),"menuRef",o().createRef()),kp(Ep(t),"stylesheet",new h("#".concat(CSS.escape(t.props.id)))),kp(Ep(t),"tableFn",Fu((function(){return t.props}))),kp(Ep(t),"tableFragments",qu()),kp(Ep(t),"tableStyle",il()),kp(Ep(t),"labelsAndIndices",We()),kp(Ep(t),"calculateTableStyle",(0,a.qe)((function(e){return i.Jnq(t.tableStyle(Op,e))}))),kp(Ep(t),"getLexerResult",(0,a.qe)(G.bind(void 0,va))),kp(Ep(t),"handleClick",(function(e){t.containsActiveElement()&&t.props.is_focused&&t.props.setProps({is_focused:!1});var n=t.menuRef;t.props.activeMenu&&n&&n.current&&!n.current.contains(e.target)&&t.props.setState({activeMenu:void 0})})),kp(Ep(t),"handleClipboardEvent",(function(e,n){t.containsActiveElement()&&n(e)})),kp(Ep(t),"handleCopy",(function(e){t.handleClipboardEvent(e,t.onCopy)})),kp(Ep(t),"handlePaste",(function(e){t.handleClipboardEvent(e,t.onPaste)})),kp(Ep(t),"resetFragmentCells",(function(e){var n=e.querySelectorAll("table.cell-table > tbody > tr:last-of-type > *");n.length&&(Array.from(n).forEach(t.clearCellWidth),Array.from(e.querySelectorAll("table.cell-table > tbody > tr > th:first-of-type")).map((function(e){return e.parentElement})).forEach((function(e){var n=Array.from(null==e?void 0:e.children);n&&n.forEach(t.clearCellWidth)})))})),kp(Ep(t),"resizeFragmentCells",(function(e,n){var r=e.querySelectorAll("table.cell-table > tbody > tr:last-of-type > *");r.length&&(Array.from(r).forEach((function(e,r){return t.setCellWidth(e,n[r])})),Array.from(e.querySelectorAll("table.cell-table > tbody > tr > th:first-of-type")).map((function(e){return e.parentElement})).forEach((function(e){var r=Array.from(null==e?void 0:e.children);r&&(r.length===n.length?r.forEach((function(e,r){return t.setCellWidth(e,n[r])})):r.forEach((function(e){return t.setCellWidth(e,0)})))})))})),kp(Ep(t),"resizeFragmentTable",(function(e,t){e&&(e.style.width=t)})),kp(Ep(t),"isDisplayed",(function(e){return"none"!==getComputedStyle(e).display})),kp(Ep(t),"forceHandleResize",(function(){return t.handleResize()})),kp(Ep(t),"getScrollbarWidthOnce",i.IHq(Z)),kp(Ep(t),"handleResizeIf",(0,a.qe)((function(){var e=t.refs,n=e.r0c0,r=e.r0c1,o=e.r1c0,i=e.r1c1;t.isDisplayed(i)&&(r.style.marginLeft="",i.style.marginLeft="",n.style.width="",o.style.width="",[n,r,o].forEach((function(e){var n=e.querySelector("table");n&&(n.style.width=""),t.resetFragmentCells(e)})),t.handleResize())}))),kp(Ep(t),"handleResize",(function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:NaN,n=arguments.length>1&&void 0!==arguments[1]&&arguments[1],r=t.props,o=r.fixed_columns,i=r.fixed_rows,a=r.setState,l=t.refs,s=l.r1,c=l.r1c1;if(t.isDisplayed(c)){t.getScrollbarWidthOnce(s).then((function(e){return a({scrollbarWidth:e})}));var u=t.refs,p=u.r0c0,d=u.r0c1,f=u.r1c0,h=p.querySelector("table"),v=d.querySelector("table"),b=f.querySelector("table"),A=c.querySelector("table"),y=getComputedStyle(A).width;if(n||(t.resizeFragmentTable(h,y),t.resizeFragmentTable(v,y),t.resizeFragmentTable(b,y)),o||i){var g=Array.from(c.querySelectorAll("table.cell-table > tbody > tr:first-of-type > *")).map((function(e){return e.getBoundingClientRect().width}));n||(t.resizeFragmentCells(p,g),t.resizeFragmentCells(d,g),t.resizeFragmentCells(f,g))}if(o){var m=c.querySelector("tr:first-of-type > *:nth-of-type(".concat(o,")"));if(m){var w=m.getBoundingClientRect().right-c.getBoundingClientRect().left;p.style.width="".concat(w,"px"),f.style.width="".concat(w,"px")}}var E=c.querySelector("tr:first-of-type > *:nth-of-type(".concat(o+1,")"));if(E){var C=c.getBoundingClientRect(),k=E.getBoundingClientRect().left-C.left;d.style.marginLeft="-".concat(k+s.scrollLeft,"px"),c.style.marginLeft="-".concat(k,"px")}if(!n){var x=parseInt(y,10),O=parseInt(getComputedStyle(A).width,10);O!==x&&t.handleResize(x,O===e)}}})),kp(Ep(t),"handleKeyDown",(function(e){var n=t.props,r=n.setProps,o=n.is_focused;if(l.ZP.trace("handleKeyDown: ".concat(e.key)),i=e.keyCode,![v.CONTROL,v.COMMAND_LEFT,v.COMMAND_RIGHT,v.COMMAND_FIREFOX].includes(i)){var i,a=function(e){return(e.ctrlKey||e.metaKey)&&!e.altKey}(e);a&&e.keyCode===v.V||e.keyCode===v.C&&a&&!o||(e.keyCode!==v.ESCAPE?(!o&&y(e.keyCode)&&t.switchCell(e),o&&!y(e.keyCode)||(e.keyCode!==v.TAB&&e.keyCode!==v.ENTER?e.keyCode!==v.BACKSPACE&&e.keyCode!==v.DELETE||t.deleteCell(e):t.switchCell(e))):r({is_focused:!1}))}})),kp(Ep(t),"switchCell",(function(e){var n=e,r=t.props,o=r.active_cell,i=r.selected_cells,a=r.start_cell,s=r.end_cell,c=r.setProps,u=r.viewport,p=r.visibleColumns;if(e.preventDefault(),o){t.$el.focus();var d=i.length>1,f=n.keyCode===v.ENTER||n.keyCode===v.TAB;if(d&&f)c({is_focused:!1,active_cell:t.getNextCell(n,{currentCell:o,restrictToSelection:!0})});else if(n.shiftKey){var h=V(i),b=h.minRow,A=h.minCol,y=h.maxRow,g=h.maxCol,m=n.keyCode===v.ARROW_DOWN||n.keyCode===v.ENTER,w=n.keyCode===v.ARROW_UP,E=n.keyCode===v.ARROW_RIGHT||n.keyCode===v.TAB,C=n.keyCode===v.ARROW_LEFT,k=a&&a.row,x=a&&a.column,O=s&&s.row,S=s&&s.column;if(m)o.row>b?O=++b:y<u.data.length-1&&(O=++y);else if(w)o.row<y?O=--y:b>0&&(O=--b);else if(E)o.column>A?S=++A:g<p.length-1&&(S=++g);else{if(!C)return;o.column<g?S=--g:A>0&&(S=--A)}var B=K({minRow:b,maxRow:y,minCol:A,maxCol:g},p,u),_={is_focused:!1,end_cell:Y(O,S,p,u),selected_cells:B},j=O===b?y:b,P=S===A?g:A;k===j&&x===P||(_.start_cell=Y(j,P,p,u)),c(_)}else{var D=t.getNextCell(n,{currentCell:o,restrictToSelection:!1});c({is_focused:!1,selected_cells:[D],active_cell:D,start_cell:D,end_cell:D})}}else l.ZP.warning("Trying to change cell, but no cell is active.")})),kp(Ep(t),"deleteCell",(function(e){var n=t.props,r=n.data,o=n.selected_cells,a=n.setProps,l=n.viewport,s=n.visibleColumns;e.preventDefault();var c=r;i.UID((function(e){return[l.indices[e.row],e.column]}),o).forEach((function(e){var t=s[e[1]];if(t.editable){var n=Pe(null,t);c=i.t8m(i.QMA([e[0],t.id]),n.success?n.value:"",c)}})),a({data:c})})),kp(Ep(t),"getNextCell",(function(e,n){var r,o=n.restrictToSelection,a=n.currentCell,l=t.props,s=l.selected_cells,c=l.viewport,u=l.visibleColumns,p=e,d=a.row,f=a.column;switch(p.keyCode){case v.ARROW_LEFT:r=o?H([d,f-1],s):[d,i.Fp7(0,f-1)];break;case v.ARROW_RIGHT:case v.TAB:r=o?H([d,f+1],s):[d,i.VV$(u.length-1,f+1)];break;case v.ARROW_UP:r=o?H([d-1,f],s):[i.Fp7(0,d-1),f];break;case v.ARROW_DOWN:case v.ENTER:r=o?H([d+1,f],s):[i.VV$(c.data.length-1,d+1),f];break;default:throw new Error("Table.getNextCell: unknown navigation keycode ".concat(p.keyCode))}return Y(r[0],r[1],u,c)})),kp(Ep(t),"onCopy",(function(e){var n=t.props,r=n.selected_cells,o=n.viewport,i=n.columns,a=n.visibleColumns,l=n.include_headers_on_copy_paste;r.length&&Le.toClipboard(e,r,i,a,o.data,l),t.$el.focus()})),kp(Ep(t),"onPaste",(function(e){var n=t.props,r=n.active_cell,o=n.columns,i=n.data,a=n.editable,l=n.filter_query,s=n.loading_state,c=n.setProps,u=n.sort_by,p=n.viewport,d=n.visibleColumns,f=n.include_headers_on_copy_paste;if(a&&r&&!s){var h=Le.fromClipboard(e,r,p.indices,o,d,i,!0,!u.length||!l.length,f);h&&c(h)}})),kp(Ep(t),"handleDropdown",(function(){var e=t.refs.r1c1;qe(e.querySelector(".Select-menu-outer"))})),kp(Ep(t),"onScroll",(function(e){var n=t.refs,r=n.r0c0,o=n.r0c1;l.ZP.trace("ControlledTable fragment scrolled to (left,top)=(".concat(e.target.scrollLeft,",").concat(e.target.scrollTop,")"));var i=parseFloat(e.target.scrollLeft)+(parseFloat(r.style.width)||0);o.style.marginLeft="".concat(-i,"px"),t.updateUiViewport(),t.handleDropdown(),t.adjustTooltipPosition()})),kp(Ep(t),"toggleColumn",(function(e,n,r){var o=t.props,i=o.columns,a=o.hidden_columns,l=o.setProps,s=S(e,i,n,r),c=a?a.slice(0):[];s.forEach((function(e){var t=c.indexOf(e);t>=0?c.splice(t,1):c.push(e)})),l({hidden_columns:c})})),t.updateStylesheet(),t}return t=u,n=[{key:"lexerResult",get:function(){var e=this.props.filter_query;return this.getLexerResult(e)}},{key:"updateStylesheet",value:function(){var e=this,t=this.props.css;i.Ed_((function(t){var n=t.selector,r=t.rule;e.stylesheet.setRule(n,r)}),t)}},{key:"updateUiViewport",value:function(){var e=this.props,t=e.setState,n=e.uiViewport;if(e.virtualization){var r=this.refs.r1c1.parentElement;n&&n.scrollLeft===r.scrollLeft&&n.scrollTop===r.scrollTop&&n.height===r.clientHeight&&n.width===r.clientWidth||t({uiViewport:{scrollLeft:r.scrollLeft,scrollTop:r.scrollTop,height:r.clientHeight,width:r.clientWidth}})}}},{key:"componentDidMount",value:function(){window.addEventListener("resize",this.forceHandleResize),document.addEventListener("mousedown",this.handleClick),document.addEventListener("paste",this.handlePaste),document.addEventListener("copy",this.handleCopy);var e=this.props,t=e.active_cell,n=e.selected_cells,r=e.setProps;n.length&&t&&!i.q9t(t,n)&&r({active_cell:n[0]}),this.updateUiViewport(),this.handleResize()}},{key:"componentWillUnmount",value:function(){window.removeEventListener("resize",this.forceHandleResize),document.removeEventListener("mousedown",this.handleClick),document.removeEventListener("paste",this.handlePaste),document.removeEventListener("copy",this.handleCopy)}},{key:"componentDidUpdate",value:function(){this.updateStylesheet(),this.updateUiViewport();var e=this.props,t=e.fixed_columns,n=e.fixed_rows;(t||n)&&this.handleResizeIf.apply(this,Ap(i.VO0(this.props))),this.handleDropdown(),this.adjustTooltipPosition();var r=this.props.active_cell;if(this.containsActiveElement()){var o=this.getActiveCellAttributes();if(o&&r&&(o.column_id!==(null==r?void 0:r.column_id)||o.row!==(null==r?void 0:r.row))){var a=r.column_id,l=r.row,s=this.$el.querySelector('td[data-dash-row="'.concat(l,'"]').concat(Bp(a)));s&&s.focus()}}var c=this.props,u=c.setState,p=c.uiCell;if(c.virtualization&&!p){var d=this.refs.r1c1,f=d.querySelector("tr > td:first-of-type");if(f){var h=d.querySelectorAll("tr th:first-of-type");u({uiCell:{height:f.clientHeight},uiHeaders:i.UID((function(e){return{height:e.clientHeight}}),Array.from(h))})}}}},{key:"clearCellWidth",value:function(e){e.style.width="",e.style.minWidth="",e.style.maxWidth="",e.style.boxSizing=""}},{key:"$el",get:function(){return document.getElementById(this.props.id)}},{key:"containsActiveElement",value:function(){var e=this.$el;return e&&e.contains(document.activeElement)}},{key:"getActiveCellAttributes",value:function(){for(var e=document.activeElement;e&&"td"!==e.nodeName.toLowerCase();)e=e.parentElement;if(e){var t=e.getAttribute("data-dash-column"),n=e.getAttribute("data-dash-row");return{column_id:t,row:+(null!=n?n:0)}}}},{key:"displayPagination",get:function(){var e=this.props,t=e.data,n=e.page_action,r=e.page_size;return n===_.p9.Native&&r<t.length||n===_.p9.Custom}},{key:"render",value:function(){var e=this,t=this.props,n=t.columns,r=t.id,a=t.tooltip_conditional,l=t.tooltip,s=t.currentTooltip,c=t.fill_width,u=t.filter_action,p=t.fixed_columns,d=t.fixed_rows,f=t.loading_state,h=t.scrollbarWidth,v=t.style_as_list_view,b=t.style_table,A=t.tooltip_data,y=t.tooltip_delay,g=t.tooltip_duration,m=t.tooltip_header,w=t.uiCell,E=t.uiHeaders,C=t.uiViewport,k=t.viewport,x=t.virtualized,O=t.virtualization,S=t.visibleColumns,B=[[d&&p?"dash-fixed-row dash-fixed-column":"",d?"dash-fixed-row":""],[p?"dash-fixed-column":"","dash-fixed-content"]],j=this.tableFn(),P=this.tableFragments(p,d,j,x.offset.rows),D=P.grid,T=P.empty,I=["dash-spreadsheet"].concat(Ap(O?["dash-virtualized"]:[]),Ap(d?["dash-freeze-top"]:[]),Ap(p?["dash-freeze-left"]:[]),Ap(v?["dash-list-view"]:[]),Ap(T[0][1]?["dash-empty-01"]:[]),Ap(T[1][1]?["dash-empty-11"]:[]),Ap(S.length?[]:["dash-no-columns"]),Ap(x.data.length?[]:["dash-no-data"]),Ap(u.type!==_.p9.None?[]:["dash-no-filter"]),Ap(c?["dash-fill-width"]:[]),Ap(f?["dash-loading"]:[])),F=["dash-spreadsheet-container"].concat(Ap(I)),M=["dash-spreadsheet-inner"].concat(Ap(I)),R=this.calculateTableStyle(b),z=function(e,t,n,r,o,a,l){var s=[{},{fragment:{marginRight:l}}];if(!e||!t||!r)return[s,[{},{}]];var c=t.height*o.data.length,u=(Math.floor(r.scrollTop/t.height)-a.before)*t.height,p=i.Smz(i.UID((function(e){return e.height}),n||[])),d=e&&r&&t?Math.max(u-p,0):0;return[s,[{cell:{marginTop:d}},{fragment:{height:Math.max(c-d,0),marginTop:d}}]]}(O,w,E,C,k,x.padding.rows,h),N=Ku(s,A,m,a,l,x,y,g),L=this.props,q=L.export_columns,W=L.export_format,V=L.export_headers,H=L.virtual,Y=L.merge_duplicate_headers,K=L.paginator,Z=L.page_current,Q=L.page_count,$={export_columns:q,export_format:W,virtual_data:H,columns:n,visibleColumns:S,export_headers:V,merge_duplicate_headers:Y};return o().createElement("div",{id:r,className:"dash-table-container",onKeyDown:this.handleKeyDown,onPaste:this.onPaste,style:{position:"relative"}},o().createElement(cp,{key:"tooltip",ref:"tooltip",className:"dash-table-tooltip",tooltip:N}),o().createElement("div",{className:"dash-spreadsheet-menu"},this.renderMenu(),o().createElement(U,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?bp(Object(n),!0).forEach((function(t){kp(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):bp(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},$))),o().createElement("div",{className:F.join(" "),style:R},o().createElement("div",{ref:"table",className:M.join(" "),style:Sp},D.map((function(t,n){return o().createElement("div",{key:"r".concat(n),ref:"r".concat(n),className:"dt-table-container__row dt-table-container__row-".concat(n),onScroll:e.onScroll},function(e,t,r,i){for(var a=e.length,l=new Array(a),s=0;s<a;++s)l[s]=(c=e[s],u=t[s],p=r[s],d=s,o().createElement("div",{style:u.fragment,key:d,ref:"r".concat(n,"c").concat(d),className:"cell cell-".concat(n,"-").concat(d," ").concat(p)},c?o().cloneElement(c,{style:u.cell}):c));var c,u,p,d;return l}(t,z[n],B[n]))})))),this.displayPagination?o().createElement(vp,{paginator:K,page_current:Z,page_count:Q}):null)}},{key:"renderMenu",value:function(){var e=this;if(!this.showToggleColumns)return null;var t=this.props,n=t.activeMenu,r=t.columns,a=t.hidden_columns,l=t.merge_duplicate_headers,s=t.setState,c=this.labelsAndIndices(r,r,l),u=c.length-1;return o().createElement("div",{className:"dash-spreadsheet-menu-item",ref:this.menuRef},o().createElement("button",{className:"show-hide",onClick:function(){return s({activeMenu:"show/hide"===n?void 0:"show/hide"})}},"Toggle Columns"),"show/hide"!==n?null:o().createElement("div",{className:"show-hide-menu"},i.UWY(c.map((function(t,n){var i=function(e,t){return function(e){if(Array.isArray(e))return e}(e)||function(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,o,i,a,l=[],s=!0,c=!1;try{if(i=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;s=!1}else for(;!(s=(r=i.call(n)).done)&&(l.push(r.value),l.length!==t);s=!0);}catch(e){c=!0,o=e}finally{try{if(!s&&null!=n.return&&(a=n.return(),Object(a)!==a))return}finally{if(c)throw o}}return l}}(e,t)||yp(e,t)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}()}(t,2)[1];return i.map((function(t,s){var c=1===i.length,p=r[t],d=!a||a.indexOf(p.id)<0,f=Ue(n,u,p.hideable),h=c&&d||!f&&d;return{i:t,j:s,component:f?o().createElement("div",{className:"show-hide-menu-item"},o().createElement("input",{type:"checkbox",checked:d,disabled:h,onClick:e.toggleColumn.bind(e,p,n,l)}),o().createElement("label",null,p.name?"string"==typeof p.name?p.name:p.name.slice(0,n+1).filter((function(e){return 0!==e.length})).join(" | "):p.id)):null}}))}))).filter((function(e){return!i.kKJ(e)})).sort((function(e,t){return e.i-t.i||e.j-t.j})).map((function(e){return e.component}))))}},{key:"adjustTooltipPosition",value:function(){var e=this.props.currentTooltip;if(e){var t=e.id,n=e.row,r=e.header,o=this.refs,i=o.table;if(o.tooltip){var a=i.querySelector(r?"tr:nth-of-type(".concat(n+1,") th").concat(Bp(t)):'td[data-dash-row="'.concat(n,'"]').concat(Bp(t)));this.refs.tooltip.updateBounds(a)}}}},{key:"setCellWidth",value:function(e,t){"number"==typeof t&&(t="".concat(t,"px")),e.style.width=t,e.style.minWidth=t,e.style.maxWidth=t,e.style.boxSizing="border-box"}},{key:"showToggleColumns",get:function(){var e=this.props,t=e.columns,n=e.hidden_columns;return n&&n.length>0||i.YjB((function(e){return!!e.hideable}),t)}}],n&&mp(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),u}(r.PureComponent),jp=n(3379),Pp=n.n(jp),Dp=n(3380),Tp=n.n(Dp),Ip=n(569),Fp=n.n(Ip),Mp=n(3565),Rp=n.n(Mp),zp=n(9216),Np=n.n(zp),Lp=n(4589),qp=n.n(Lp),Up=n(7288),Wp={};Wp.styleTagTransform=qp(),Wp.setAttributes=Rp(),Wp.insert=Fp().bind(null,"head"),Wp.domAPI=Tp(),Wp.insertStyleElement=Np(),Pp()(Up.Z,Wp),Up.Z&&Up.Z.locals&&Up.Z.locals;var Vp=n(8220),Hp={};Hp.styleTagTransform=qp(),Hp.setAttributes=Rp(),Hp.insert=Fp().bind(null,"head"),Hp.domAPI=Tp(),Hp.insertStyleElement=Np(),Pp()(Vp.Z,Hp),Vp.Z&&Vp.Z.locals&&Vp.Z.locals;Vc.add({prefix:"fas",iconName:"eraser",icon:[512,512,[],"f12d","M497.941 273.941c18.745-18.745 18.745-49.137 0-67.882l-160-160c-18.745-18.745-49.136-18.746-67.883 0l-256 256c-18.745 18.745-18.745 49.137 0 67.882l96 96A48.004 48.004 0 0 0 144 480h356c6.627 0 12-5.373 12-12v-40c0-6.627-5.373-12-12-12H355.883l142.058-142.059zm-302.627-62.627l137.373 137.373L265.373 416H150.628l-80-80 124.686-124.686z"]},{prefix:"far",iconName:"eye-slash",icon:[640,512,[],"f070","M634 471L36 3.51A16 16 0 0 0 13.51 6l-10 12.49A16 16 0 0 0 6 41l598 467.49a16 16 0 0 0 22.49-2.49l10-12.49A16 16 0 0 0 634 471zM296.79 146.47l134.79 105.38C429.36 191.91 380.48 144 320 144a112.26 112.26 0 0 0-23.21 2.47zm46.42 219.07L208.42 260.16C210.65 320.09 259.53 368 320 368a113 113 0 0 0 23.21-2.46zM320 112c98.65 0 189.09 55 237.93 144a285.53 285.53 0 0 1-44 60.2l37.74 29.5a333.7 333.7 0 0 0 52.9-75.11 32.35 32.35 0 0 0 0-29.19C550.29 135.59 442.93 64 320 64c-36.7 0-71.71 7-104.63 18.81l46.41 36.29c18.94-4.3 38.34-7.1 58.22-7.1zm0 288c-98.65 0-189.08-55-237.93-144a285.47 285.47 0 0 1 44.05-60.19l-37.74-29.5a333.6 333.6 0 0 0-52.89 75.1 32.35 32.35 0 0 0 0 29.19C89.72 376.41 197.08 448 320 448c36.7 0 71.71-7.05 104.63-18.81l-46.41-36.28C359.28 397.2 339.89 400 320 400z"]},{prefix:"fas",iconName:"pencil-alt",icon:[512,512,[],"f303","M497.9 142.1l-46.1 46.1c-4.7 4.7-12.3 4.7-17 0l-111-111c-4.7-4.7-4.7-12.3 0-17l46.1-46.1c18.7-18.7 49.1-18.7 67.9 0l60.1 60.1c18.8 18.7 18.8 49.1 0 67.9zM284.2 99.8L21.6 362.4.4 483.9c-2.9 16.4 11.4 30.6 27.8 27.8l121.5-21.3 262.6-262.6c4.7-4.7 4.7-12.3 0-17l-111-111c-4.8-4.7-12.4-4.7-17.1 0zM124.1 339.9c-5.5-5.5-5.5-14.3 0-19.8l154-154c5.5-5.5 14.3-5.5 19.8 0s5.5 14.3 0 19.8l-154 154c-5.5 5.5-14.3 5.5-19.8 0zM88 424h48v36.3l-64.5 11.3-31.1-31.1L51.7 376H88v48z"]},{prefix:"fas",iconName:"sort",icon:[320,512,[],"f0dc","M41 288h238c21.4 0 32.1 25.9 17 41L177 448c-9.4 9.4-24.6 9.4-33.9 0L24 329c-15.1-15.1-4.4-41 17-41zm255-105L177 64c-9.4-9.4-24.6-9.4-33.9 0L24 183c-15.1 15.1-4.4 41 17 41h238c21.4 0 32.1-25.9 17-41z"]},{prefix:"fas",iconName:"sort-down",icon:[320,512,[],"f0dd","M41 288h238c21.4 0 32.1 25.9 17 41L177 448c-9.4 9.4-24.6 9.4-33.9 0L24 329c-15.1-15.1-4.4-41 17-41z"]},{prefix:"fas",iconName:"sort-up",icon:[320,512,[],"f0de","M279 224H41c-21.4 0-32.1-25.9-17-41L143 64c9.4-9.4 24.6-9.4 33.9 0l119 119c15.2 15.1 4.5 41-16.9 41z"]},{prefix:"far",iconName:"trash-alt",icon:[448,512,[],"f2ed","M268 416h24a12 12 0 0 0 12-12V188a12 12 0 0 0-12-12h-24a12 12 0 0 0-12 12v216a12 12 0 0 0 12 12zM432 80h-82.41l-34-56.7A48 48 0 0 0 274.41 0H173.59a48 48 0 0 0-41.16 23.3L98.41 80H16A16 16 0 0 0 0 96v16a16 16 0 0 0 16 16h16v336a48 48 0 0 0 48 48h288a48 48 0 0 0 48-48V128h16a16 16 0 0 0 16-16V96a16 16 0 0 0-16-16zM171.84 50.91A6 6 0 0 1 177 48h94a6 6 0 0 1 5.15 2.91L293.61 80H154.39zM368 464H80V128h288zm-212-48h24a12 12 0 0 0 12-12V188a12 12 0 0 0-12-12h-24a12 12 0 0 0-12 12v216a12 12 0 0 0 12 12z"]},{prefix:"fas",iconName:"angle-left",icon:[256,512,[],"f104","M31.7 239l136-136c9.4-9.4 24.6-9.4 33.9 0l22.6 22.6c9.4 9.4 9.4 24.6 0 33.9L127.9 256l96.4 96.4c9.4 9.4 9.4 24.6 0 33.9L201.7 409c-9.4 9.4-24.6 9.4-33.9 0l-136-136c-9.5-9.4-9.5-24.6-.1-34z"]},{prefix:"fas",iconName:"angle-right",icon:[256,512,[],"f105","M224.3 273l-136 136c-9.4 9.4-24.6 9.4-33.9 0l-22.6-22.6c-9.4-9.4-9.4-24.6 0-33.9l96.4-96.4-96.4-96.4c-9.4-9.4-9.4-24.6 0-33.9L54.3 103c9.4-9.4 24.6-9.4 33.9 0l136 136c9.5 9.4 9.5 24.6.1 34z"]},{prefix:"fas",iconName:"angle-double-left",icon:[448,512,[],"f100","M223.7 239l136-136c9.4-9.4 24.6-9.4 33.9 0l22.6 22.6c9.4 9.4 9.4 24.6 0 33.9L319.9 256l96.4 96.4c9.4 9.4 9.4 24.6 0 33.9L393.7 409c-9.4 9.4-24.6 9.4-33.9 0l-136-136c-9.5-9.4-9.5-24.6-.1-34zm-192 34l136 136c9.4 9.4 24.6 9.4 33.9 0l22.6-22.6c9.4-9.4 9.4-24.6 0-33.9L127.9 256l96.4-96.4c9.4-9.4 9.4-24.6 0-33.9L201.7 103c-9.4-9.4-24.6-9.4-33.9 0l-136 136c-9.5 9.4-9.5 24.6-.1 34z"]},{prefix:"fas",iconName:"angle-double-right",icon:[448,512,[],"f101","M224.3 273l-136 136c-9.4 9.4-24.6 9.4-33.9 0l-22.6-22.6c-9.4-9.4-9.4-24.6 0-33.9l96.4-96.4-96.4-96.4c-9.4-9.4-9.4-24.6 0-33.9L54.3 103c9.4-9.4 24.6-9.4 33.9 0l136 136c9.5 9.4 9.5 24.6.1 34zm192-34l-136-136c-9.4-9.4-24.6-9.4-33.9 0l-22.6 22.6c-9.4 9.4-9.4 24.6 0 33.9l96.4 96.4-96.4 96.4c-9.4 9.4-9.4 24.6 0 33.9l22.6 22.6c9.4 9.4 24.6 9.4 33.9 0l136-136c9.4-9.2 9.4-24.4 0-33.8z"]});var Yp=n(366),Kp={};function Zp(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Qp(e,t){return Math.ceil(e.length/t)}Kp.styleTagTransform=qp(),Kp.setAttributes=Rp(),Kp.insert=Fp().bind(null,"head"),Kp.domAPI=Tp(),Kp.insertStyleElement=Np(),Pp()(Yp.Z,Kp),Yp.Z&&Yp.Z.locals&&Yp.Z.locals;var $p=(0,a.Pi)((function(e,t,n,r,o,i){return e===_.p9.Native&&(r=Qp(i,n)),r&&(r=Math.max(r,1)),function(e){if(null===e)return{loadNext(){},loadPrevious(){},loadFirst(){},loadLast(){},loadPage(){},hasPrevious(){return!0},hasNext(){return!0},isLast(){return!1},lastPage:void 0};var t=e.setProps,n=e.page_count,r=e.page_current;function o(){t(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Zp(Object(n),!0).forEach((function(t){var r,o,i;r=e,o=t,i=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Zp(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({page_current:r},B))}function i(e){e=Math.max(0,e),e=n?Math.min(n-1,e):e,r=e,o()}return n&&n-1<r&&(r=0,o()),{loadNext:function(){return i(r+1)},loadPrevious:function(){return i(r-1)},loadFirst:function(){return i(0)},loadPage:i,loadLast:function(){n&&i(n-1)},hasPrevious:function(){return 0!==r},hasNext:function(){return!n||r!==n-1},isLast:function(){return!!n&&r===n-1},lastPage:n?Math.max(0,n-1):void 0}}(e===_.p9.None?null:{setProps:o,page_current:t,page_count:r})})),Gp=(0,a.Pi)((function(e,t){return e.map((function(e){return e.id})).filter((function(e){return-1!==t.indexOf(e)}))})),Jp=(0,a.Pi)((function(e,t){var n=new Map;e.forEach((function(e,t){n.set(e,t)}));var r=[];return t.forEach((function(e){var t=n.get(e);void 0!==t&&r.push(t)})),r})),Xp=(0,a.Pi)((function(e,t,n,r,o){switch(e){case _.p9.None:return function(e,t){return{data:e,indices:t}}(r,o);case _.p9.Native:return function(e,t,n,r){var o=t*Math.min(e,Qp(n,t)),i=Math.min(o+t,n.length);return{data:n.slice(o,i),indices:r.slice(o,i)}}(t,n,r,o);case _.p9.Custom:return function(e,t){return{data:e,indices:t}}(r,o);default:throw new Error("Unknown pagination mode: '".concat(e,"'"))}})),ed=(0,a.Pi)((function(e,t,n,r,o){var a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:[],l=new Map;if(i.h0F(i.Ed_)((function(e,t){l.set(e,t)}),t),n.type===_.p9.Native){var s=new ya(r);t=s.isValid?s.filter(t):t}return o===_.p9.Native&&(t=function(e,t){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:ws;return t.length?i.HCG(i.UID((function(e){return e.direction===ys.Descending?i.Ukb((function(t,r){var o=e.column_id,i=t[o],a=r[o];return!n(i,e.column_id)&&(!!n(a,e.column_id)||i>a)})):i.Ukb((function(t,r){var o=e.column_id,i=t[o],a=r[o];return!n(i,e.column_id)&&(!!n(a,e.column_id)||i<a)}))}),t),e):e}(t,a,(function(t,n){return i.kKJ(t)||i.q9t(t,function(t){var n=i.sEJ((function(e){return e.id===t}),e);return n&&n.sort_as_null||[]}(n))}))),{data:t,indices:i.UID((function(e){return l.get(e)}),t)}}));function td(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function nd(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?td(Object(n),!0).forEach((function(t){var r,o,i;r=e,o=t,i=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):td(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var rd=(0,a.Pi)((function(e,t,n,r,o){if(!e)return nd(nd({},o),{},{offset:{rows:0,columns:0},padding:{rows:{before:0,after:0}}});if(!r||!t)return{data:o.data.slice(0,1),indices:o.indices.slice(0,1),offset:{rows:0,columns:0},padding:{rows:{before:0,after:0}}};var a=i.Smz(i.UID((function(e){return e.height}),n||[])),l=Math.max(r.scrollTop-a,0),s=Math.max(a-r.scrollTop,0),c=Math.floor(l/t.height),u=Math.ceil((r.height-s+l)/t.height),p=Math.min(c,1),d=Math.min(o.data.length-u,1);return c-=p,u+=d,{data:o.data.slice(c,u),indices:o.indices.slice(c,u),offset:{rows:c,columns:0},padding:{rows:{before:p,after:d}}}}));function od(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function id(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?od(Object(n),!0).forEach((function(t){var r,o,i;r=e,o=t,i=n[t],(o=function(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}(o))in r?Object.defineProperty(r,o,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[o]=i})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):od(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var ad=/^derived_/;function ld(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function sd(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,fd(r.key),r)}}function cd(e,t){return cd=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},cd(e,t)}function ud(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function pd(e){return pd=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},pd(e)}function dd(e,t,n){return(t=fd(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function fd(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var hd=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&cd(e,t)}(c,e);var t,n,r,l,s=(r=c,l=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=pd(r);if(l){var n=pd(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return ud(e)}(this,e)});function c(e){var t,n,r,o,l,u,p,d,f,h,v,b,A,y,g,m,w;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,c),dd(ud(t=s.call(this,e)),"__setProps",(0,a.qe)((function(e){return e?function(n){if(i.e$l("data",n)){var r=t.props.data;n.data_timestamp=Date.now(),n.data_previous=r}e(n)}:function(e){t.setState(e)}}))),dd(ud(t),"__setState",(0,a.qe)((function(){return function(e){return t.setState(e)}}))),dd(ud(t),"filterMap",ps()),dd(ud(t),"controlledPropsHelper",(n=$p(),r=Xp(),o=Gp(),l=Jp(),u=ed(),p=Jp(),d=rd(),function(e,t,a,s){var c=i.ATH(a,s),f=c.data,h=c.filter_query,v=c.filter_action,b=c.page_action,A=c.page_current,y=c.page_size,g=c.page_count,m=c.selected_columns,w=c.selected_rows,E=c.sort_action,C=c.sort_by,k=c.uiCell,x=c.uiHeaders,O=c.uiViewport,S=c.virtualization,B=c.visibleColumns,_=u(B,f,v,h,E,C),j=r(b,A,y,_.data,_.indices),P=d(S,k,x,O,j),D=p(_.indices,w),T=o(B,m),I=l(j.indices,w),F=n(b,A,y,g,e,_.data);return i.Jnq([a,s,{paginator:F,setProps:e,setState:t,viewport:j,viewport_selected_columns:T,viewport_selected_rows:I,virtual:_,virtual_selected_rows:D,virtualized:P}])})),dd(ud(t),"updateDerivedProps",(f=(0,a.yw)((function(e){return e})),h=(0,a.yw)((function(e,t){return[e,t]})),v=(0,a.yw)((function(e){return e})),b=(0,a.yw)((function(e){return e})),A=(0,a.yw)((function(e){return e})),y=(0,a.yw)((function(e){return e})),g=(0,a.yw)((function(e){return e})),m=(0,a.yw)((function(e){return e})),w=(0,a.yw)((function(e){return new ya(e).toStructure()})),function(e,t){var n=e.filter_query,r=e.filter_action,o=e.page_action,a=e.page_current,l=e.page_size,s=e.sort_action,c=e.sort_by,u=e.viewport,p=e.viewport_selected_columns,d=e.viewport_selected_rows,E=e.virtual,C=e.virtual_selected_rows,k=w(n),x=b(u).cached,O=g(E).cached,S=A(p).cached,B=y(d).cached,j=m(C).cached,P=f(n),D=h(a,l),T=v(c),I=!P.cached&&!P.first&&r.type===_.p9.Custom||!D.cached&&!D.first&&o===_.p9.Custom||!T.cached&&!T.first&&s===_.p9.Custom,F={};k.cached||(F.derived_filter_query_structure=k.result),O||(F.derived_virtual_data=E.data,F.derived_virtual_indices=E.indices,F.derived_virtual_row_ids=i.jge("id",E.data)),x||(F.derived_viewport_data=u.data,F.derived_viewport_indices=u.indices,F.derived_viewport_row_ids=i.jge("id",u.data)),j||(F.derived_virtual_selected_rows=C,F.derived_virtual_selected_row_ids=i.UID((function(e){return E.data[e].id}),C)),S||(F.derived_viewport_selected_columns=p),B||(F.derived_viewport_selected_rows=d,F.derived_viewport_selected_row_ids=i.UID((function(e){return u.data[e].id}),d)),I&&(F.active_cell=void 0,F.selected_cells=[],F.start_cell=void 0,F.end_cell=void 0,F.selected_rows=[],F.selected_row_ids=[]),i.XPQ(F).length&&setTimeout((function(){return t(F)}),0)})),t.state={workFilter:{value:e.filter_query,map:t.filterMap(new Map,e.filter_action.operator,e.filter_query,e.visibleColumns)},rawFilterQuery:"",scrollbarWidth:0},t}return t=c,n=[{key:"UNSAFE_componentWillReceiveProps",value:function(e){var t=this;this.setState((function(n){var r=n.applyFocus,o=n.workFilter,a=o.map,l=o.value,s={};if(e.filter_query!==t.props.filter_query&&l!==e.filter_query){var c=t.filterMap(a,e.filter_action.operator,e.filter_query,e.visibleColumns);c!==a&&(s.workFilter={map:c,value:l})}if(e.active_cell!==t.props.active_cell)s.applyFocus=!0;else if(e.loading_state!==t.props.loading_state){var u=document.activeElement,p=_n.getFirstParentOfType(u,"td"),d=_n.getParentById(p,t.props.id);s.applyFocus=!!d}return s.applyFocus===r&&delete s.applyFocus,i.p8H(s).length?s:null}))}},{key:"shouldComponentUpdate",value:function(e,t){return function(e,t,n,r){return i.YjB((function(n){return!ad.test(n)&&e[n]!==t[n]}),i.p8H(id(id({},e),t)))||!(0,Zu.X)(n,r)}(this.props,e,this.state,t)}},{key:"render",value:function(){var e=this.controlledPropsHelper(this.controlledSetProps,this.controlledSetState,this.props,this.state);return this.updateDerivedProps(e,this.controlledSetProps),o().createElement(_p,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ld(Object(n),!0).forEach((function(t){dd(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ld(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}({},e))}},{key:"controlledSetProps",get:function(){return this.__setProps(this.props.setProps)}},{key:"controlledSetState",get:function(){return this.__setState()}}],n&&sd(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),c}(r.Component);function vd(e){return e!==_.p9.Native}var bd=function(e){return function(e){var t=e.filter_action,n=e.sort_action;return e.page_action!==_.p9.Custom||vd(t)&&vd(n)}(e)?!!function(e){var t=e.columns;return i.kKJ(t)||!i.YjB((function(e){return e.format&&(e.format.symbol&&2!==e.format.symbol.length||e.format.grouping&&0===e.format.grouping.length||e.format.numerals&&10!==e.format.numerals.length)}))(t)}(e)||(l.ZP.error("Invalid column format"),!1):(l.ZP.error("Invalid combination of filter_action / sort_action / page_action"),!1)};function Ad(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,wd(r.key),r)}}function yd(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function gd(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?yd(Object(n),!0).forEach((function(t){md(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):yd(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function md(e,t,n){return(t=wd(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function wd(e){var t=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"==typeof t?t:String(t)}var Ed={symbol:["$",""],decimal:".",group:",",grouping:[3],percent:"%",separate_4digits:!0},Cd=[],kd={case:_.oN.Sensitive,placeholder_text:"filter data..."},xd=function(e){return+e||0},Od=function(e,t,n){return e.headers?w(t)+(n!==_.p9.None?1:0)+xd(e.data):0},Sd=function(e){return e.length>0?Object.keys(e[0]).map((function(e){return new _.sg({name:e,id:e})})):[]},Bd=function(e,t,n,r,o){return i.UID((function(n){var a,l=i.d9v(n);return l.editable=function(e,t){return i.kKJ(t)?e:t}(r,n.editable),l.filter_options=gd(gd(gd({},kd),null!=o?o:{}),null!==(a=l.filter_options)&&void 0!==a?a:{}),l.sort_as_null=l.sort_as_null||t,l.type===_.QD.Numeric&&l.format&&(l.format.locale=Td(e,l.format.locale),l.format.nully=Fd(l.format.nully),l.format.specifier=Id(l.format.specifier)),l}),n)},_d=function(e){return Td(e)},jd=function(e){var t,n;return"object"==typeof e?{type:null!==(t=e.type)&&void 0!==t?t:_.p9.None,operator:null!==(n=e.operator)&&void 0!==n?n:_.J2.And}:{type:e,operator:_.J2.And}},Pd=function(e,t){return i.hXT((function(e){return!t||t.indexOf(e.id)<0}),e)},Dd=function(){function e(){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),md(this,"populateColumnsFrom",(0,a.qe)(Sd)),md(this,"applyDefaultToLocale",(0,a.qe)(_d)),md(this,"applyDefaultsToColumns",(0,a.qe)(Bd)),md(this,"getFilterAction",(0,a.qe)(jd)),md(this,"getVisibleColumns",(0,a.qe)(Pd))}var t,n;return t=e,n=[{key:"sanitize",value:function(e){var t,n=this.applyDefaultToLocale(e.locale_format),r=null!==(t=e.data)&&void 0!==t?t:[],o=e.columns?this.applyDefaultsToColumns(n,e.sort_as_null,e.columns,e.editable,e.filter_options):this.populateColumnsFrom(r),a=this.getVisibleColumns(o,e.hidden_columns),l=e.export_headers;e.export_format===_.T2.Xlsx&&i.kKJ(l)?l=_.CR.Names:e.export_format===_.T2.Csv&&i.kKJ(l)&&(l=_.CR.Ids);var s,c,u,p,d=e.cell_selectable?e.active_cell:void 0,f=e.cell_selectable?e.selected_cells:Cd;return i.ATH(e,{active_cell:d,columns:o,data:r,export_headers:l,filter_action:this.getFilterAction(e.filter_action),fixed_columns:(c=e.fixed_columns,u=e.row_deletable,p=e.row_selectable,c.headers?(u?1:0)+(p?1:0)+xd(c.data):0),fixed_rows:Od(e.fixed_rows,o,e.filter_action),loading_state:(s=e.loading_state,!(!s||!s.is_loading||"data"!==s.prop_name&&""!==s.prop_name&&void 0!==s.prop_name)),locale_format:n,selected_cells:f,visibleColumns:a})}}],n&&Ad(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),e}(),Td=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return i.Jnq([Ed].concat(t))},Id=function(e){return void 0===e?"":e},Fd=function(e){return void 0===e?"":e},Md=n(8609);function Rd(){return Rd=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Rd.apply(this,arguments)}function zd(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,(void 0,"symbol"==typeof(o=function(e,t){if("object"!=typeof e||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(r.key))?o:String(o)),r)}var o}function Nd(e,t){return Nd=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e},Nd(e,t)}function Ld(e){return Ld=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(e){return e.__proto__||Object.getPrototypeOf(e)},Ld(e)}var qd=function(e){!function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&Nd(e,t)}(l,e);var t,n,r,i,a=(r=l,i=function(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(e){return!1}}(),function(){var e,t=Ld(r);if(i){var n=Ld(this).constructor;e=Reflect.construct(t,arguments,n)}else e=t.apply(this,arguments);return function(e,t){if(t&&("object"==typeof t||"function"==typeof t))return t;if(void 0!==t)throw new TypeError("Derived constructors may only return object or undefined");return function(e){if(void 0===e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}(e)}(this,e)});function l(e){var t,n;return function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,l),(t=a.call(this,e)).getId=function(){return n=n||function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:36;return e+Math.random().toString(t).substring(2)}("table-")},t.sanitizer=new Dd,t}return t=l,(n=[{key:"render",value:function(){if(!bd(this.props))return o().createElement("div",null,"Invalid props combination");var e=this.sanitizer.sanitize(this.props);return this.props.id?o().createElement(hd,e):o().createElement(hd,Rd({},e,{id:this.getId()}))}}])&&zd(t.prototype,n),Object.defineProperty(t,"prototype",{writable:!1}),l}(r.Component);qd.defaultProps=Md.lG,qd.propTypes=Md.iG},4184:function(e,t){var n;!function(){"use strict";var r={}.hasOwnProperty;function o(){for(var e=[],t=0;t<arguments.length;t++){var n=arguments[t];if(n){var i=typeof n;if("string"===i||"number"===i)e.push(n);else if(Array.isArray(n)){if(n.length){var a=o.apply(null,n);a&&e.push(a)}}else if("object"===i){if(n.toString!==Object.prototype.toString&&!n.toString.toString().includes("[native code]")){e.push(n.toString());continue}for(var l in n)r.call(n,l)&&n[l]&&e.push(l)}}}return e.join(" ")}e.exports?(o.default=o,e.exports=o):void 0===(n=function(){return o}.apply(t,[]))||(e.exports=n)}()},8220:function(e,t,n){"use strict";var r=n(7537),o=n.n(r),i=n(3645),a=n.n(i)()(o());a.push([e.id,".dash-spreadsheet.dash-freeze-left,\n.dash-spreadsheet.dash-freeze-top {\n  width: auto;\n  width: fit-content;\n  width: -moz-fit-content;\n  width: -webkit-fit-content;\n}\n.dash-spreadsheet.dash-freeze-left {\n  max-width: 500px;\n}\n.dash-spreadsheet.dash-freeze-top,\n.dash-spreadsheet.dash-virtualized {\n  max-height: 500px;\n}\n.dash-tooltip {\n  border: 1px solid #e4e4e4;\n  border-radius: 5px;\n  position: absolute;\n  z-index: 500;\n}\n.dash-tooltip .dash-table-tooltip {\n  position: relative;\n  background-color: #f6f6f6;\n  max-width: 300px;\n  min-width: 300px;\n  padding: 2px 10px;\n}\n.dash-tooltip[data-attr-anchor='top'] {\n  margin-top: 10px;\n}\n.dash-tooltip[data-attr-anchor='top']:after,\n.dash-tooltip[data-attr-anchor='top']:before {\n  bottom: 100%;\n  left: 50%;\n  border: solid transparent;\n  content: \" \";\n  height: 0;\n  width: 0;\n  position: absolute;\n  pointer-events: none;\n}\n.dash-tooltip[data-attr-anchor='top']:after {\n  border-color: transparent;\n  border-bottom-color: #f6f6f6;\n  border-width: 8px;\n  margin-left: -8px;\n}\n.dash-tooltip[data-attr-anchor='top']:before {\n  border-color: transparent;\n  border-bottom-color: #e4e4e4;\n  border-width: 9px;\n  margin-left: -9px;\n}\n.dash-tooltip[data-attr-anchor='bottom'] {\n  margin-bottom: 10px;\n}\n.dash-tooltip[data-attr-anchor='bottom']:after,\n.dash-tooltip[data-attr-anchor='bottom']:before {\n  top: 100%;\n  left: 50%;\n  border: solid transparent;\n  content: \" \";\n  height: 0;\n  width: 0;\n  position: absolute;\n  pointer-events: none;\n}\n.dash-tooltip[data-attr-anchor='bottom']:after {\n  border-color: transparent;\n  border-top-color: #f6f6f6;\n  border-width: 8px;\n  margin-left: -8px;\n}\n.dash-tooltip[data-attr-anchor='bottom']:before {\n  border-color: transparent;\n  border-top-color: #e4e4e4;\n  border-width: 9px;\n  margin-left: -9px;\n}\n.dash-spreadsheet-menu {\n  display: flex;\n  flex-direction: row;\n}\n.dash-spreadsheet-menu > * {\n  padding-right: 5px;\n}\n.dash-spreadsheet-menu .dash-spreadsheet-menu-item {\n  position: relative;\n}\n.dash-spreadsheet-menu .dash-spreadsheet-menu-item .show-hide-menu {\n  background-color: #fafafa;\n  border: 1px solid #d3d3d3;\n  display: flex;\n  flex-direction: column;\n  max-height: 300px;\n  overflow: auto;\n  position: absolute;\n  top: 100%;\n  left: 0;\n  z-index: 500;\n}\n.dash-spreadsheet-menu .dash-spreadsheet-menu-item .show-hide-menu .show-hide-menu-item {\n  display: flex;\n  flex-direction: row;\n  padding: 5px;\n}\n.dash-spreadsheet-menu .dash-spreadsheet-menu-item .show-hide-menu .show-hide-menu-item label {\n  white-space: nowrap;\n}\n.dash-table-container .previous-next-container {\n  text-align: right;\n  padding: 5px 0px;\n}\n.dash-table-container .previous-next-container .page-number {\n  font-family: monospace;\n  display: inline-block;\n}\n.dash-table-container .previous-next-container .page-number .last-page {\n  display: inline-block;\n  text-align: center;\n  padding: 1px 2px;\n}\n.dash-table-container .previous-next-container .page-number .current-page-container {\n  display: inline-block;\n  position: relative;\n}\n.dash-table-container .previous-next-container .page-number .current-page-container .current-page-shadow,\n.dash-table-container .previous-next-container .page-number .current-page-container input.current-page {\n  display: inline-block;\n  border-bottom: solid lightgrey 1px !important;\n  color: black;\n  border: none;\n  text-align: center;\n  font-family: monospace;\n  font-size: 10pt;\n  padding: 1px 2px;\n}\n.dash-table-container .previous-next-container .page-number .current-page-container .current-page-shadow::placeholder,\n.dash-table-container .previous-next-container .page-number .current-page-container input.current-page::placeholder {\n  color: black;\n}\n.dash-table-container .previous-next-container .page-number .current-page-container .current-page-shadow:focus,\n.dash-table-container .previous-next-container .page-number .current-page-container input.current-page:focus {\n  outline: none;\n}\n.dash-table-container .previous-next-container .page-number .current-page-container .current-page-shadow:focus::placeholder,\n.dash-table-container .previous-next-container .page-number .current-page-container input.current-page:focus::placeholder {\n  opacity: 0;\n}\n.dash-table-container .previous-next-container .page-number .current-page-container input.current-page {\n  position: absolute;\n  top: 0;\n  left: 0;\n  width: 100%;\n  height: 100%;\n}\n.dash-table-container .previous-next-container button.previous-page,\n.dash-table-container .previous-next-container button.next-page,\n.dash-table-container .previous-next-container button.first-page,\n.dash-table-container .previous-next-container button.last-page {\n  transition-duration: 400ms;\n  padding: 5px;\n  border: none;\n  display: inline-block;\n  margin-left: 5px;\n  margin-right: 5px;\n}\n.dash-table-container .previous-next-container button.previous-page:hover,\n.dash-table-container .previous-next-container button.next-page:hover,\n.dash-table-container .previous-next-container button.first-page:hover,\n.dash-table-container .previous-next-container button.last-page:hover {\n  color: hotpink;\n}\n.dash-table-container .previous-next-container button.previous-page:hover:disabled,\n.dash-table-container .previous-next-container button.next-page:hover:disabled,\n.dash-table-container .previous-next-container button.first-page:hover:disabled,\n.dash-table-container .previous-next-container button.last-page:hover:disabled {\n  color: graytext;\n}\n.dash-table-container .previous-next-container button.previous-page:focus,\n.dash-table-container .previous-next-container button.next-page:focus,\n.dash-table-container .previous-next-container button.first-page:focus,\n.dash-table-container .previous-next-container button.last-page:focus {\n  outline: none;\n}\n.dash-table-container .dash-spreadsheet-container {\n  /* The \"normal\" reset CSS */\n  /* The \"modified\" reset CSS applied to the table to ignore markdown cells */\n  display: flex;\n  flex-direction: row;\n  position: relative;\n  line-height: initial;\n  /* focus happens after copying to clipboard */\n}\n.dash-table-container .dash-spreadsheet-container th {\n  font-style: normal;\n  font-weight: normal;\n  text-align: left;\n}\n.dash-table-container .dash-spreadsheet-container th,\n.dash-table-container .dash-spreadsheet-container td {\n  margin: 0;\n  padding: 0;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown),\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) {\n  margin: 0;\n  padding: 0;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) dl,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) dl,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) dt,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) dt,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) dd,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) dd,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) ul,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) ul,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) ol,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) ol,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) li,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) li,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h1,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h1,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h2,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h2,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h3,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h3,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h4,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h4,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h5,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h5,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h6,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h6,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) pre,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) pre,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) code,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) code,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) form,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) form,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) fieldset,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) fieldset,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) legend,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) legend,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) input,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) input,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) textarea,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) textarea,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) p,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) p,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) blockquote,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) blockquote,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) th,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) th,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) td,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) td {\n  margin: 0;\n  padding: 0;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) table,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) table {\n  border-collapse: collapse;\n  border-spacing: 0;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) fieldset,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) fieldset,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) img,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) img {\n  border: 0;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) address,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) address,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) caption,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) caption,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) cite,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) cite,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) code,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) code,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) dfn,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) dfn,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) em,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) em,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) strong,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) strong,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) th,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) th,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) var,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) var {\n  font-style: normal;\n  font-weight: normal;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) ol,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) ol,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) ul,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) ul {\n  list-style: none;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) caption,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) caption,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) th,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) th {\n  text-align: left;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h1,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h1,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h2,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h2,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h3,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h3,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h4,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h4,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h5,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h5,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) h6,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) h6 {\n  font-size: 100%;\n  font-weight: normal;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) q:before,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) q:before,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) q:after,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) q:after {\n  content: '';\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) abbr,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) abbr,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) acronym,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) acronym {\n  border: 0;\n  font-variant: normal;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) sup,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) sup {\n  vertical-align: text-top;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) sub,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) sub {\n  vertical-align: text-bottom;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) input,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) input,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) textarea,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) textarea,\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) select,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) select {\n  font-family: inherit;\n  font-size: inherit;\n  font-weight: inherit;\n  *font-size: 100%;\n}\n.dash-table-container .dash-spreadsheet-container th > div:not(.cell-markdown) legend,\n.dash-table-container .dash-spreadsheet-container td > div:not(.cell-markdown) legend {\n  color: #000;\n}\n.dash-table-container .dash-spreadsheet-container input[type=\"button\"] {\n  border-radius: 0;\n  -webkit-appearance: none;\n}\n.dash-table-container .dash-spreadsheet-container *:focus {\n  outline: none;\n}\n.dash-table-container .dash-spreadsheet-container table {\n  font-size: inherit;\n  pointer-events: none;\n}\n.dash-table-container .dash-spreadsheet-container table td,\n.dash-table-container .dash-spreadsheet-container table th {\n  pointer-events: initial;\n}\n.dash-table-container .dash-spreadsheet-container input[type=\"radio\"] {\n  margin: initial;\n  line-height: initial;\n  box-sizing: initial;\n  padding: initial;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner {\n  box-sizing: border-box;\n  display: flex;\n  flex-direction: column;\n  /*\n             * fixes Firefox td height bug on td > dropdown children\n             * bug should only appear on FF but\n             * @supports = scoped to Firefox only\n             * to minimize side effects\n             */\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner *,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner *:after,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner *:before {\n  box-sizing: inherit;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .Select {\n  overflow: hidden;\n  position: static;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .Select,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .Select-control {\n  background-color: inherit;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .Select-value {\n  display: flex;\n  flex-direction: column;\n  justify-content: center;\n  margin-top: -2px;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .marker-row tr {\n  visibility: hidden !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .marker-row td,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .marker-row th {\n  height: 0 !important;\n  padding: 0 !important;\n  margin: 0 !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter input::placeholder {\n  color: inherit;\n  font-size: 0.8em;\n  padding-right: 5px;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter + .dash-filter:not(:hover):not(:focus-within) input::placeholder {\n  color: transparent;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter.invalid {\n  background-color: pink;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner:not(.dash-empty-11) .dt-table-container__row-0 tr:last-of-type td,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner:not(.dash-empty-11) .dt-table-container__row-0 tr:last-of-type th {\n  border-bottom: none !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner:not(.dash-empty-01) .cell-0-0 tr td:last-of-type,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner:not(.dash-empty-01) .cell-1-0 tr td:last-of-type,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner:not(.dash-empty-01) .cell-0-0 tr th:last-of-type,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner:not(.dash-empty-01) .cell-1-0 tr th:last-of-type {\n  border-right: none !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-0-0 {\n  overflow: hidden;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-0-0 td.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-1-0 td.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-0-0 th.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-1-0 th.phantom-cell {\n  border-color: transparent !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-0-1 td.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-1-1 td.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-0-1 th.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .cell-1-1 th.phantom-cell {\n  border-color: transparent inherit transparent transparent !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized {\n  overflow: hidden !important;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left .dt-table-container__row-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top .dt-table-container__row-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized .dt-table-container__row-0 {\n  display: flex;\n  flex: 0 0 auto;\n  flex-direction: row;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left .dt-table-container__row-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top .dt-table-container__row-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized .dt-table-container__row-1 {\n  display: flex;\n  flex-direction: row;\n  overflow: auto;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left .cell-0-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top .cell-0-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized .cell-0-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left .cell-1-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top .cell-1-0,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized .cell-1-0 {\n  flex: 0 0 auto;\n  left: 0;\n  position: sticky;\n  position: -webkit-sticky;\n  z-index: 400;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left .cell-0-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top .cell-0-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized .cell-0-1 {\n  z-index: 300;\n  flex: 0 0 auto;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-left .cell-1-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-freeze-top .cell-1-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-virtualized .cell-1-1 {\n  flex: 0 0 auto;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-fill-width .cell-0-1,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-fill-width .cell-1-1 {\n  flex: 1 0 auto;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner.dash-fill-width .cell table {\n  width: 100%;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td {\n  background-color: inherit;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td.focused {\n  margin: -1px;\n  z-index: 200;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .dash-cell-value-container {\n  width: 100%;\n  height: 100%;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .dash-input-cell-value-container {\n  position: relative;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .dash-cell-value {\n  height: 100%;\n  width: 100%;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .dash-cell-value.unfocused.selectable::selection {\n  background-color: transparent;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .dash-cell-value.unfocused {\n  caret-color: transparent;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td input.dash-cell-value {\n  position: absolute;\n  left: 0;\n  top: 0;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .cell-value-shadow {\n  margin: auto 0;\n  opacity: 0;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .input-cell-value-shadow {\n  display: inline-block;\n  height: initial;\n  width: initial;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td .dropdown-cell-value-shadow {\n  display: block;\n  height: 0px;\n  padding: 0 42px 0 10px;\n}\n@supports (-moz-appearance:none) {\n  .dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td.dropdown .dash-cell-value-container {\n    height: auto;\n  }\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th.dash-filter {\n  position: relative;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th.dash-filter input {\n  left: 0;\n  top: 0;\n  height: 100%;\n  width: 100%;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th.dash-filter input.dash-filter--case {\n  position: relative;\n  left: auto;\n  top: auto;\n  width: auto;\n  height: 16px;\n  line-height: 0px;\n  padding: 1px;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th.dash-filter input.dash-filter--case--sensitive {\n  border-color: hotpink;\n  border-radius: 3px;\n  border-style: solid;\n  border-width: 2px;\n  color: hotpink;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th {\n  white-space: nowrap;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th .column-header--clear,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th .column-header--delete,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th .column-header--edit,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th .column-header--hide,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th .column-header--sort {\n  -webkit-touch-callout: none;\n  -webkit-user-select: none;\n  -khtml-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  cursor: default;\n  cursor: pointer;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner tr {\n  min-height: 30px;\n  height: 30px;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th {\n  background-clip: padding-box;\n  padding: 2px;\n  overflow-x: hidden;\n  white-space: nowrap;\n  box-sizing: border-box;\n  text-align: right;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td.phantom-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th.phantom-cell {\n  visibility: hidden;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td div.dash-cell-value,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th div.dash-cell-value {\n  display: inline;\n  vertical-align: middle;\n  white-space: inherit;\n  overflow: inherit;\n  text-overflow: inherit;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td div.dash-cell-value.cell-markdown,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th div.dash-cell-value.cell-markdown {\n  text-align: left;\n  font-family: sans-serif;\n  display: inline-block;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td div.dash-cell-value.cell-markdown blockquote,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th div.dash-cell-value.cell-markdown blockquote {\n  white-space: pre;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner textarea {\n  white-space: pre;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner table {\n  border-collapse: collapse;\n  font-family: monospace;\n  --accent: hotpink;\n  --border: lightgrey;\n  --text-color: #3c3c3c;\n  --hover: #fdfdfd;\n  --background-color-ellipses: #fdfdfd;\n  --faded-text: #fafafa;\n  --faded-text-header: #b4b4b4;\n  --selected-background: rgba(255, 65, 54, 0.2);\n  --faded-dropdown: #f0f0f0;\n  --muted: #c8c8c8;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner table:focus {\n  outline: none;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner thead {\n  display: table-row-group;\n}\n.dash-table-container .dash-spreadsheet-container .elip {\n  text-align: center;\n  width: 100%;\n  background-color: var(--background-color-ellipses);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td.dropdown {\n  /*\n             * To view the dropdown's contents, we need\n             * overflow-y: visible.\n             * Unfortunately, overflow-x: hidden and overflow-y: visible\n             * can't both be set at the same time.\n             * So, we have to make both overflow-x: visible and overflow-y: visble\n             *\n             * See https://stackoverflow.com/questions/6421966/\n             *\n             * There might be another solution with parent divs, but I haven't\n             * tried it.\n             */\n  overflow-x: visible;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner :not(.cell--selected) tr:hover,\n.dash-table-container .dash-spreadsheet-container tr:hover input :not(.cell--selected) {\n  background-color: var(--hover);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th {\n  background-color: #fafafa;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner td {\n  background-color: white;\n}\n.dash-table-container .dash-spreadsheet-container .expanded-row--empty-cell {\n  background-color: transparent;\n}\n.dash-table-container .dash-spreadsheet-container .expanded-row {\n  text-align: center;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner input:not([type=radio]):not([type=checkbox]) {\n  padding: 0px;\n  margin: 0px;\n  height: calc(100% - 1px);\n  line-height: 30px;\n  border: none;\n  font-family: inherit;\n  text-align: right;\n  box-sizing: border-box;\n  color: var(--text-color);\n  background-color: transparent;\n  /* so as to not overlay the box shadow */\n  /* browser's default text-shadow is `$color 0px 0px 0px;`\n             * for `input`, which makes it look a little bit heavier than dropdowns\n             * or bare `td`\n             */\n  text-shadow: none;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner input.unfocused {\n  color: transparent;\n  text-shadow: 0 0 0 var(--text-color);\n  cursor: default;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner input.unfocused:focus {\n  outline: none;\n}\n.dash-table-container .dash-spreadsheet-container .toggle-row {\n  border: none;\n  box-shadow: none;\n  width: 10px;\n  padding-left: 10px;\n  padding-right: 10px;\n  cursor: pointer;\n  color: var(--faded-text);\n}\n.dash-table-container .dash-spreadsheet-container .toggle-row--expanded {\n  color: var(--accent);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner tr:hover .toggle-row {\n  color: var(--accent);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-delete-cell,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-delete-header {\n  -webkit-touch-callout: none;\n  -webkit-user-select: none;\n  -khtml-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  cursor: default;\n  font-size: 1.3rem;\n  text-align: center;\n  cursor: pointer;\n  color: var(--muted);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-delete-cell:hover,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-delete-header:hover {\n  color: var(--accent);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-header > div,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter > div {\n  display: flex;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-header > div input[type=\"text\"],\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter > div input[type=\"text\"] {\n  flex: 1;\n  line-height: unset;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-header > div input[type=\"text\"]::placeholder,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter > div input[type=\"text\"]::placeholder {\n  font-size: 0.9em;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter > div {\n  flex-direction: row-reverse;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-actions {\n  display: flex;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header-name {\n  flex-grow: 1;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner [class^='column-header--'],\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner [class^='dash-filter--'] {\n  cursor: pointer;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--select {\n  height: auto;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--select,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--sort {\n  color: var(--faded-text-header);\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter--case,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--clear,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--delete,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--edit,\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .column-header--hide {\n  opacity: 0.1;\n  padding-left: 2px;\n  padding-right: 2px;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th:hover [class^='column-header--']:not(.disabled),\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner th:hover [class^='dash-filter--']:not(.disabled) {\n  color: var(--accent);\n  opacity: 1;\n}\n.dash-table-container .dash-spreadsheet-container .dash-spreadsheet-inner .dash-filter--case {\n  font-size: 10px;\n}\n","",{version:3,sources:["webpack://./src/dash-table/components/Table/Table.less","webpack://./src/dash-table/style/reset.less"],names:[],mappings:"AAoBI;;EAjBA,WAAA;EACA,kBAAA;EACA,uBAAA;EACA,0BAAA;AAAJ;AAmBI;EACI,gBAAA;AAjBR;AAoBI;;EAEI,iBAAA;AAlBR;AAsBA;EACI,yBAAA;EACA,kBAAA;EACA,kBAAA;EACA,YAAA;AApBJ;AAgBA;EAOQ,kBAAA;EACA,yBAAA;EACA,gBAAA;EACA,gBAAA;EACA,iBAAA;AApBR;AAuBI;EACI,gBAAA;AArBR;AAuBQ;;EACI,YAAA;EACA,SAAA;EACA,yBAAA;EACA,YAAA;EACA,SAAA;EACA,QAAA;EACA,kBAAA;EACA,oBAAA;AApBZ;AAuBQ;EACI,yBAAA;EACA,4BAAA;EACA,iBAAA;EACA,iBAAA;AArBZ;AAwBQ;EACI,yBAAA;EACA,4BAAA;EACA,iBAAA;EACA,iBAAA;AAtBZ;AA0BI;EACI,mBAAA;AAxBR;AA0BQ;;EACI,SAAA;EACA,SAAA;EACA,yBAAA;EACA,YAAA;EACA,SAAA;EACA,QAAA;EACA,kBAAA;EACA,oBAAA;AAvBZ;AA0BQ;EACI,yBAAA;EACA,yBAAA;EACA,iBAAA;EACA,iBAAA;AAxBZ;AA0BQ;EACI,yBAAA;EACA,yBAAA;EACA,iBAAA;EACA,iBAAA;AAxBZ;AA6BA;EACI,aAAA;EACA,mBAAA;AA3BJ;AA6BI;EACI,kBAAA;AA3BR;AAsBA;EASQ,kBAAA;AA5BR;AAmBA;EAYY,yBAAA;EACA,yBAAA;EACA,aAAA;EACA,sBAAA;EACA,iBAAA;EACA,cAAA;EACA,kBAAA;EACA,SAAA;EACA,OAAA;EACA,YAAA;AA5BZ;AAOA;EAwBgB,aAAA;EACA,mBAAA;EACA,YAAA;AA5BhB;AAEA;EA6BoB,mBAAA;AA5BpB;AAmCA;EAEQ,iBAAA;EACA,gBAAA;AAlCR;AA+BA;EAMY,sBAAA;EACA,qBAAA;AAlCZ;AA2BA;EAUgB,qBAAA;EACA,kBAAA;EACA,gBAAA;AAlChB;AAsBA;EAgBgB,qBAAA;EACA,kBAAA;AAnChB;AAkBA;;EAqBoB,qBAAA;EACA,6CAAA;EACA,YAAA;EACA,YAAA;EACA,kBAAA;EACA,sBAAA;EACA,eAAA;EACA,gBAAA;AAnCpB;AAqCoB;;EACI,YAAA;AAlCxB;AAqCoB;;EACI,aAAA;AAlCxB;AAoCwB;;EACI,UAAA;AAjC5B;AALA;EA4CoB,kBAAA;EACA,MAAA;EACA,OAAA;EACA,WAAA;EACA,YAAA;AApCpB;AAZA;;;;EAsDY,0BAAA;EACA,YAAA;EACA,YAAA;EACA,qBAAA;EACA,gBAAA;EACA,iBAAA;AApCZ;AAsCY;;;;EACI,cAAA;AAjChB;AAmCgB;;;;EACI,eAAA;AA9BpB;AAkCY;;;;EACI,aAAA;AA7BhB;AAzCA;EA2CE,2BAA2B;EAC3B,2EAA2E;EAiCrE,aAAA;EACA,mBAAA;EACA,kBAAA;EAKA,oBAAA;EAnCN,6CAA6C;AAC/C;AAlDA;ECxIQ,kBAAA;EACA,mBAAA;EACA,gBAAA;AD6LR;AAvDA;;EClIQ,SAAA;EACA,UAAA;AD6LR;AC3LQ;;EACI,SAAA;EACA,UAAA;AD8LZ;AChMQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAIwG,SAAA;EAAS,UAAA;AD6OzH;ACjPQ;;EAKU,yBAAA;EAAyB,iBAAA;ADiP3C;ACtPQ;;;;EAMiB,SAAA;ADsPzB;AC5PQ;;;;;;;;;;;;;;;;;;EAOmD,kBAAA;EAAkB,mBAAA;AD0Q7E;ACjRQ;;;;EAQU,gBAAA;AD+QlB;ACvRQ;;;;EASe,gBAAA;ADoRvB;AC7RQ;;;;;;;;;;;;EAUsB,eAAA;EAAe,mBAAA;ADkS7C;AC5SQ;;;;EAWqB,WAAA;ADuS7B;AClTQ;;;;EAYiB,SAAA;EAAS,oBAAA;AD6SlC;ACzTQ;;EAaQ,wBAAA;ADgThB;AC7TQ;;EAcQ,2BAAA;ADmThB;ACjUQ;;;;;;EAe0B,oBAAA;EAAoB,kBAAA;EAAkB,oBAAA;GAAoB,eAAA;AD6T5F;AC5UQ;;EAgBW,WAAA;ADgUnB;AAjNA;ECxGQ,gBAAA;EACA,wBAAA;AD4TR;AArNA;ECnGQ,aAAA;AD2TR;AAxNA;EA0FY,kBAAA;EACA,oBAAA;AAiIZ;AA5NA;;EA8FgB,uBAAA;AAkIhB;AAhOA;EAsGY,eAAA;EACA,oBAAA;EAIA,mBAAA;EACA,gBAAA;AA0HZ;AAtOA;EAgHY,sBAAA;EACA,aAAA;EACA,sBAAA;EAyHV;;;;;cAKY;AACd;AAjPA;;;EAuHU,mBAAA;AA+HV;AAtPA;EA2HgB,gBAAA;EACA,gBAAA;AA8HhB;AA1PA;;EAiIU,yBAAA;AA6HV;AA9PA;EAqIgB,aAAA;EACA,sBAAA;EACA,uBAAA;EACA,gBAAA;AA4HhB;AApQA;EA6IoB,6BAAA;AA0HpB;AAvQA;;EAiJoB,oBAAA;EACA,qBAAA;EACA,oBAAA;AA0HpB;AA7QA;EAyJoB,cAAA;EACA,gBAAA;EACA,kBAAA;AAuHpB;AAnHoB;EAEQ,kBAAA;AAoH5B;AA/GgB;EACI,sBAAA;AAiHpB;AA7GY;;EAIgB,8BAAA;AA6G5B;AAlG4B;;;;EACI,6BAAA;AAuGhC;AAlSA;EAmMgB,gBAAA;AAkGhB;AA5FoB;;;;EACI,oCAAA;AAiGxB;AAzFoB;;;;EACI,oEAAA;AA8FxB;AAzFY;;;EAGI,2BAAA;AA2FhB;AA9FY;;;EAMQ,aAAA;EACA,cAAA;EACA,mBAAA;AA6FpB;AArGY;;;EAYQ,aAAA;EACA,mBAAA;EACA,cAAA;AA8FpB;AA5GY;;;;;;EAmBQ,cAAA;EACA,OAAA;EACA,gBAAA;EACA,wBAAA;EACA,YAAA;AAiGpB;AAxHY;;;EA2BQ,YAAA;EACA,cAAA;AAkGpB;AA9HY;;;EAgCQ,cAAA;AAmGpB;AA/FY;;EAGQ,cAAA;AAgGpB;AAnGY;EAQY,WAAA;AA8FxB;AAlWA;EA0QgB,yBAAA;AA2FhB;AAzFgB;EACI,YAAA;EACA,YAAA;AA2FpB;AAzWA;EAkRoB,WAAA;EACA,YAAA;AA0FpB;AA7WA;EAuRoB,kBAAA;AAyFpB;AAhXA;EA2RoB,YAAA;EACA,WAAA;AAwFpB;AAtFoB;EACI,6BAAA;AAwFxB;AArFoB;EACI,wBAAA;AAuFxB;AA1XA;EAwSoB,kBAAA;EACA,OAAA;EACA,MAAA;AAqFpB;AA/XA;EA8SoB,cAAA;EACA,UAAA;AAoFpB;AAnYA;EAmToB,qBAAA;EACA,eAAA;EACA,cAAA;AAmFpB;AAxYA;EAyToB,cAAA;EACA,WAAA;EACA,sBAAA;AAkFpB;AAxEY;EAAA;IAEQ,YAAA;EA0ElB;AACF;AAlZA;EA4UgB,kBAAA;AAyEhB;AAvEgB;EACI,OAAA;EACA,MAAA;EACA,YAAA;EACA,WAAA;AAyEpB;AAvEoB;EACI,kBAAA;EACA,UAAA;EACA,SAAA;EACA,WAAA;EACA,YAAA;EACA,gBAAA;EACA,YAAA;AAyExB;AAvEoB;EACI,qBAAA;EACA,kBAAA;EACA,mBAAA;EACA,iBAAA;EACA,cAAA;AAyExB;AA3aA;EAwWgB,mBAAA;AAsEhB;AA9aA;;;;;EArII,2BAAA;EACA,yBAAA;EACA,wBAAA;EACA,sBAAA;EACA,qBAAA;EACA,iBAAA;EACA,eAAA;EA+egB,eAAA;AA4EpB;AA5bA;EAqXgB,gBAAA;EACA,YAAA;AA0EhB;AAhcA;;EA2XgB,4BAAA;EACA,YAAA;EACA,kBAAA;EACA,mBAAA;EACA,sBAAA;EAEA,iBAAA;AAwEhB;AAtEgB;;EACI,kBAAA;AAyEpB;AA7cA;;EAwYoB,eAAA;EACA,sBAAA;EACA,oBAAA;EACA,iBAAA;EACA,sBAAA;AAyEpB;AAvEoB;;EACI,gBAAA;EACA,uBAAA;EACA,qBAAA;AA0ExB;AA7EoB;;EAMQ,gBAAA;AA2E5B;AA/dA;EA4ZY,gBAAA;AAsEZ;AAleA;EAgaY,yBAAA;EAEA,sBAAA;EACA,iBAAA;EACA,mBAAA;EACA,qBAAA;EACA,gBAAA;EACA,oCAAA;EACA,qBAAA;EACA,4BAAA;EACA,6CAAA;EACA,yBAAA;EACA,gBAAA;AAoEZ;AAhfA;EAibY,aAAA;AAkEZ;AAnfA;EAqbY,wBAAA;AAiEZ;AAtfA;EAybY,kBAAA;EACA,WAAA;EACA,kDAAA;AAgEZ;AA3fA;EA6fE;;;;;;;;;;;cAWY;EA7DF,mBAAA;AA+DZ;AA1gBA;;EAgdY,8BAAA;AA8DZ;AA9gBA;EAodY,yBAAA;AA6DZ;AAjhBA;EAwdY,uBAAA;AA4DZ;AAphBA;EA4dY,6BAAA;AA2DZ;AAvhBA;EAgeY,kBAAA;AA0DZ;AA1hBA;EAoeY,YAAA;EACA,WAAA;EACA,wBAAA;EACA,iBAAA;EACA,YAAA;EACA,oBAAA;EACA,iBAAA;EACA,sBAAA;EACA,wBAAA;EACA,6BAAA;EAyDV,wCAAwC;EACxC;;;cAGY;EAvDF,iBAAA;AAyDZ;AA5iBA;EAufY,kBAAA;EACA,oCAAA;EACA,eAAA;AAwDZ;AAjjBA;EA6fY,aAAA;AAuDZ;AApjBA;EAigBY,YAAA;EACA,gBAAA;EACA,WAAA;EACA,kBAAA;EACA,mBAAA;EACA,eAAA;EACA,wBAAA;AAsDZ;AA7jBA;EA2gBY,oBAAA;AAqDZ;AAhkBA;EA+gBY,oBAAA;AAoDZ;AAnkBA;;EArII,2BAAA;EACA,yBAAA;EACA,wBAAA;EACA,sBAAA;EACA,qBAAA;EACA,iBAAA;EACA,eAAA;EAqpBQ,iBAAA;EACA,kBAAA;EACA,eAAA;EACA,mBAAA;AAwDZ;AAjlBA;;EA6hBY,oBAAA;AAwDZ;AArlBA;;EAmiBgB,aAAA;AAsDhB;AAzlBA;;EAsiBoB,OAAA;EACA,kBAAA;AAuDpB;AAtDoB;;EACI,gBAAA;AAyDxB;AAlmBA;EA8iBgB,2BAAA;AAuDhB;AArmBA;EAijBgB,aAAA;AAuDhB;AAxmBA;EAqjBgB,YAAA;AAsDhB;AA3mBA;;EAyjBgB,eAAA;AAsDhB;AA/mBA;EA6jBU,YAAA;AAqDV;AAlnBA;;EAkkBU,+BAAA;AAoDV;AAtnBA;;;;;EA2kBgB,YAAA;EACA,iBAAA;EACA,kBAAA;AAkDhB;AA7CoB;;EACI,oBAAA;EACA,UAAA;AAgDxB;AApoBA;EA0lBgB,eAAA;AA6ChB",sourcesContent:["@import (reference) '~dash-table/style/reset.less';\n\n.fit-content-polyfill() {\n    width: auto; // MS Edge, IE\n    width: fit-content; // Chrome\n    width: -moz-fit-content; // Firefox\n    width: -webkit-fit-content; // Safari\n}\n\n.not-selectable() {\n    -webkit-touch-callout: none;\n    -webkit-user-select: none;\n    -khtml-user-select: none;\n    -moz-user-select: none;\n    -ms-user-select: none;\n    user-select: none;\n    cursor: default;\n}\n\n.dash-spreadsheet {\n    &.dash-freeze-left,\n    &.dash-freeze-top {\n        .fit-content-polyfill();\n    }\n\n    &.dash-freeze-left {\n        max-width: 500px;\n    }\n\n    &.dash-freeze-top,\n    &.dash-virtualized {\n        max-height: 500px;\n    }\n}\n\n.dash-tooltip {\n    border: 1px solid #e4e4e4;\n    border-radius: 5px;\n    position: absolute;\n    z-index: 500;\n\n    .dash-table-tooltip {\n        position: relative;\n        background-color: #f6f6f6;\n        max-width: 300px;\n        min-width: 300px;\n        padding: 2px 10px;\n    }\n\n    &[data-attr-anchor='top'] {\n        margin-top: 10px;\n\n        &:after, &:before {\n            bottom: 100%;\n            left: 50%;\n            border: solid transparent;\n            content: \" \";\n            height: 0;\n            width: 0;\n            position: absolute;\n            pointer-events: none;\n        }\n\n        &:after {\n            border-color: transparent;\n            border-bottom-color: #f6f6f6;\n            border-width: 8px;\n            margin-left: -8px;\n        }\n\n        &:before {\n            border-color: transparent;\n            border-bottom-color: #e4e4e4;\n            border-width: 9px;\n            margin-left: -9px;\n        }\n    }\n\n    &[data-attr-anchor='bottom'] {\n        margin-bottom: 10px;\n\n        &:after, &:before {\n            top: 100%;\n            left: 50%;\n            border: solid transparent;\n            content: \" \";\n            height: 0;\n            width: 0;\n            position: absolute;\n            pointer-events: none;\n        }\n\n        &:after {\n            border-color: transparent;\n            border-top-color: #f6f6f6;\n            border-width: 8px;\n            margin-left: -8px;\n        }\n        &:before {\n            border-color: transparent;\n            border-top-color: #e4e4e4;\n            border-width: 9px;\n            margin-left: -9px;\n        }\n    }\n}\n\n.dash-spreadsheet-menu {\n    display: flex;\n    flex-direction: row;\n\n    & > * {\n        padding-right: 5px;\n    }\n\n    .dash-spreadsheet-menu-item {\n        position: relative;\n\n        .show-hide-menu {\n            background-color: #fafafa;\n            border: 1px solid #d3d3d3;\n            display: flex;\n            flex-direction: column;\n            max-height: 300px;\n            overflow: auto;\n            position: absolute;\n            top: 100%;\n            left: 0;\n            z-index: 500;\n\n            .show-hide-menu-item {\n                display: flex;\n                flex-direction: row;\n                padding: 5px;\n\n                label {\n                    white-space: nowrap;\n                }\n            }\n        }\n    }\n}\n\n.dash-table-container {\n    .previous-next-container {\n        text-align: right;\n        padding: 5px 0px;\n\n        .page-number {\n            font-family: monospace;\n            display: inline-block;\n\n            .last-page {\n                display: inline-block;\n                text-align: center;\n                padding: 1px 2px;\n            }\n\n            .current-page-container {\n                display: inline-block;\n                position: relative;\n\n                .current-page-shadow,\n                input.current-page {\n                    display: inline-block;\n                    border-bottom: solid lightgrey 1px !important;\n                    color: black;\n                    border: none;\n                    text-align: center;\n                    font-family: monospace;\n                    font-size: 10pt;\n                    padding: 1px 2px;\n\n                    &::placeholder {\n                        color: black;\n                    }\n\n                    &:focus {\n                        outline: none;\n\n                        &::placeholder {\n                            opacity: 0;\n                        }\n                    }\n                }\n\n                input.current-page {\n                    position: absolute;\n                    top: 0;\n                    left: 0;\n                    width: 100%;\n                    height: 100%;\n                }\n            }\n        }\n\n        button.previous-page, button.next-page, button.first-page, button.last-page {\n            transition-duration: 400ms;\n            padding: 5px;\n            border: none;\n            display: inline-block;\n            margin-left: 5px;\n            margin-right: 5px;\n\n            &:hover {\n                color: hotpink;\n\n                &:disabled {\n                    color: graytext\n                }\n            }\n\n            &:focus {\n                outline: none;\n            }\n        }\n    }\n\n    .dash-spreadsheet-container {\n        .reset-css();\n        display: flex;\n        flex-direction: row;\n        position: relative;\n\n        // This overrides Bootstrap 3.4.1 body styling\n        // https://github.com/twbs/bootstrap/blob/v3-dev/dist/css/bootstrap.css#L1087\n        // Also unapplies with the latest `in development` 5.0.0-alpha2 (https://github.com/twbs/bootstrap/blob/main/dist/css/bootstrap.css#L51)\n        line-height: initial;\n\n        // This overrides Chrome's default `font-size: medium;` which is causing performance issues\n        // with AutoInputResize sub-component in react-select\n        // https://github.com/JedWatson/react-input-autosize/blob/05b0f86a7f8b16de99c2b31296ff0d3307f15957/src/AutosizeInput.js#L58\n        table {\n            font-size: inherit;\n            pointer-events: none;\n\n            td, th {\n                pointer-events: initial;\n            }\n        }\n\n        input[type=\"radio\"] {\n            // These override Bootstrap 3.4.1 type=\"radio\" styling\n            // https://github.com/twbs/bootstrap/blob/v3-dev/dist/css/bootstrap.css#L2621\n            // This is not a problem with the latest `in development` 5.0.0-alpha2\n            margin: initial;\n            line-height: initial;\n            // These override Bootstrap 4.5.0 type=\"radio\" styling\n            // https://github.com/twbs/bootstrap/blob/v4-dev/dist/css/bootstrap.css#L287\n            // This is not a problem with the latest `in development` 5.0.0-alpha2\n            box-sizing: initial;\n            padding: initial;\n        }\n\n\t    .dash-spreadsheet-inner {\n            box-sizing: border-box;\n            display: flex;\n            flex-direction: column;\n\n            *,\n            *:after,\n            *:before {\n\t\t        box-sizing: inherit;\n            }\n\n            .Select {\n                overflow: hidden;\n                position: static;\n            }\n\n            .Select,\n            .Select-control {\n\t\t        background-color: inherit;\n            }\n\n            .Select-value {\n                display: flex;\n                flex-direction: column;\n                justify-content: center;\n                margin-top: -2px;\n            }\n\n            .marker-row {\n                tr {\n                    visibility: hidden !important;\n                }\n\n                td, th {\n                    height: 0 !important;\n                    padding: 0 !important;\n                    margin: 0 !important;\n                }\n            }\n\n            .dash-filter {\n\t\t        input::placeholder {\n                    color: inherit;\n                    font-size: 0.8em;\n                    padding-right: 5px;\n                }\n\n                & + .dash-filter {\n                    &:not(:hover):not(:focus-within) {\n                        input::placeholder {\n                            color: transparent;\n                        }\n                    }\n                }\n\n                &.invalid {\n                    background-color: pink;\n                }\n            }\n\n            &:not(.dash-empty-11) {\n                .dt-table-container__row-0 {\n                    tr:last-of-type {\n                        td, th {\n                            border-bottom: none !important;\n                        }\n                    }\n                }\n            }\n\n            &:not(.dash-empty-01) {\n        \t\t.cell-0-0,\n\t\t        .cell-1-0 {\n                    tr {\n                        td, th {\n                            &:last-of-type {\n                                border-right: none !important;\n                            }\n                        }\n                    }\n\t    \t    }\n            }\n\n            .cell-0-0 {\n                overflow: hidden;\n            }\n\n            .cell-0-0,\n            .cell-1-0 {\n                td, th {\n                    &.phantom-cell {\n                        border-color: transparent !important;\n                    }\n                }\n            }\n\n            .cell-0-1,\n            .cell-1-1 {\n                td, th {\n                    &.phantom-cell {\n                        border-color: transparent inherit transparent transparent !important;\n                    }\n                }\n            }\n\n            &.dash-freeze-left,\n            &.dash-freeze-top,\n            &.dash-virtualized {\n                overflow: hidden !important;\n\n                .dt-table-container__row-0 {\n                    display: flex;\n                    flex: 0 0 auto;\n                    flex-direction: row;\n                }\n\n                .dt-table-container__row-1 {\n                    display: flex;\n                    flex-direction: row;\n                    overflow: auto;\n                }\n\n                .cell-0-0,\n                .cell-1-0 {\n                    flex: 0 0 auto;\n                    left: 0;\n                    position: sticky;\n                    position:-webkit-sticky;\n                    z-index: 400;\n                }\n\n                .cell-0-1 {\n                    z-index: 300;\n                    flex: 0 0 auto;\n                }\n\n                .cell-1-1 {\n                    flex: 0 0 auto;\n                }\n            }\n\n            &.dash-fill-width {\n                .cell-0-1,\n                .cell-1-1 {\n                    flex: 1 0 auto;\n                }\n\n                .cell {\n                    table {\n                        width: 100%;\n                    }\n                }\n            }\n\n            td {\n                background-color: inherit;\n\n                &.focused {\n                    margin: -1px;\n                    z-index: 200;\n                }\n\n                .dash-cell-value-container {\n                    width: 100%;\n                    height: 100%;\n                }\n\n                .dash-input-cell-value-container {\n                    position: relative;\n                }\n\n                .dash-cell-value {\n                    height: 100%;\n                    width: 100%;\n\n                    &.unfocused.selectable::selection {\n                        background-color: transparent;\n                    }\n\n                    &.unfocused {\n                        caret-color: transparent;\n                    }\n                }\n\n                input.dash-cell-value {\n                    position: absolute;\n                    left: 0;\n                    top: 0;\n                }\n\n                .cell-value-shadow {\n                    margin: auto 0;\n                    opacity: 0;\n                }\n\n                .input-cell-value-shadow {\n                    display: inline-block;\n                    height: initial;\n                    width: initial;\n                }\n\n                .dropdown-cell-value-shadow {\n                    display: block;\n                    height: 0px;\n                    padding: 0 42px 0 10px;\n                }\n            }\n\n            /*\n             * fixes Firefox td height bug on td > dropdown children\n             * bug should only appear on FF but\n             * @supports = scoped to Firefox only\n             * to minimize side effects\n             */\n            @supports (-moz-appearance:none) {\n                td.dropdown .dash-cell-value-container {\n                    height: auto;\n                }\n            }\n\n            th.dash-filter {\n                position: relative;\n\n                & input {\n                    left: 0;\n                    top: 0;\n                    height: 100%;\n                    width: 100%;\n\n                    &.dash-filter--case {\n                        position: relative;\n                        left: auto;\n                        top: auto;\n                        width: auto;\n                        height: 16px;\n                        line-height: 0px;\n                        padding: 1px;\n                    }\n                    &.dash-filter--case--sensitive {\n                        border-color: hotpink;\n                        border-radius: 3px;\n                        border-style: solid;\n                        border-width: 2px;\n                        color: hotpink;\n                    }\n                }\n            }\n\n            th {\n                white-space: nowrap;\n\n                .column-header--clear,\n                .column-header--delete,\n                .column-header--edit,\n                .column-header--hide,\n                .column-header--sort {\n                    .not-selectable();\n                    cursor: pointer;\n                }\n            }\n\n            tr {\n                min-height: 30px;\n                height: 30px;\n            }\n\n            // cell content styling\n            td, th {\n                background-clip: padding-box;\n                padding: 2px;\n                overflow-x: hidden;\n                white-space: nowrap;\n                box-sizing: border-box;\n\n                text-align: right;\n\n                &.phantom-cell {\n                    visibility: hidden;\n                }\n\n                div.dash-cell-value {\n                    display: inline;\n                    vertical-align: middle;\n                    white-space: inherit;\n                    overflow: inherit;\n                    text-overflow: inherit;\n\n                    &.cell-markdown {\n                        text-align: left;\n                        font-family: sans-serif;\n                        display: inline-block;\n\n                        blockquote {\n                            white-space: pre;\n                        }\n                    }\n                }\n            }\n        }\n\n    \t.dash-spreadsheet-inner textarea {\n            white-space: pre;\n\t    }\n\n\t    .dash-spreadsheet-inner table {\n            border-collapse: collapse;\n\n            font-family: monospace;\n            --accent: hotpink;\n            --border: lightgrey;\n            --text-color: rgb(60, 60, 60);\n            --hover: rgb(253, 253, 253);\n            --background-color-ellipses: rgb(253, 253, 253);\n            --faded-text: rgb(250, 250, 250);\n            --faded-text-header: rgb(180, 180, 180);\n            --selected-background: rgba(255, 65, 54, 0.2);\n            --faded-dropdown: rgb(240, 240, 240);\n            --muted: rgb(200, 200, 200);\n\t    }\n\n\t    /* focus happens after copying to clipboard */\n\t    .dash-spreadsheet-inner table:focus {\n            outline: none;\n\t    }\n\n\t    .dash-spreadsheet-inner thead {\n            display: table-row-group;\n\t    }\n\n\t    .elip {\n            text-align: center;\n            width: 100%;\n            background-color: var(--background-color-ellipses);\n\t    }\n\n\t    .dash-spreadsheet-inner td.dropdown {\n            /*\n             * To view the dropdown's contents, we need\n             * overflow-y: visible.\n             * Unfortunately, overflow-x: hidden and overflow-y: visible\n             * can't both be set at the same time.\n             * So, we have to make both overflow-x: visible and overflow-y: visble\n             *\n             * See https://stackoverflow.com/questions/6421966/\n             *\n             * There might be another solution with parent divs, but I haven't\n             * tried it.\n             */\n            overflow-x: visible;\n\t    }\n\n        .dash-spreadsheet-inner :not(.cell--selected) tr:hover,\n        tr:hover input :not(.cell--selected) {\n            background-color: var(--hover);\n    \t}\n\n    \t.dash-spreadsheet-inner th {\n            background-color: rgb(250, 250, 250);\n\t    }\n\n\t    .dash-spreadsheet-inner td {\n            background-color: white;\n\t    }\n\n\t    .expanded-row--empty-cell {\n            background-color: transparent;\n\t    }\n\n\t    .expanded-row {\n            text-align: center;\n\t    }\n\n\t    .dash-spreadsheet-inner input:not([type=radio]):not([type=checkbox]) {\n            padding: 0px;\n            margin: 0px;\n            height: calc(100% - 1px);\n            line-height: 30px;\n            border: none;\n            font-family: inherit;\n            text-align: right;\n            box-sizing: border-box;\n            color: var(--text-color);\n            background-color: transparent; /* so as to not overlay the box shadow */\n\n            /* browser's default text-shadow is `$color 0px 0px 0px;`\n             * for `input`, which makes it look a little bit heavier than dropdowns\n             * or bare `td`\n             */\n            text-shadow: none;\n\t    }\n\n\t    .dash-spreadsheet-inner input.unfocused {\n            color: transparent;\n            text-shadow: 0 0 0 var(--text-color);\n            cursor: default;\n\t    }\n\n\t    .dash-spreadsheet-inner input.unfocused:focus {\n            outline: none;\n\t    }\n\n\t    .toggle-row {\n            border: none;\n            box-shadow: none;\n            width: 10px;\n            padding-left: 10px;\n            padding-right: 10px;\n            cursor: pointer;\n            color: var(--faded-text);\n\t    }\n\n    \t.toggle-row--expanded {\n            color: var(--accent);\n\t    }\n\n    \t.dash-spreadsheet-inner tr:hover .toggle-row {\n            color: var(--accent);\n\t    }\n\n        .dash-spreadsheet-inner .dash-delete-cell,\n        .dash-spreadsheet-inner .dash-delete-header {\n            .not-selectable();\n\n            font-size: 1.3rem;\n            text-align: center;\n            cursor: pointer;\n            color: var(--muted);\n    \t}\n        .dash-spreadsheet-inner .dash-delete-cell:hover,\n        .dash-spreadsheet-inner .dash-delete-header:hover {\n            color: var(--accent);\n    \t}\n\n\t    .dash-spreadsheet-inner {\n            .dash-header>div,\n            .dash-filter>div {\n                display: flex;\n\n                input[type=\"text\"] {\n                    flex: 1;\n                    line-height: unset;\n                    &::placeholder {\n                        font-size: 0.9em;\n                    }\n                }\n            }\n            .dash-filter>div {\n                flex-direction: row-reverse;\n            }\n            .column-actions {\n                display: flex;\n            }\n\n            .column-header-name {\n                flex-grow: 1;\n            }\n\n            [class^='column-header--'], [class^='dash-filter--'] {\n                cursor: pointer;\n            }\n\n            .column-header--select {\n\t\t        height: auto;\n            }\n\n            .column-header--select,\n            .column-header--sort {\n        \t\tcolor: var(--faded-text-header);\n            }\n\n\n            .dash-filter--case,\n            .column-header--clear,\n            .column-header--delete,\n            .column-header--edit,\n            .column-header--hide {\n                opacity: 0.1;\n                padding-left: 2px;\n                padding-right: 2px;\n            }\n\n            th:hover {\n        \t\t[class^='column-header--'], [class^='dash-filter--'] {\n                    &:not(.disabled) {\n                        color: var(--accent);\n                        opacity: 1;\n                    }\n                }\n            }\n\n            .dash-filter--case {\n                font-size: 10px;\n            }\n\t    }\n    }\n}\n",'/*RESET CSS*/\n.reset-css() {\n    /* The "normal" reset CSS */\n    // div,dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,code,form,fieldset,legend,input,textarea,p,blockquote,th,td{margin:0;padding:0}table{border-collapse:collapse;border-spacing:0}fieldset,img{border:0}address,caption,cite,code,dfn,em,strong,th,var{font-style:normal;font-weight:normal}ol,ul{list-style:none}caption,th{text-align:left}h1,h2,h3,h4,h5,h6{font-size:100%;font-weight:normal}q:before,q:after{content:\'\'}abbr,acronym{border:0;font-variant:normal}sup{vertical-align:text-top}sub{vertical-align:text-bottom}input,textarea,select{font-family:inherit;font-size:inherit;font-weight:inherit;*font-size:100%}legend{color:#000}#yui3-css-stamp.cssreset{display:none}\n\n    /* The "modified" reset CSS applied to the table to ignore markdown cells */\n    th {\n        font-style:normal;\n        font-weight:normal;\n        text-align:left;\n    }\n\n    th, td {\n        margin:0;\n        padding:0;\n\n        & > div:not(.cell-markdown) {\n            margin:0;\n            padding:0;\n\n            dl,dt,dd,ul,ol,li,h1,h2,h3,h4,h5,h6,pre,code,form,fieldset,legend,input,textarea,p,blockquote,th,td{margin:0;padding:0}\n            table{border-collapse:collapse;border-spacing:0}\n            fieldset,img{border:0}\n            address,caption,cite,code,dfn,em,strong,th,var{font-style:normal;font-weight:normal}\n            ol,ul{list-style:none}\n            caption,th{text-align:left}\n            h1,h2,h3,h4,h5,h6{font-size:100%;font-weight:normal}\n            q:before,q:after{content:\'\'}\n            abbr,acronym{border:0;font-variant:normal}\n            sup{vertical-align:text-top}\n            sub{vertical-align:text-bottom}\n            input,textarea,select{font-family:inherit;font-size:inherit;font-weight:inherit;*font-size:100%}\n            legend{color:#000}\n        }\n    }\n\n    // Input buttons have an overlay + are rounded by default in iOS Mobile Safari\n    // http://stackoverflow.com/questions/2918707/turn-off-iphone-safari-input-element-rounding\n    input[type="button"] {\n        border-radius: 0;\n        -webkit-appearance: none;\n    }\n\n    *:focus {\n        outline: none;\n    }\n}'],sourceRoot:""}]),t.Z=a},7288:function(e,t,n){"use strict";var r=n(7537),o=n.n(r),i=n(3645),a=n.n(i)()(o());a.push([e.id,"/**\n * React Select\n * ============\n * Created by Jed Watson and Joss Mackison for KeystoneJS, http://www.keystonejs.com/\n * https://twitter.com/jedwatson https://twitter.com/jossmackison https://twitter.com/keystonejs\n * MIT License: https://github.com/JedWatson/react-select\n*/\n.Select {\n  position: relative;\n}\n.Select input::-webkit-contacts-auto-fill-button,\n.Select input::-webkit-credentials-auto-fill-button {\n  display: none !important;\n}\n.Select input::-ms-clear {\n  display: none !important;\n}\n.Select input::-ms-reveal {\n  display: none !important;\n}\n.Select,\n.Select div,\n.Select input,\n.Select span {\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n.Select.is-disabled .Select-arrow-zone {\n  cursor: default;\n  pointer-events: none;\n  opacity: 0.35;\n}\n.Select.is-disabled > .Select-control {\n  background-color: #f9f9f9;\n}\n.Select.is-disabled > .Select-control:hover {\n  box-shadow: none;\n}\n.Select.is-open > .Select-control {\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n  background: #fff;\n  border-color: #b3b3b3 #ccc #d9d9d9;\n}\n.Select.is-open > .Select-control .Select-arrow {\n  top: -2px;\n  border-color: transparent transparent #999;\n  border-width: 0 5px 5px;\n}\n.Select.is-searchable.is-open > .Select-control {\n  cursor: text;\n}\n.Select.is-searchable.is-focused:not(.is-open) > .Select-control {\n  cursor: text;\n}\n.Select.is-focused > .Select-control {\n  background: #fff;\n}\n.Select.is-focused:not(.is-open) > .Select-control {\n  border-color: #007eff;\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 3px rgba(0, 126, 255, 0.1);\n  background: #fff;\n}\n.Select.has-value.is-clearable.Select--single > .Select-control .Select-value {\n  padding-right: 42px;\n}\n.Select.has-value.Select--single > .Select-control .Select-value .Select-value-label,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value .Select-value-label {\n  color: #333;\n}\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label {\n  cursor: pointer;\n  text-decoration: none;\n}\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label:hover,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:hover,\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label:focus,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:focus {\n  color: #007eff;\n  outline: none;\n  text-decoration: underline;\n}\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label:focus,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:focus {\n  background: #fff;\n}\n.Select.has-value.is-pseudo-focused .Select-input {\n  opacity: 0;\n}\n.Select.is-open .Select-arrow,\n.Select .Select-arrow-zone:hover > .Select-arrow {\n  border-top-color: #666;\n}\n.Select.Select--rtl {\n  direction: rtl;\n  text-align: right;\n}\n.Select-control {\n  background-color: #fff;\n  border-color: #d9d9d9 #ccc #b3b3b3;\n  border-radius: 4px;\n  border: 1px solid #ccc;\n  color: #333;\n  cursor: default;\n  display: table;\n  border-spacing: 0;\n  border-collapse: separate;\n  height: 36px;\n  outline: none;\n  overflow: hidden;\n  position: relative;\n  width: 100%;\n}\n.Select-control:hover {\n  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);\n}\n.Select-control .Select-input:focus {\n  outline: none;\n  background: #fff;\n}\n.Select-placeholder,\n.Select--single > .Select-control .Select-value {\n  bottom: 0;\n  color: #aaa;\n  left: 0;\n  line-height: 34px;\n  padding-left: 10px;\n  padding-right: 10px;\n  position: absolute;\n  right: 0;\n  top: 0;\n  max-width: 100%;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.Select-input {\n  height: 34px;\n  padding-left: 10px;\n  padding-right: 10px;\n  vertical-align: middle;\n}\n.Select-input > input {\n  width: 100%;\n  background: none transparent;\n  border: 0 none;\n  box-shadow: none;\n  cursor: default;\n  display: inline-block;\n  font-family: inherit;\n  font-size: inherit;\n  margin: 0;\n  outline: none;\n  line-height: 17px;\n  /* For IE 8 compatibility */\n  padding: 8px 0 12px;\n  /* For IE 8 compatibility */\n  -webkit-appearance: none;\n}\n.is-focused .Select-input > input {\n  cursor: text;\n}\n.has-value.is-pseudo-focused .Select-input {\n  opacity: 0;\n}\n.Select-control:not(.is-searchable) > .Select-input {\n  outline: none;\n}\n.Select-loading-zone {\n  cursor: pointer;\n  display: table-cell;\n  position: relative;\n  text-align: center;\n  vertical-align: middle;\n  width: 16px;\n}\n.Select-loading {\n  -webkit-animation: Select-animation-spin 400ms infinite linear;\n  -o-animation: Select-animation-spin 400ms infinite linear;\n  animation: Select-animation-spin 400ms infinite linear;\n  width: 16px;\n  height: 16px;\n  box-sizing: border-box;\n  border-radius: 50%;\n  border: 2px solid #ccc;\n  border-right-color: #333;\n  display: inline-block;\n  position: relative;\n  vertical-align: middle;\n}\n.Select-clear-zone {\n  -webkit-animation: Select-animation-fadeIn 200ms;\n  -o-animation: Select-animation-fadeIn 200ms;\n  animation: Select-animation-fadeIn 200ms;\n  color: #999;\n  cursor: pointer;\n  display: table-cell;\n  position: relative;\n  text-align: center;\n  vertical-align: middle;\n  width: 17px;\n}\n.Select-clear-zone:hover {\n  color: #D0021B;\n}\n.Select-clear {\n  display: inline-block;\n  font-size: 18px;\n  line-height: 1;\n}\n.Select--multi .Select-clear-zone {\n  width: 17px;\n}\n.Select-arrow-zone {\n  cursor: pointer;\n  display: table-cell;\n  position: relative;\n  text-align: center;\n  vertical-align: middle;\n  width: 25px;\n  padding-right: 5px;\n}\n.Select--rtl .Select-arrow-zone {\n  padding-right: 0;\n  padding-left: 5px;\n}\n.Select-arrow {\n  border-color: #999 transparent transparent;\n  border-style: solid;\n  border-width: 5px 5px 2.5px;\n  display: inline-block;\n  height: 0;\n  width: 0;\n  position: relative;\n}\n.Select-control > *:last-child {\n  padding-right: 5px;\n}\n.Select--multi .Select-multi-value-wrapper {\n  display: inline-block;\n}\n.Select .Select-aria-only {\n  position: absolute;\n  display: inline-block;\n  height: 1px;\n  width: 1px;\n  margin: -1px;\n  clip: rect(0, 0, 0, 0);\n  overflow: hidden;\n  float: left;\n}\n@-webkit-keyframes Select-animation-fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n@keyframes Select-animation-fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n.Select-menu-outer {\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n  background-color: #fff;\n  border: 1px solid #ccc;\n  border-top-color: #e6e6e6;\n  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);\n  box-sizing: border-box;\n  margin-top: -1px;\n  max-height: 200px;\n  position: absolute;\n  left: 0;\n  top: 100%;\n  width: 100%;\n  z-index: 1;\n  -webkit-overflow-scrolling: touch;\n}\n.Select-menu {\n  max-height: 198px;\n  overflow-y: auto;\n}\n.Select-option {\n  box-sizing: border-box;\n  background-color: #fff;\n  color: #666666;\n  cursor: pointer;\n  display: block;\n  padding: 8px 10px;\n}\n.Select-option:last-child {\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n}\n.Select-option.is-selected {\n  background-color: #f5faff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.04);\n  color: #333;\n}\n.Select-option.is-focused {\n  background-color: #ebf5ff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.08);\n  color: #333;\n}\n.Select-option.is-disabled {\n  color: #cccccc;\n  cursor: default;\n}\n.Select-noresults {\n  box-sizing: border-box;\n  color: #999999;\n  cursor: default;\n  display: block;\n  padding: 8px 10px;\n}\n.Select--multi .Select-input {\n  vertical-align: middle;\n  margin-left: 10px;\n  padding: 0;\n}\n.Select--multi.Select--rtl .Select-input {\n  margin-left: 0;\n  margin-right: 10px;\n}\n.Select--multi.has-value .Select-input {\n  margin-left: 5px;\n}\n.Select--multi .Select-value {\n  background-color: #ebf5ff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.08);\n  border-radius: 2px;\n  border: 1px solid #c2e0ff;\n  /* Fallback color for IE 8 */\n  border: 1px solid rgba(0, 126, 255, 0.24);\n  color: #007eff;\n  display: inline-block;\n  font-size: 0.9em;\n  line-height: 1.4;\n  margin-left: 5px;\n  margin-top: 5px;\n  vertical-align: top;\n}\n.Select--multi .Select-value-icon,\n.Select--multi .Select-value-label {\n  display: inline-block;\n  vertical-align: middle;\n}\n.Select--multi .Select-value-label {\n  border-bottom-right-radius: 2px;\n  border-top-right-radius: 2px;\n  cursor: default;\n  padding: 2px 5px;\n}\n.Select--multi a.Select-value-label {\n  color: #007eff;\n  cursor: pointer;\n  text-decoration: none;\n}\n.Select--multi a.Select-value-label:hover {\n  text-decoration: underline;\n}\n.Select--multi .Select-value-icon {\n  cursor: pointer;\n  border-bottom-left-radius: 2px;\n  border-top-left-radius: 2px;\n  border-right: 1px solid #c2e0ff;\n  /* Fallback color for IE 8 */\n  border-right: 1px solid rgba(0, 126, 255, 0.24);\n  padding: 1px 5px 3px;\n}\n.Select--multi .Select-value-icon:hover,\n.Select--multi .Select-value-icon:focus {\n  background-color: #d8eafd;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 113, 230, 0.08);\n  color: #0071e6;\n}\n.Select--multi .Select-value-icon:active {\n  background-color: #c2e0ff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.24);\n}\n.Select--multi.Select--rtl .Select-value {\n  margin-left: 0;\n  margin-right: 5px;\n}\n.Select--multi.Select--rtl .Select-value-icon {\n  border-right: none;\n  border-left: 1px solid #c2e0ff;\n  /* Fallback color for IE 8 */\n  border-left: 1px solid rgba(0, 126, 255, 0.24);\n}\n.Select--multi.is-disabled .Select-value {\n  background-color: #fcfcfc;\n  border: 1px solid #e3e3e3;\n  color: #333;\n}\n.Select--multi.is-disabled .Select-value-icon {\n  cursor: not-allowed;\n  border-right: 1px solid #e3e3e3;\n}\n.Select--multi.is-disabled .Select-value-icon:hover,\n.Select--multi.is-disabled .Select-value-icon:focus,\n.Select--multi.is-disabled .Select-value-icon:active {\n  background-color: #fcfcfc;\n}\n@keyframes Select-animation-spin {\n  to {\n    transform: rotate(1turn);\n  }\n}\n@-webkit-keyframes Select-animation-spin {\n  to {\n    -webkit-transform: rotate(1turn);\n  }\n}\n","",{version:3,sources:["webpack://./node_modules/react-select/dist/react-select.css"],names:[],mappings:"AAAA;;;;;;CAMC;AACD;EACE,kBAAkB;AACpB;AACA;;EAEE,wBAAwB;AAC1B;AACA;EACE,wBAAwB;AAC1B;AACA;EACE,wBAAwB;AAC1B;AACA;;;;EAIE,8BAA8B;EAC9B,2BAA2B;EAC3B,sBAAsB;AACxB;AACA;EACE,eAAe;EACf,oBAAoB;EACpB,aAAa;AACf;AACA;EACE,yBAAyB;AAC3B;AACA;EACE,gBAAgB;AAClB;AACA;EACE,6BAA6B;EAC7B,4BAA4B;EAC5B,gBAAgB;EAChB,kCAAkC;AACpC;AACA;EACE,SAAS;EACT,0CAA0C;EAC1C,uBAAuB;AACzB;AACA;EACE,YAAY;AACd;AACA;EACE,YAAY;AACd;AACA;EACE,gBAAgB;AAClB;AACA;EACE,qBAAqB;EACrB,kFAAkF;EAClF,gBAAgB;AAClB;AACA;EACE,mBAAmB;AACrB;AACA;;EAEE,WAAW;AACb;AACA;;EAEE,eAAe;EACf,qBAAqB;AACvB;AACA;;;;EAIE,cAAc;EACd,aAAa;EACb,0BAA0B;AAC5B;AACA;;EAEE,gBAAgB;AAClB;AACA;EACE,UAAU;AACZ;AACA;;EAEE,sBAAsB;AACxB;AACA;EACE,cAAc;EACd,iBAAiB;AACnB;AACA;EACE,sBAAsB;EACtB,kCAAkC;EAClC,kBAAkB;EAClB,sBAAsB;EACtB,WAAW;EACX,eAAe;EACf,cAAc;EACd,iBAAiB;EACjB,yBAAyB;EACzB,YAAY;EACZ,aAAa;EACb,gBAAgB;EAChB,kBAAkB;EAClB,WAAW;AACb;AACA;EACE,uCAAuC;AACzC;AACA;EACE,aAAa;EACb,gBAAgB;AAClB;AACA;;EAEE,SAAS;EACT,WAAW;EACX,OAAO;EACP,iBAAiB;EACjB,kBAAkB;EAClB,mBAAmB;EACnB,kBAAkB;EAClB,QAAQ;EACR,MAAM;EACN,eAAe;EACf,gBAAgB;EAChB,uBAAuB;EACvB,mBAAmB;AACrB;AACA;EACE,YAAY;EACZ,kBAAkB;EAClB,mBAAmB;EACnB,sBAAsB;AACxB;AACA;EACE,WAAW;EACX,4BAA4B;EAC5B,cAAc;EACd,gBAAgB;EAChB,eAAe;EACf,qBAAqB;EACrB,oBAAoB;EACpB,kBAAkB;EAClB,SAAS;EACT,aAAa;EACb,iBAAiB;EACjB,2BAA2B;EAC3B,mBAAmB;EACnB,2BAA2B;EAC3B,wBAAwB;AAC1B;AACA;EACE,YAAY;AACd;AACA;EACE,UAAU;AACZ;AACA;EACE,aAAa;AACf;AACA;EACE,eAAe;EACf,mBAAmB;EACnB,kBAAkB;EAClB,kBAAkB;EAClB,sBAAsB;EACtB,WAAW;AACb;AACA;EACE,8DAA8D;EAC9D,yDAAyD;EACzD,sDAAsD;EACtD,WAAW;EACX,YAAY;EACZ,sBAAsB;EACtB,kBAAkB;EAClB,sBAAsB;EACtB,wBAAwB;EACxB,qBAAqB;EACrB,kBAAkB;EAClB,sBAAsB;AACxB;AACA;EACE,gDAAgD;EAChD,2CAA2C;EAC3C,wCAAwC;EACxC,WAAW;EACX,eAAe;EACf,mBAAmB;EACnB,kBAAkB;EAClB,kBAAkB;EAClB,sBAAsB;EACtB,WAAW;AACb;AACA;EACE,cAAc;AAChB;AACA;EACE,qBAAqB;EACrB,eAAe;EACf,cAAc;AAChB;AACA;EACE,WAAW;AACb;AACA;EACE,eAAe;EACf,mBAAmB;EACnB,kBAAkB;EAClB,kBAAkB;EAClB,sBAAsB;EACtB,WAAW;EACX,kBAAkB;AACpB;AACA;EACE,gBAAgB;EAChB,iBAAiB;AACnB;AACA;EACE,0CAA0C;EAC1C,mBAAmB;EACnB,2BAA2B;EAC3B,qBAAqB;EACrB,SAAS;EACT,QAAQ;EACR,kBAAkB;AACpB;AACA;EACE,kBAAkB;AACpB;AACA;EACE,qBAAqB;AACvB;AACA;EACE,kBAAkB;EAClB,qBAAqB;EACrB,WAAW;EACX,UAAU;EACV,YAAY;EACZ,sBAAsB;EACtB,gBAAgB;EAChB,WAAW;AACb;AACA;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;AACA;EACE;IACE,UAAU;EACZ;EACA;IACE,UAAU;EACZ;AACF;AACA;EACE,+BAA+B;EAC/B,8BAA8B;EAC9B,sBAAsB;EACtB,sBAAsB;EACtB,yBAAyB;EACzB,uCAAuC;EACvC,sBAAsB;EACtB,gBAAgB;EAChB,iBAAiB;EACjB,kBAAkB;EAClB,OAAO;EACP,SAAS;EACT,WAAW;EACX,UAAU;EACV,iCAAiC;AACnC;AACA;EACE,iBAAiB;EACjB,gBAAgB;AAClB;AACA;EACE,sBAAsB;EACtB,sBAAsB;EACtB,cAAc;EACd,eAAe;EACf,cAAc;EACd,iBAAiB;AACnB;AACA;EACE,+BAA+B;EAC/B,8BAA8B;AAChC;AACA;EACE,yBAAyB;EACzB,4BAA4B;EAC5B,yCAAyC;EACzC,WAAW;AACb;AACA;EACE,yBAAyB;EACzB,4BAA4B;EAC5B,yCAAyC;EACzC,WAAW;AACb;AACA;EACE,cAAc;EACd,eAAe;AACjB;AACA;EACE,sBAAsB;EACtB,cAAc;EACd,eAAe;EACf,cAAc;EACd,iBAAiB;AACnB;AACA;EACE,sBAAsB;EACtB,iBAAiB;EACjB,UAAU;AACZ;AACA;EACE,cAAc;EACd,kBAAkB;AACpB;AACA;EACE,gBAAgB;AAClB;AACA;EACE,yBAAyB;EACzB,4BAA4B;EAC5B,yCAAyC;EACzC,kBAAkB;EAClB,yBAAyB;EACzB,4BAA4B;EAC5B,yCAAyC;EACzC,cAAc;EACd,qBAAqB;EACrB,gBAAgB;EAChB,gBAAgB;EAChB,gBAAgB;EAChB,eAAe;EACf,mBAAmB;AACrB;AACA;;EAEE,qBAAqB;EACrB,sBAAsB;AACxB;AACA;EACE,+BAA+B;EAC/B,4BAA4B;EAC5B,eAAe;EACf,gBAAgB;AAClB;AACA;EACE,cAAc;EACd,eAAe;EACf,qBAAqB;AACvB;AACA;EACE,0BAA0B;AAC5B;AACA;EACE,eAAe;EACf,8BAA8B;EAC9B,2BAA2B;EAC3B,+BAA+B;EAC/B,4BAA4B;EAC5B,+CAA+C;EAC/C,oBAAoB;AACtB;AACA;;EAEE,yBAAyB;EACzB,4BAA4B;EAC5B,yCAAyC;EACzC,cAAc;AAChB;AACA;EACE,yBAAyB;EACzB,4BAA4B;EAC5B,yCAAyC;AAC3C;AACA;EACE,cAAc;EACd,iBAAiB;AACnB;AACA;EACE,kBAAkB;EAClB,8BAA8B;EAC9B,4BAA4B;EAC5B,8CAA8C;AAChD;AACA;EACE,yBAAyB;EACzB,yBAAyB;EACzB,WAAW;AACb;AACA;EACE,mBAAmB;EACnB,+BAA+B;AACjC;AACA;;;EAGE,yBAAyB;AAC3B;AACA;EACE;IACE,wBAAwB;EAC1B;AACF;AACA;EACE;IACE,gCAAgC;EAClC;AACF",sourcesContent:["/**\n * React Select\n * ============\n * Created by Jed Watson and Joss Mackison for KeystoneJS, http://www.keystonejs.com/\n * https://twitter.com/jedwatson https://twitter.com/jossmackison https://twitter.com/keystonejs\n * MIT License: https://github.com/JedWatson/react-select\n*/\n.Select {\n  position: relative;\n}\n.Select input::-webkit-contacts-auto-fill-button,\n.Select input::-webkit-credentials-auto-fill-button {\n  display: none !important;\n}\n.Select input::-ms-clear {\n  display: none !important;\n}\n.Select input::-ms-reveal {\n  display: none !important;\n}\n.Select,\n.Select div,\n.Select input,\n.Select span {\n  -webkit-box-sizing: border-box;\n  -moz-box-sizing: border-box;\n  box-sizing: border-box;\n}\n.Select.is-disabled .Select-arrow-zone {\n  cursor: default;\n  pointer-events: none;\n  opacity: 0.35;\n}\n.Select.is-disabled > .Select-control {\n  background-color: #f9f9f9;\n}\n.Select.is-disabled > .Select-control:hover {\n  box-shadow: none;\n}\n.Select.is-open > .Select-control {\n  border-bottom-right-radius: 0;\n  border-bottom-left-radius: 0;\n  background: #fff;\n  border-color: #b3b3b3 #ccc #d9d9d9;\n}\n.Select.is-open > .Select-control .Select-arrow {\n  top: -2px;\n  border-color: transparent transparent #999;\n  border-width: 0 5px 5px;\n}\n.Select.is-searchable.is-open > .Select-control {\n  cursor: text;\n}\n.Select.is-searchable.is-focused:not(.is-open) > .Select-control {\n  cursor: text;\n}\n.Select.is-focused > .Select-control {\n  background: #fff;\n}\n.Select.is-focused:not(.is-open) > .Select-control {\n  border-color: #007eff;\n  box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075), 0 0 0 3px rgba(0, 126, 255, 0.1);\n  background: #fff;\n}\n.Select.has-value.is-clearable.Select--single > .Select-control .Select-value {\n  padding-right: 42px;\n}\n.Select.has-value.Select--single > .Select-control .Select-value .Select-value-label,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value .Select-value-label {\n  color: #333;\n}\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label {\n  cursor: pointer;\n  text-decoration: none;\n}\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label:hover,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:hover,\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label:focus,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:focus {\n  color: #007eff;\n  outline: none;\n  text-decoration: underline;\n}\n.Select.has-value.Select--single > .Select-control .Select-value a.Select-value-label:focus,\n.Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value a.Select-value-label:focus {\n  background: #fff;\n}\n.Select.has-value.is-pseudo-focused .Select-input {\n  opacity: 0;\n}\n.Select.is-open .Select-arrow,\n.Select .Select-arrow-zone:hover > .Select-arrow {\n  border-top-color: #666;\n}\n.Select.Select--rtl {\n  direction: rtl;\n  text-align: right;\n}\n.Select-control {\n  background-color: #fff;\n  border-color: #d9d9d9 #ccc #b3b3b3;\n  border-radius: 4px;\n  border: 1px solid #ccc;\n  color: #333;\n  cursor: default;\n  display: table;\n  border-spacing: 0;\n  border-collapse: separate;\n  height: 36px;\n  outline: none;\n  overflow: hidden;\n  position: relative;\n  width: 100%;\n}\n.Select-control:hover {\n  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);\n}\n.Select-control .Select-input:focus {\n  outline: none;\n  background: #fff;\n}\n.Select-placeholder,\n.Select--single > .Select-control .Select-value {\n  bottom: 0;\n  color: #aaa;\n  left: 0;\n  line-height: 34px;\n  padding-left: 10px;\n  padding-right: 10px;\n  position: absolute;\n  right: 0;\n  top: 0;\n  max-width: 100%;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.Select-input {\n  height: 34px;\n  padding-left: 10px;\n  padding-right: 10px;\n  vertical-align: middle;\n}\n.Select-input > input {\n  width: 100%;\n  background: none transparent;\n  border: 0 none;\n  box-shadow: none;\n  cursor: default;\n  display: inline-block;\n  font-family: inherit;\n  font-size: inherit;\n  margin: 0;\n  outline: none;\n  line-height: 17px;\n  /* For IE 8 compatibility */\n  padding: 8px 0 12px;\n  /* For IE 8 compatibility */\n  -webkit-appearance: none;\n}\n.is-focused .Select-input > input {\n  cursor: text;\n}\n.has-value.is-pseudo-focused .Select-input {\n  opacity: 0;\n}\n.Select-control:not(.is-searchable) > .Select-input {\n  outline: none;\n}\n.Select-loading-zone {\n  cursor: pointer;\n  display: table-cell;\n  position: relative;\n  text-align: center;\n  vertical-align: middle;\n  width: 16px;\n}\n.Select-loading {\n  -webkit-animation: Select-animation-spin 400ms infinite linear;\n  -o-animation: Select-animation-spin 400ms infinite linear;\n  animation: Select-animation-spin 400ms infinite linear;\n  width: 16px;\n  height: 16px;\n  box-sizing: border-box;\n  border-radius: 50%;\n  border: 2px solid #ccc;\n  border-right-color: #333;\n  display: inline-block;\n  position: relative;\n  vertical-align: middle;\n}\n.Select-clear-zone {\n  -webkit-animation: Select-animation-fadeIn 200ms;\n  -o-animation: Select-animation-fadeIn 200ms;\n  animation: Select-animation-fadeIn 200ms;\n  color: #999;\n  cursor: pointer;\n  display: table-cell;\n  position: relative;\n  text-align: center;\n  vertical-align: middle;\n  width: 17px;\n}\n.Select-clear-zone:hover {\n  color: #D0021B;\n}\n.Select-clear {\n  display: inline-block;\n  font-size: 18px;\n  line-height: 1;\n}\n.Select--multi .Select-clear-zone {\n  width: 17px;\n}\n.Select-arrow-zone {\n  cursor: pointer;\n  display: table-cell;\n  position: relative;\n  text-align: center;\n  vertical-align: middle;\n  width: 25px;\n  padding-right: 5px;\n}\n.Select--rtl .Select-arrow-zone {\n  padding-right: 0;\n  padding-left: 5px;\n}\n.Select-arrow {\n  border-color: #999 transparent transparent;\n  border-style: solid;\n  border-width: 5px 5px 2.5px;\n  display: inline-block;\n  height: 0;\n  width: 0;\n  position: relative;\n}\n.Select-control > *:last-child {\n  padding-right: 5px;\n}\n.Select--multi .Select-multi-value-wrapper {\n  display: inline-block;\n}\n.Select .Select-aria-only {\n  position: absolute;\n  display: inline-block;\n  height: 1px;\n  width: 1px;\n  margin: -1px;\n  clip: rect(0, 0, 0, 0);\n  overflow: hidden;\n  float: left;\n}\n@-webkit-keyframes Select-animation-fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n@keyframes Select-animation-fadeIn {\n  from {\n    opacity: 0;\n  }\n  to {\n    opacity: 1;\n  }\n}\n.Select-menu-outer {\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n  background-color: #fff;\n  border: 1px solid #ccc;\n  border-top-color: #e6e6e6;\n  box-shadow: 0 1px 0 rgba(0, 0, 0, 0.06);\n  box-sizing: border-box;\n  margin-top: -1px;\n  max-height: 200px;\n  position: absolute;\n  left: 0;\n  top: 100%;\n  width: 100%;\n  z-index: 1;\n  -webkit-overflow-scrolling: touch;\n}\n.Select-menu {\n  max-height: 198px;\n  overflow-y: auto;\n}\n.Select-option {\n  box-sizing: border-box;\n  background-color: #fff;\n  color: #666666;\n  cursor: pointer;\n  display: block;\n  padding: 8px 10px;\n}\n.Select-option:last-child {\n  border-bottom-right-radius: 4px;\n  border-bottom-left-radius: 4px;\n}\n.Select-option.is-selected {\n  background-color: #f5faff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.04);\n  color: #333;\n}\n.Select-option.is-focused {\n  background-color: #ebf5ff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.08);\n  color: #333;\n}\n.Select-option.is-disabled {\n  color: #cccccc;\n  cursor: default;\n}\n.Select-noresults {\n  box-sizing: border-box;\n  color: #999999;\n  cursor: default;\n  display: block;\n  padding: 8px 10px;\n}\n.Select--multi .Select-input {\n  vertical-align: middle;\n  margin-left: 10px;\n  padding: 0;\n}\n.Select--multi.Select--rtl .Select-input {\n  margin-left: 0;\n  margin-right: 10px;\n}\n.Select--multi.has-value .Select-input {\n  margin-left: 5px;\n}\n.Select--multi .Select-value {\n  background-color: #ebf5ff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.08);\n  border-radius: 2px;\n  border: 1px solid #c2e0ff;\n  /* Fallback color for IE 8 */\n  border: 1px solid rgba(0, 126, 255, 0.24);\n  color: #007eff;\n  display: inline-block;\n  font-size: 0.9em;\n  line-height: 1.4;\n  margin-left: 5px;\n  margin-top: 5px;\n  vertical-align: top;\n}\n.Select--multi .Select-value-icon,\n.Select--multi .Select-value-label {\n  display: inline-block;\n  vertical-align: middle;\n}\n.Select--multi .Select-value-label {\n  border-bottom-right-radius: 2px;\n  border-top-right-radius: 2px;\n  cursor: default;\n  padding: 2px 5px;\n}\n.Select--multi a.Select-value-label {\n  color: #007eff;\n  cursor: pointer;\n  text-decoration: none;\n}\n.Select--multi a.Select-value-label:hover {\n  text-decoration: underline;\n}\n.Select--multi .Select-value-icon {\n  cursor: pointer;\n  border-bottom-left-radius: 2px;\n  border-top-left-radius: 2px;\n  border-right: 1px solid #c2e0ff;\n  /* Fallback color for IE 8 */\n  border-right: 1px solid rgba(0, 126, 255, 0.24);\n  padding: 1px 5px 3px;\n}\n.Select--multi .Select-value-icon:hover,\n.Select--multi .Select-value-icon:focus {\n  background-color: #d8eafd;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 113, 230, 0.08);\n  color: #0071e6;\n}\n.Select--multi .Select-value-icon:active {\n  background-color: #c2e0ff;\n  /* Fallback color for IE 8 */\n  background-color: rgba(0, 126, 255, 0.24);\n}\n.Select--multi.Select--rtl .Select-value {\n  margin-left: 0;\n  margin-right: 5px;\n}\n.Select--multi.Select--rtl .Select-value-icon {\n  border-right: none;\n  border-left: 1px solid #c2e0ff;\n  /* Fallback color for IE 8 */\n  border-left: 1px solid rgba(0, 126, 255, 0.24);\n}\n.Select--multi.is-disabled .Select-value {\n  background-color: #fcfcfc;\n  border: 1px solid #e3e3e3;\n  color: #333;\n}\n.Select--multi.is-disabled .Select-value-icon {\n  cursor: not-allowed;\n  border-right: 1px solid #e3e3e3;\n}\n.Select--multi.is-disabled .Select-value-icon:hover,\n.Select--multi.is-disabled .Select-value-icon:focus,\n.Select--multi.is-disabled .Select-value-icon:active {\n  background-color: #fcfcfc;\n}\n@keyframes Select-animation-spin {\n  to {\n    transform: rotate(1turn);\n  }\n}\n@-webkit-keyframes Select-animation-spin {\n  to {\n    -webkit-transform: rotate(1turn);\n  }\n}\n"],sourceRoot:""}]),t.Z=a},366:function(e,t,n){"use strict";var r=n(7537),o=n.n(r),i=n(3645),a=n.n(i)()(o());a.push([e.id,".dash-spreadsheet .Select,\n.dash-spreadsheet .Select-control {\n    border: none;\n    cursor: pointer;\n    height: 30px;  /* matches the td height and line-height */\n    border: none;\n}\n\n.dash-spreadsheet .Select-placeholder,\n.dash-spreadsheet .Select--single > .Select-control .Select-value {\n    height: 100%;\n    line-height: inherit;\n}\n\n.dash-spreadsheet .Select.has-value.Select--single > .Select-control .Select-value .Select-value-label,\n.dash-spreadsheet .Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value .Select-value-label {\n    color: var(--text-color);\n}\n\n.dash-spreadsheet .Select:hover,\n.dash-spreadsheet .Select-control:hover {\n    border: none;\n    box-shadow: none;\n    cursor: pointer;\n}\n\n.dash-spreadsheet .Select-menu-outer {\n    z-index: 100;\n}\n\n.dash-spreadsheet .Select-arrow {\n    border-top-color: var(--faded-dropdown);\n    border-left-color: transparent;\n    border-right-color: transparent;\n    border-bottom-color: transparent;\n}\n\n.dash-spreadsheet .Select-control:hover .Select-arrow {\n    border-top-color: var(--accent);\n    border-left-color: transparent;\n    border-right-color: transparent;\n    border-bottom-color: transparent;\n}\n\n.dash-spreadsheet .is-open > .Select-control .Select-arrow {\n    border-bottom-color: var(--background-color-8-react-select);\n    border-left-color: transparent;\n    border-right-color: transparent;\n    border-top-color: transparent;\n}\n\n.dash-spreadsheet .Select--multi .Select-value,\n.dash-spreadsheet .Select--multi a.Select-value-label {\n    color: var(--color-8-react-select);\n}\n\n.dash-spreadsheet .Select {\n    border-radius: 0;\n}\n\n.dash-spreadsheet .Select.is-disabled > .Select-control {\n    border: none;\n}\n\n.dash-spreadsheet .Select--multi .Select-value-icon:hover,\n.dash-spreadsheet .Select--multi .Select-value-icon:focus {\n    font-weight: bold;\n}\n\n.dash-spreadsheet .Select.is-disabled .Select-control {\n    cursor: not-allowed;\n}\n\n.dash-spreadsheet .Select-menu-outer {\n    background-color: white;\n    border: none;\n    border-bottom: thin var(--border) solid;\n    border-radius: 0;\n    border-top: none;\n}\n\n.dash-spreadsheet .Select-option {\n    background-color: inherit;\n    color: var(--accent);\n}\n\n.dash-spreadsheet .Select-option:hover {\n    font-weight: bold;\n    color: var(--accent);\n    background-color: var(--hover);\n}\n\n.dash-spreadsheet .Select.is-focused:not(.is-open) > .Select-control {\n    border: none;\n    -webkit-box-shadow: none;\n    box-shadow: none;\n    background: #fff;\n}\n\n.dash-spreadsheet .Select-option.is-focused {\n    background-color: white;\n    color: var(--accent);\n    font-weight: bold;\n}\n\n.dash-spreadsheet .Select-clear {\n    color: transparent;\n}\n.dash-spreadsheet .Select:hover .Select-clear {\n    color: var(--accent);\n}\n\n.dash-spreadsheet .Select-control {\n    padding-left: 2px;\n}\n","",{version:3,sources:["webpack://./src/dash-table/components/Table/Dropdown.css"],names:[],mappings:"AAAA;;IAEI,YAAY;IACZ,eAAe;IACf,YAAY,GAAG,0CAA0C;IACzD,YAAY;AAChB;;AAEA;;IAEI,YAAY;IACZ,oBAAoB;AACxB;;AAEA;;IAEI,wBAAwB;AAC5B;;AAEA;;IAEI,YAAY;IACZ,gBAAgB;IAChB,eAAe;AACnB;;AAEA;IACI,YAAY;AAChB;;AAEA;IACI,uCAAuC;IACvC,8BAA8B;IAC9B,+BAA+B;IAC/B,gCAAgC;AACpC;;AAEA;IACI,+BAA+B;IAC/B,8BAA8B;IAC9B,+BAA+B;IAC/B,gCAAgC;AACpC;;AAEA;IACI,2DAA2D;IAC3D,8BAA8B;IAC9B,+BAA+B;IAC/B,6BAA6B;AACjC;;AAEA;;IAEI,kCAAkC;AACtC;;AAEA;IACI,gBAAgB;AACpB;;AAEA;IACI,YAAY;AAChB;;AAEA;;IAEI,iBAAiB;AACrB;;AAEA;IACI,mBAAmB;AACvB;;AAEA;IACI,uBAAuB;IACvB,YAAY;IACZ,uCAAuC;IACvC,gBAAgB;IAChB,gBAAgB;AACpB;;AAEA;IACI,yBAAyB;IACzB,oBAAoB;AACxB;;AAEA;IACI,iBAAiB;IACjB,oBAAoB;IACpB,8BAA8B;AAClC;;AAEA;IACI,YAAY;IACZ,wBAAwB;IACxB,gBAAgB;IAChB,gBAAgB;AACpB;;AAEA;IACI,uBAAuB;IACvB,oBAAoB;IACpB,iBAAiB;AACrB;;AAEA;IACI,kBAAkB;AACtB;AACA;IACI,oBAAoB;AACxB;;AAEA;IACI,iBAAiB;AACrB",sourcesContent:[".dash-spreadsheet .Select,\n.dash-spreadsheet .Select-control {\n    border: none;\n    cursor: pointer;\n    height: 30px;  /* matches the td height and line-height */\n    border: none;\n}\n\n.dash-spreadsheet .Select-placeholder,\n.dash-spreadsheet .Select--single > .Select-control .Select-value {\n    height: 100%;\n    line-height: inherit;\n}\n\n.dash-spreadsheet .Select.has-value.Select--single > .Select-control .Select-value .Select-value-label,\n.dash-spreadsheet .Select.has-value.is-pseudo-focused.Select--single > .Select-control .Select-value .Select-value-label {\n    color: var(--text-color);\n}\n\n.dash-spreadsheet .Select:hover,\n.dash-spreadsheet .Select-control:hover {\n    border: none;\n    box-shadow: none;\n    cursor: pointer;\n}\n\n.dash-spreadsheet .Select-menu-outer {\n    z-index: 100;\n}\n\n.dash-spreadsheet .Select-arrow {\n    border-top-color: var(--faded-dropdown);\n    border-left-color: transparent;\n    border-right-color: transparent;\n    border-bottom-color: transparent;\n}\n\n.dash-spreadsheet .Select-control:hover .Select-arrow {\n    border-top-color: var(--accent);\n    border-left-color: transparent;\n    border-right-color: transparent;\n    border-bottom-color: transparent;\n}\n\n.dash-spreadsheet .is-open > .Select-control .Select-arrow {\n    border-bottom-color: var(--background-color-8-react-select);\n    border-left-color: transparent;\n    border-right-color: transparent;\n    border-top-color: transparent;\n}\n\n.dash-spreadsheet .Select--multi .Select-value,\n.dash-spreadsheet .Select--multi a.Select-value-label {\n    color: var(--color-8-react-select);\n}\n\n.dash-spreadsheet .Select {\n    border-radius: 0;\n}\n\n.dash-spreadsheet .Select.is-disabled > .Select-control {\n    border: none;\n}\n\n.dash-spreadsheet .Select--multi .Select-value-icon:hover,\n.dash-spreadsheet .Select--multi .Select-value-icon:focus {\n    font-weight: bold;\n}\n\n.dash-spreadsheet .Select.is-disabled .Select-control {\n    cursor: not-allowed;\n}\n\n.dash-spreadsheet .Select-menu-outer {\n    background-color: white;\n    border: none;\n    border-bottom: thin var(--border) solid;\n    border-radius: 0;\n    border-top: none;\n}\n\n.dash-spreadsheet .Select-option {\n    background-color: inherit;\n    color: var(--accent);\n}\n\n.dash-spreadsheet .Select-option:hover {\n    font-weight: bold;\n    color: var(--accent);\n    background-color: var(--hover);\n}\n\n.dash-spreadsheet .Select.is-focused:not(.is-open) > .Select-control {\n    border: none;\n    -webkit-box-shadow: none;\n    box-shadow: none;\n    background: #fff;\n}\n\n.dash-spreadsheet .Select-option.is-focused {\n    background-color: white;\n    color: var(--accent);\n    font-weight: bold;\n}\n\n.dash-spreadsheet .Select-clear {\n    color: transparent;\n}\n.dash-spreadsheet .Select:hover .Select-clear {\n    color: var(--accent);\n}\n\n.dash-spreadsheet .Select-control {\n    padding-left: 2px;\n}\n"],sourceRoot:""}]),t.Z=a},5924:function(e,t,n){"use strict";var r=n(1244);e.exports=function(e){var t=typeof e;if("string"===t){var n=e;if(0==(e=+e)&&r(n))return!1}else if("number"!==t)return!1;return e-e<1}},1244:function(e){"use strict";e.exports=function(e){for(var t,n=e.length,r=0;r<n;r++)if(((t=e.charCodeAt(r))<9||t>13)&&32!==t&&133!==t&&160!==t&&5760!==t&&6158!==t&&(t<8192||t>8205)&&8232!==t&&8233!==t&&8239!==t&&8287!==t&&8288!==t&&12288!==t&&65279!==t)return!1;return!0}},5639:function(e,t,n){"use strict";var r=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},o=function(){function e(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}return function(t,n,r){return n&&e(t.prototype,n),r&&e(t,r),t}}(),i=n(9196),a=s(i),l=s(n(9064));function s(e){return e&&e.__esModule?e:{default:e}}var c={position:"absolute",top:0,left:0,visibility:"hidden",height:0,overflow:"scroll",whiteSpace:"pre"},u=["extraWidth","injectStyles","inputClassName","inputRef","inputStyle","minWidth","onAutosize","placeholderIsMinWidth"],p=function(e,t){t.style.fontSize=e.fontSize,t.style.fontFamily=e.fontFamily,t.style.fontWeight=e.fontWeight,t.style.fontStyle=e.fontStyle,t.style.letterSpacing=e.letterSpacing,t.style.textTransform=e.textTransform},d=!("undefined"==typeof window||!window.navigator)&&/MSIE |Trident\/|Edge\//.test(window.navigator.userAgent),f=function(){return d?"_"+Math.random().toString(36).substr(2,12):void 0},h=function(e){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t);var n=function(e,t){if(!e)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!t||"object"!=typeof t&&"function"!=typeof t?e:t}(this,(t.__proto__||Object.getPrototypeOf(t)).call(this,e));return n.inputRef=function(e){n.input=e,"function"==typeof n.props.inputRef&&n.props.inputRef(e)},n.placeHolderSizerRef=function(e){n.placeHolderSizer=e},n.sizerRef=function(e){n.sizer=e},n.state={inputWidth:e.minWidth,inputId:e.id||f()},n}return function(e,t){if("function"!=typeof t&&null!==t)throw new TypeError("Super expression must either be null or a function, not "+typeof t);e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}}),t&&(Object.setPrototypeOf?Object.setPrototypeOf(e,t):e.__proto__=t)}(t,e),o(t,[{key:"componentDidMount",value:function(){this.mounted=!0,this.copyInputStyles(),this.updateInputWidth()}},{key:"UNSAFE_componentWillReceiveProps",value:function(e){var t=e.id;t!==this.props.id&&this.setState({inputId:t||f()})}},{key:"componentDidUpdate",value:function(e,t){t.inputWidth!==this.state.inputWidth&&"function"==typeof this.props.onAutosize&&this.props.onAutosize(this.state.inputWidth),this.updateInputWidth()}},{key:"componentWillUnmount",value:function(){this.mounted=!1}},{key:"copyInputStyles",value:function(){if(this.mounted&&window.getComputedStyle){var e=this.input&&window.getComputedStyle(this.input);e&&(p(e,this.sizer),this.placeHolderSizer&&p(e,this.placeHolderSizer))}}},{key:"updateInputWidth",value:function(){if(this.mounted&&this.sizer&&void 0!==this.sizer.scrollWidth){var e=void 0;e=this.props.placeholder&&(!this.props.value||this.props.value&&this.props.placeholderIsMinWidth)?Math.max(this.sizer.scrollWidth,this.placeHolderSizer.scrollWidth)+2:this.sizer.scrollWidth+2,(e+="number"===this.props.type&&void 0===this.props.extraWidth?16:parseInt(this.props.extraWidth)||0)<this.props.minWidth&&(e=this.props.minWidth),e!==this.state.inputWidth&&this.setState({inputWidth:e})}}},{key:"getInput",value:function(){return this.input}},{key:"focus",value:function(){this.input.focus()}},{key:"blur",value:function(){this.input.blur()}},{key:"select",value:function(){this.input.select()}},{key:"renderStyles",value:function(){var e=this.props.injectStyles;return d&&e?a.default.createElement("style",{dangerouslySetInnerHTML:{__html:"input#"+this.state.inputId+"::-ms-clear {display: none;}"}}):null}},{key:"render",value:function(){var e=[this.props.defaultValue,this.props.value,""].reduce((function(e,t){return null!=e?e:t})),t=r({},this.props.style);t.display||(t.display="inline-block");var n=r({boxSizing:"content-box",width:this.state.inputWidth+"px"},this.props.inputStyle),o=function(e,t){var n={};for(var r in e)t.indexOf(r)>=0||Object.prototype.hasOwnProperty.call(e,r)&&(n[r]=e[r]);return n}(this.props,[]);return function(e){u.forEach((function(t){return delete e[t]}))}(o),o.className=this.props.inputClassName,o.id=this.state.inputId,o.style=n,a.default.createElement("div",{className:this.props.className,style:t},this.renderStyles(),a.default.createElement("input",r({},o,{ref:this.inputRef})),a.default.createElement("div",{ref:this.sizerRef,style:c},e),this.props.placeholder?a.default.createElement("div",{ref:this.placeHolderSizerRef,style:c},this.props.placeholder):null)}}]),t}(i.Component);h.propTypes={className:l.default.string,defaultValue:l.default.any,extraWidth:l.default.oneOfType([l.default.number,l.default.string]),id:l.default.string,injectStyles:l.default.bool,inputClassName:l.default.string,inputRef:l.default.func,inputStyle:l.default.object,minWidth:l.default.oneOfType([l.default.number,l.default.string]),onAutosize:l.default.func,onChange:l.default.func,placeholder:l.default.string,placeholderIsMinWidth:l.default.bool,style:l.default.object,value:l.default.any},h.defaultProps={minWidth:1,injectStyles:!0},t.Z=h},4490:function(e){!function(t){"use strict";function n(){}function r(e){return e.split('"').length-1}n.prototype=Object.create(Object.prototype,{parse:{value:function(e){var t,n,o,i,a,l,s,c=[],u=0;for((o=e.split("\n")).length>1&&""===o[o.length-1]&&o.pop(),t=0,n=o.length;t<n;t+=1){for(o[t]=o[t].split("\t"),i=0,a=o[t].length;i<a;i+=1)c[u]||(c[u]=[]),l&&0===i?(s=c[u].length-1,c[u][s]=c[u][s]+"\n"+o[t][0],l&&1&r(o[t][0])&&(l=!1,c[u][s]=c[u][s].substring(0,c[u][s].length-1).replace(/""/g,'"'))):i===a-1&&0===o[t][i].indexOf('"')&&1&r(o[t][i])?(c[u].push(o[t][i].substring(1).replace(/""/g,'"')),l=!0):(c[u].push(o[t][i].replace(/""/g,'"')),l=!1);l||(u+=1)}return c},enumerable:!0,configurable:!1,writable:!1},stringify:{value:function(e){var t,n,r,o,i,a="";for(t=0,n=e.length;t<n;t+=1){for(r=0,o=e[t].length;r<o;r+=1)r>0&&(a+="\t"),"string"==typeof(i=e[t][r])?i.indexOf("\n")>-1?a+='"'+i.replace(/"/g,'""')+'"':a+=i:a+=null==i?"":i;a+="\n"}return a},enumerable:!0,configurable:!1,writable:!1}}),e.exports?e.exports=n:t.SheetClip=n}(this)}}]);
//# sourceMappingURL=async-table.js.map