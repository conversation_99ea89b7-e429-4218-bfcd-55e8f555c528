{"version": 3, "file": "dash_core_components-shared.js", "mappings": ";mHAAA,OAOC,WACA,aAEA,IAAIA,EAAS,CAAC,EAAEC,eAGhB,SAASC,IAGR,IAFA,IAAIC,EAAU,GAELC,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CAC1C,IAAIG,EAAMF,UAAUD,GACpB,GAAKG,EAAL,CAEA,IAAIC,SAAiBD,EAErB,GAAgB,WAAZC,GAAoC,WAAZA,EAC3BL,EAAQM,KAAKF,QACP,GAAIG,MAAMC,QAAQJ,IACxB,GAAIA,EAAID,OAAQ,CACf,IAAIM,EAAQV,EAAWW,MAAM,KAAMN,GAC/BK,GACHT,EAAQM,KAAKG,EAEf,OACM,GAAgB,WAAZJ,EAAsB,CAChC,GAAID,EAAIO,WAAaC,OAAOC,UAAUF,WAAaP,EAAIO,SAASA,WAAWG,SAAS,iBAAkB,CACrGd,EAAQM,KAAKF,EAAIO,YACjB,QACD,CAEA,IAAK,IAAII,KAAOX,EACXP,EAAOmB,KAAKZ,EAAKW,IAAQX,EAAIW,IAChCf,EAAQM,KAAKS,EAGhB,CAxBkB,CAyBnB,CAEA,OAAOf,EAAQiB,KAAK,IACrB,CAEqCC,EAAOC,SAC3CpB,EAAWqB,QAAUrB,EACrBmB,EAAOC,QAAUpB,QAKhB,KAFwB,EAAF,WACtB,OAAOA,CACP,UAFoB,OAEpB,YAIH,CApDA,qCCEa,IAAIsB,EAAE,mBAAoBC,QAAQA,OAAOC,IAAIC,EAAEH,EAAEC,OAAOC,IAAI,iBAAiB,MAAME,EAAEJ,EAAEC,OAAOC,IAAI,gBAAgB,MAAMG,EAAEL,EAAEC,OAAOC,IAAI,kBAAkB,MAAMI,EAAEN,EAAEC,OAAOC,IAAI,qBAAqB,MAAMK,EAAEP,EAAEC,OAAOC,IAAI,kBAAkB,MAAMM,EAAER,EAAEC,OAAOC,IAAI,kBAAkB,MAAMO,EAAET,EAAEC,OAAOC,IAAI,iBAAiB,MAAMQ,EAAEV,EAAEC,OAAOC,IAAI,oBAAoB,MAAMS,EAAEX,EAAEC,OAAOC,IAAI,yBAAyB,MAAMU,EAAEZ,EAAEC,OAAOC,IAAI,qBAAqB,MAAMW,EAAEb,EAAEC,OAAOC,IAAI,kBAAkB,MAAMY,EAAEd,EACpfC,OAAOC,IAAI,uBAAuB,MAAMa,EAAEf,EAAEC,OAAOC,IAAI,cAAc,MAAMc,EAAEhB,EAAEC,OAAOC,IAAI,cAAc,MAAMe,EAAEjB,EAAEC,OAAOC,IAAI,eAAe,MAAMgB,EAAElB,EAAEC,OAAOC,IAAI,qBAAqB,MAAMiB,EAAEnB,EAAEC,OAAOC,IAAI,mBAAmB,MAAMkB,EAAEpB,EAAEC,OAAOC,IAAI,eAAe,MAClQ,SAASmB,EAAEC,GAAG,GAAG,iBAAkBA,GAAG,OAAOA,EAAE,CAAC,IAAIC,EAAED,EAAEE,SAAS,OAAOD,GAAG,KAAKpB,EAAE,OAAOmB,EAAEA,EAAEG,MAAQ,KAAKf,EAAE,KAAKC,EAAE,KAAKN,EAAE,KAAKE,EAAE,KAAKD,EAAE,KAAKO,EAAE,OAAOS,EAAE,QAAQ,OAAOA,EAAEA,GAAGA,EAAEE,UAAY,KAAKf,EAAE,KAAKG,EAAE,KAAKI,EAAE,KAAKD,EAAE,KAAKP,EAAE,OAAOc,EAAE,QAAQ,OAAOC,GAAG,KAAKnB,EAAE,OAAOmB,EAAE,CAAC,CAAC,SAASG,EAAEJ,GAAG,OAAOD,EAAEC,KAAKX,CAAC,CAACb,EAAQ6B,UAAUjB,EAAEZ,EAAQ8B,eAAejB,EAAEb,EAAQ+B,gBAAgBpB,EAAEX,EAAQgC,gBAAgBtB,EAAEV,EAAQiC,QAAQ5B,EAAEL,EAAQkC,WAAWpB,EAAEd,EAAQmC,SAAS5B,EAAEP,EAAQoC,KAAKlB,EAAElB,EAAQqC,KAAKpB,EAAEjB,EAAQsC,OAAOhC,EAChfN,EAAQuC,SAAS9B,EAAET,EAAQwC,WAAWhC,EAAER,EAAQyC,SAAS1B,EAAEf,EAAQ0C,YAAY,SAASlB,GAAG,OAAOI,EAAEJ,IAAID,EAAEC,KAAKZ,CAAC,EAAEZ,EAAQ2C,iBAAiBf,EAAE5B,EAAQ4C,kBAAkB,SAASpB,GAAG,OAAOD,EAAEC,KAAKb,CAAC,EAAEX,EAAQ6C,kBAAkB,SAASrB,GAAG,OAAOD,EAAEC,KAAKd,CAAC,EAAEV,EAAQ8C,UAAU,SAAStB,GAAG,MAAM,iBAAkBA,GAAG,OAAOA,GAAGA,EAAEE,WAAWrB,CAAC,EAAEL,EAAQ+C,aAAa,SAASvB,GAAG,OAAOD,EAAEC,KAAKV,CAAC,EAAEd,EAAQgD,WAAW,SAASxB,GAAG,OAAOD,EAAEC,KAAKjB,CAAC,EAAEP,EAAQiD,OAAO,SAASzB,GAAG,OAAOD,EAAEC,KAAKN,CAAC,EAC1dlB,EAAQkD,OAAO,SAAS1B,GAAG,OAAOD,EAAEC,KAAKP,CAAC,EAAEjB,EAAQmD,SAAS,SAAS3B,GAAG,OAAOD,EAAEC,KAAKlB,CAAC,EAAEN,EAAQoD,WAAW,SAAS5B,GAAG,OAAOD,EAAEC,KAAKf,CAAC,EAAET,EAAQqD,aAAa,SAAS7B,GAAG,OAAOD,EAAEC,KAAKhB,CAAC,EAAER,EAAQsD,WAAW,SAAS9B,GAAG,OAAOD,EAAEC,KAAKT,CAAC,EAC1Of,EAAQuD,mBAAmB,SAAS/B,GAAG,MAAM,iBAAkBA,GAAG,mBAAoBA,GAAGA,IAAIjB,GAAGiB,IAAIX,GAAGW,IAAIf,GAAGe,IAAIhB,GAAGgB,IAAIT,GAAGS,IAAIR,GAAG,iBAAkBQ,GAAG,OAAOA,IAAIA,EAAEE,WAAWR,GAAGM,EAAEE,WAAWT,GAAGO,EAAEE,WAAWhB,GAAGc,EAAEE,WAAWf,GAAGa,EAAEE,WAAWZ,GAAGU,EAAEE,WAAWN,GAAGI,EAAEE,WAAWL,GAAGG,EAAEE,WAAWJ,GAAGE,EAAEE,WAAWP,EAAE,EAAEnB,EAAQwD,OAAOjC,sCCXjUxB,EAAOC,QAAU,EAAjB,kDCIF,IAAIyD,EAAU,WACV,GAAmB,oBAARC,IACP,OAAOA,IASX,SAASC,EAASC,EAAKhE,GACnB,IAAIiE,GAAU,EAQd,OAPAD,EAAIE,MAAK,SAAUC,EAAOC,GACtB,OAAID,EAAM,KAAOnE,IACbiE,EAASG,GACF,EAGf,IACOH,CACX,CACA,OAAsB,WAClB,SAASI,IACLC,KAAKC,YAAc,EACvB,CAsEA,OArEA1E,OAAO2E,eAAeH,EAAQvE,UAAW,OAAQ,CAI7C2E,IAAK,WACD,OAAOH,KAAKC,YAAYnF,MAC5B,EACAsF,YAAY,EACZC,cAAc,IAMlBN,EAAQvE,UAAU2E,IAAM,SAAUzE,GAC9B,IAAIoE,EAAQL,EAASO,KAAKC,YAAavE,GACnCmE,EAAQG,KAAKC,YAAYH,GAC7B,OAAOD,GAASA,EAAM,EAC1B,EAMAE,EAAQvE,UAAU8E,IAAM,SAAU5E,EAAK6E,GACnC,IAAIT,EAAQL,EAASO,KAAKC,YAAavE,IAClCoE,EACDE,KAAKC,YAAYH,GAAO,GAAKS,EAG7BP,KAAKC,YAAYhF,KAAK,CAACS,EAAK6E,GAEpC,EAKAR,EAAQvE,UAAUgF,OAAS,SAAU9E,GACjC,IAAI+E,EAAUT,KAAKC,YACfH,EAAQL,EAASgB,EAAS/E,IACzBoE,GACDW,EAAQC,OAAOZ,EAAO,EAE9B,EAKAC,EAAQvE,UAAUmF,IAAM,SAAUjF,GAC9B,SAAU+D,EAASO,KAAKC,YAAavE,EACzC,EAIAqE,EAAQvE,UAAUoF,MAAQ,WACtBZ,KAAKC,YAAYS,OAAO,EAC5B,EAMAX,EAAQvE,UAAUqF,QAAU,SAAUC,EAAUC,QAChC,IAARA,IAAkBA,EAAM,MAC5B,IAAK,IAAIC,EAAK,EAAGC,EAAKjB,KAAKC,YAAae,EAAKC,EAAGnG,OAAQkG,IAAM,CAC1D,IAAInB,EAAQoB,EAAGD,GACfF,EAASnF,KAAKoF,EAAKlB,EAAM,GAAIA,EAAM,GACvC,CACJ,EACOE,CACX,CA1EqB,EA2ExB,CAjGa,GAsGVmB,EAA8B,oBAAXC,QAA8C,oBAAbC,UAA4BD,OAAOC,WAAaA,SAGpGC,OACsB,IAAX,EAAA9E,GAA0B,EAAAA,EAAO+E,OAASA,KAC1C,EAAA/E,EAES,oBAATgF,MAAwBA,KAAKD,OAASA,KACtCC,KAEW,oBAAXJ,QAA0BA,OAAOG,OAASA,KAC1CH,OAGJK,SAAS,cAATA,GASPC,EACqC,mBAA1BC,sBAIAA,sBAAsBC,KAAKN,GAE/B,SAAUP,GAAY,OAAOc,YAAW,WAAc,OAAOd,EAASe,KAAKC,MAAQ,GAAG,IAAO,GAAK,EAwEzGC,EAAiB,CAAC,MAAO,QAAS,SAAU,OAAQ,QAAS,SAAU,OAAQ,UAE/EC,EAAwD,oBAArBC,iBAInCC,EAA0C,WAM1C,SAASA,IAMLlC,KAAKmC,YAAa,EAMlBnC,KAAKoC,sBAAuB,EAM5BpC,KAAKqC,mBAAqB,KAM1BrC,KAAKsC,WAAa,GAClBtC,KAAKuC,iBAAmBvC,KAAKuC,iBAAiBZ,KAAK3B,MACnDA,KAAKwC,QAjGb,SAAmB1B,EAAU2B,GACzB,IAAIC,GAAc,EAAOC,GAAe,EAAOC,EAAe,EAO9D,SAASC,IACDH,IACAA,GAAc,EACd5B,KAEA6B,GACAG,GAER,CAQA,SAASC,IACLtB,EAAwBoB,EAC5B,CAMA,SAASC,IACL,IAAIE,EAAYnB,KAAKC,MACrB,GAAIY,EAAa,CAEb,GAAIM,EAAYJ,EA7CN,EA8CN,OAMJD,GAAe,CACnB,MAEID,GAAc,EACdC,GAAe,EACff,WAAWmB,EAAiBN,GAEhCG,EAAeI,CACnB,CACA,OAAOF,CACX,CA4CuBG,CAASjD,KAAKwC,QAAQb,KAAK3B,MAzC9B,GA0ChB,CA+JA,OAxJAkC,EAAyB1G,UAAU0H,YAAc,SAAUC,IACjDnD,KAAKsC,WAAWc,QAAQD,IAC1BnD,KAAKsC,WAAWrH,KAAKkI,GAGpBnD,KAAKmC,YACNnC,KAAKqD,UAEb,EAOAnB,EAAyB1G,UAAU8H,eAAiB,SAAUH,GAC1D,IAAII,EAAYvD,KAAKsC,WACjBxC,EAAQyD,EAAUH,QAAQD,IAEzBrD,GACDyD,EAAU7C,OAAOZ,EAAO,IAGvByD,EAAUzI,QAAUkF,KAAKmC,YAC1BnC,KAAKwD,aAEb,EAOAtB,EAAyB1G,UAAUgH,QAAU,WACnBxC,KAAKyD,oBAIvBzD,KAAKwC,SAEb,EASAN,EAAyB1G,UAAUiI,iBAAmB,WAElD,IAAIC,EAAkB1D,KAAKsC,WAAWqB,QAAO,SAAUR,GACnD,OAAOA,EAASS,eAAgBT,EAASU,WAC7C,IAOA,OADAH,EAAgB7C,SAAQ,SAAUsC,GAAY,OAAOA,EAASW,iBAAmB,IAC1EJ,EAAgB5I,OAAS,CACpC,EAOAoH,EAAyB1G,UAAU6H,SAAW,WAGrCnC,IAAalB,KAAKmC,aAMvBf,SAAS2C,iBAAiB,gBAAiB/D,KAAKuC,kBAChDpB,OAAO4C,iBAAiB,SAAU/D,KAAKwC,SACnCR,GACAhC,KAAKqC,mBAAqB,IAAIJ,iBAAiBjC,KAAKwC,SACpDxC,KAAKqC,mBAAmB2B,QAAQ5C,SAAU,CACtC6C,YAAY,EACZC,WAAW,EACXC,eAAe,EACfC,SAAS,MAIbhD,SAAS2C,iBAAiB,qBAAsB/D,KAAKwC,SACrDxC,KAAKoC,sBAAuB,GAEhCpC,KAAKmC,YAAa,EACtB,EAOAD,EAAyB1G,UAAUgI,YAAc,WAGxCtC,GAAclB,KAAKmC,aAGxBf,SAASiD,oBAAoB,gBAAiBrE,KAAKuC,kBACnDpB,OAAOkD,oBAAoB,SAAUrE,KAAKwC,SACtCxC,KAAKqC,oBACLrC,KAAKqC,mBAAmBiC,aAExBtE,KAAKoC,sBACLhB,SAASiD,oBAAoB,qBAAsBrE,KAAKwC,SAE5DxC,KAAKqC,mBAAqB,KAC1BrC,KAAKoC,sBAAuB,EAC5BpC,KAAKmC,YAAa,EACtB,EAQAD,EAAyB1G,UAAU+G,iBAAmB,SAAUtB,GAC5D,IAAIsD,EAAKtD,EAAGuD,aAAcA,OAAsB,IAAPD,EAAgB,GAAKA,EAEvCxC,EAAenC,MAAK,SAAUlE,GACjD,SAAU8I,EAAapB,QAAQ1H,EACnC,KAEIsE,KAAKwC,SAEb,EAMAN,EAAyBuC,YAAc,WAInC,OAHKzE,KAAK0E,YACN1E,KAAK0E,UAAY,IAAIxC,GAElBlC,KAAK0E,SAChB,EAMAxC,EAAyBwC,UAAY,KAC9BxC,CACX,CAjM6C,GA0MzCyC,EAAqB,SAAWC,EAAQC,GACxC,IAAK,IAAI7D,EAAK,EAAGC,EAAK1F,OAAOuJ,KAAKD,GAAQ7D,EAAKC,EAAGnG,OAAQkG,IAAM,CAC5D,IAAItF,EAAMuF,EAAGD,GACbzF,OAAO2E,eAAe0E,EAAQlJ,EAAK,CAC/B6E,MAAOsE,EAAMnJ,GACb0E,YAAY,EACZ2E,UAAU,EACV1E,cAAc,GAEtB,CACA,OAAOuE,CACV,EAQGI,EAAc,SAAWJ,GAOzB,OAHkBA,GAAUA,EAAOK,eAAiBL,EAAOK,cAAcC,aAGnD7D,CACzB,EAGG8D,EAAYC,EAAe,EAAG,EAAG,EAAG,GAOxC,SAASC,EAAQ9E,GACb,OAAO+E,WAAW/E,IAAU,CAChC,CAQA,SAASgF,EAAeC,GAEpB,IADA,IAAIC,EAAY,GACPzE,EAAK,EAAGA,EAAKnG,UAAUC,OAAQkG,IACpCyE,EAAUzE,EAAK,GAAKnG,UAAUmG,GAElC,OAAOyE,EAAUC,QAAO,SAAUC,EAAMC,GAEpC,OAAOD,EAAON,EADFG,EAAO,UAAYI,EAAW,UAE9C,GAAG,EACP,CAyGA,IAAIC,EAGkC,oBAAvBC,mBACA,SAAUlB,GAAU,OAAOA,aAAkBI,EAAYJ,GAAQkB,kBAAoB,EAKzF,SAAUlB,GAAU,OAAQA,aAAkBI,EAAYJ,GAAQmB,YAC3C,mBAAnBnB,EAAOoB,OAAyB,EAiB/C,SAASC,EAAerB,GACpB,OAAK1D,EAGD2E,EAAqBjB,GAhH7B,SAA2BA,GACvB,IAAIsB,EAAOtB,EAAOoB,UAClB,OAAOZ,EAAe,EAAG,EAAGc,EAAKC,MAAOD,EAAKE,OACjD,CA8GeC,CAAkBzB,GAvGjC,SAAmCA,GAG/B,IAAI0B,EAAc1B,EAAO0B,YAAaC,EAAe3B,EAAO2B,aAS5D,IAAKD,IAAgBC,EACjB,OAAOpB,EAEX,IAAIK,EAASR,EAAYJ,GAAQ4B,iBAAiB5B,GAC9C6B,EA3CR,SAAqBjB,GAGjB,IAFA,IACIiB,EAAW,CAAC,EACPzF,EAAK,EAAG0F,EAFD,CAAC,MAAO,QAAS,SAAU,QAED1F,EAAK0F,EAAY5L,OAAQkG,IAAM,CACrE,IAAI4E,EAAWc,EAAY1F,GACvBT,EAAQiF,EAAO,WAAaI,GAChCa,EAASb,GAAYP,EAAQ9E,EACjC,CACA,OAAOkG,CACX,CAkCmBE,CAAYnB,GACvBoB,EAAWH,EAASI,KAAOJ,EAASK,MACpCC,EAAUN,EAASO,IAAMP,EAASQ,OAKlCd,EAAQd,EAAQG,EAAOW,OAAQC,EAASf,EAAQG,EAAOY,QAqB3D,GAlByB,eAArBZ,EAAO0B,YAOH5F,KAAK6F,MAAMhB,EAAQS,KAAcN,IACjCH,GAASZ,EAAeC,EAAQ,OAAQ,SAAWoB,GAEnDtF,KAAK6F,MAAMf,EAASW,KAAaR,IACjCH,GAAUb,EAAeC,EAAQ,MAAO,UAAYuB,KAoDhE,SAA2BnC,GACvB,OAAOA,IAAWI,EAAYJ,GAAQxD,SAASgG,eACnD,CA/CSC,CAAkBzC,GAAS,CAK5B,IAAI0C,EAAgBhG,KAAK6F,MAAMhB,EAAQS,GAAYN,EAC/CiB,EAAiBjG,KAAK6F,MAAMf,EAASW,GAAWR,EAMpB,IAA5BjF,KAAKkG,IAAIF,KACTnB,GAASmB,GAEoB,IAA7BhG,KAAKkG,IAAID,KACTnB,GAAUmB,EAElB,CACA,OAAOnC,EAAeqB,EAASI,KAAMJ,EAASO,IAAKb,EAAOC,EAC9D,CAyCWqB,CAA0B7C,GALtBO,CAMf,CAiCA,SAASC,EAAejI,EAAGC,EAAG+I,EAAOC,GACjC,MAAO,CAAEjJ,EAAGA,EAAGC,EAAGA,EAAG+I,MAAOA,EAAOC,OAAQA,EAC/C,CAMA,IAAIsB,EAAmC,WAMnC,SAASA,EAAkB9C,GAMvB5E,KAAK2H,eAAiB,EAMtB3H,KAAK4H,gBAAkB,EAMvB5H,KAAK6H,aAAezC,EAAe,EAAG,EAAG,EAAG,GAC5CpF,KAAK4E,OAASA,CAClB,CAyBA,OAlBA8C,EAAkBlM,UAAUsM,SAAW,WACnC,IAAIC,EAAO9B,EAAejG,KAAK4E,QAE/B,OADA5E,KAAK6H,aAAeE,EACZA,EAAK5B,QAAUnG,KAAK2H,gBACxBI,EAAK3B,SAAWpG,KAAK4H,eAC7B,EAOAF,EAAkBlM,UAAUwM,cAAgB,WACxC,IAAID,EAAO/H,KAAK6H,aAGhB,OAFA7H,KAAK2H,eAAiBI,EAAK5B,MAC3BnG,KAAK4H,gBAAkBG,EAAK3B,OACrB2B,CACX,EACOL,CACX,CApDsC,GAsDlCO,EAOA,SAA6BrD,EAAQsD,GACjC,IA/FoBjH,EACpB9D,EAAUC,EAAU+I,EAAkBC,EAEtC+B,EACAJ,EA2FIK,GA9FJjL,GADoB8D,EA+FiBiH,GA9F9B/K,EAAGC,EAAI6D,EAAG7D,EAAG+I,EAAQlF,EAAGkF,MAAOC,EAASnF,EAAGmF,OAElD+B,EAAoC,oBAApBE,gBAAkCA,gBAAkB9M,OACpEwM,EAAOxM,OAAO+M,OAAOH,EAAO3M,WAEhCmJ,EAAmBoD,EAAM,CACrB5K,EAAGA,EAAGC,EAAGA,EAAG+I,MAAOA,EAAOC,OAAQA,EAClCY,IAAK5J,EACL0J,MAAO3J,EAAIgJ,EACXc,OAAQb,EAAShJ,EACjByJ,KAAM1J,IAEH4K,GAyFHpD,EAAmB3E,KAAM,CAAE4E,OAAQA,EAAQwD,YAAaA,GAC5D,EAIAG,EAAmC,WAWnC,SAASA,EAAkBzH,EAAU0H,EAAYC,GAc7C,GAPAzI,KAAK0I,oBAAsB,GAM3B1I,KAAK2I,cAAgB,IAAIpJ,EACD,mBAAbuB,EACP,MAAM,IAAI8H,UAAU,2DAExB5I,KAAK6I,UAAY/H,EACjBd,KAAK8I,YAAcN,EACnBxI,KAAK+I,aAAeN,CACxB,CAmHA,OA5GAF,EAAkB/M,UAAUwI,QAAU,SAAUY,GAC5C,IAAK/J,UAAUC,OACX,MAAM,IAAI8N,UAAU,4CAGxB,GAAuB,oBAAZ7K,SAA6BA,mBAAmBxC,OAA3D,CAGA,KAAMqJ,aAAkBI,EAAYJ,GAAQ7G,SACxC,MAAM,IAAI6K,UAAU,yCAExB,IAAII,EAAehJ,KAAK2I,cAEpBK,EAAarI,IAAIiE,KAGrBoE,EAAa1I,IAAIsE,EAAQ,IAAI8C,EAAkB9C,IAC/C5E,KAAK8I,YAAY5F,YAAYlD,MAE7BA,KAAK8I,YAAYtG,UAZjB,CAaJ,EAOA+F,EAAkB/M,UAAUyN,UAAY,SAAUrE,GAC9C,IAAK/J,UAAUC,OACX,MAAM,IAAI8N,UAAU,4CAGxB,GAAuB,oBAAZ7K,SAA6BA,mBAAmBxC,OAA3D,CAGA,KAAMqJ,aAAkBI,EAAYJ,GAAQ7G,SACxC,MAAM,IAAI6K,UAAU,yCAExB,IAAII,EAAehJ,KAAK2I,cAEnBK,EAAarI,IAAIiE,KAGtBoE,EAAaxI,OAAOoE,GACfoE,EAAarD,MACd3F,KAAK8I,YAAYxF,eAAetD,MAXpC,CAaJ,EAMAuI,EAAkB/M,UAAU8I,WAAa,WACrCtE,KAAKkJ,cACLlJ,KAAK2I,cAAc/H,QACnBZ,KAAK8I,YAAYxF,eAAetD,KACpC,EAOAuI,EAAkB/M,UAAUoI,aAAe,WACvC,IAAIuF,EAAQnJ,KACZA,KAAKkJ,cACLlJ,KAAK2I,cAAc9H,SAAQ,SAAUuI,GAC7BA,EAAYtB,YACZqB,EAAMT,oBAAoBzN,KAAKmO,EAEvC,GACJ,EAOAb,EAAkB/M,UAAUsI,gBAAkB,WAE1C,GAAK9D,KAAK6D,YAAV,CAGA,IAAI9C,EAAMf,KAAK+I,aAEXtI,EAAUT,KAAK0I,oBAAoBW,KAAI,SAAUD,GACjD,OAAO,IAAInB,EAAoBmB,EAAYxE,OAAQwE,EAAYpB,gBACnE,IACAhI,KAAK6I,UAAUlN,KAAKoF,EAAKN,EAASM,GAClCf,KAAKkJ,aAPL,CAQJ,EAMAX,EAAkB/M,UAAU0N,YAAc,WACtClJ,KAAK0I,oBAAoBhI,OAAO,EACpC,EAMA6H,EAAkB/M,UAAUqI,UAAY,WACpC,OAAO7D,KAAK0I,oBAAoB5N,OAAS,CAC7C,EACOyN,CACX,CAnJsC,GAwJlChF,EAA+B,oBAAZ+F,QAA0B,IAAIA,QAAY,IAAI/J,EAKjEgK,EAOA,SAASA,EAAezI,GACpB,KAAMd,gBAAgBuJ,GAClB,MAAM,IAAIX,UAAU,sCAExB,IAAK/N,UAAUC,OACX,MAAM,IAAI8N,UAAU,4CAExB,IAAIJ,EAAatG,EAAyBuC,cACtCtB,EAAW,IAAIoF,EAAkBzH,EAAU0H,EAAYxI,MAC3DuD,EAAUjD,IAAIN,KAAMmD,EACxB,EAIJ,CACI,UACA,YACA,cACFtC,SAAQ,SAAU2I,GAChBD,EAAe/N,UAAUgO,GAAU,WAC/B,IAAIvI,EACJ,OAAQA,EAAKsC,EAAUpD,IAAIH,OAAOwJ,GAAQnO,MAAM4F,EAAIpG,UACxD,CACJ,IAEA,IAAIiF,OAEuC,IAA5BuB,EAASkI,eACTlI,EAASkI,eAEbA,EAGX,+BCz5BA1N,EAAOC,QANP,SAAgCyF,GAC9B,QAAa,IAATA,EACF,MAAM,IAAIkI,eAAe,6DAE3B,OAAOlI,CACT,EACyC1F,EAAOC,QAAQ4N,YAAa,EAAM7N,EAAOC,QAAiB,QAAID,EAAOC,+BCN9G,IAAI6N,EAAgB,EAAQ,OAe5B9N,EAAOC,QAdP,SAAyB8N,EAAKlO,EAAK6E,GAYjC,OAXA7E,EAAMiO,EAAcjO,MACTkO,EACTrO,OAAO2E,eAAe0J,EAAKlO,EAAK,CAC9B6E,MAAOA,EACPH,YAAY,EACZC,cAAc,EACd0E,UAAU,IAGZ6E,EAAIlO,GAAO6E,EAENqJ,CACT,EACkC/N,EAAOC,QAAQ4N,YAAa,EAAM7N,EAAOC,QAAiB,QAAID,EAAOC,2BCfvG,SAAS+N,IAYP,OAXAhO,EAAOC,QAAU+N,EAAWtO,OAAOuO,OAASvO,OAAOuO,OAAOnI,OAAS,SAAUiD,GAC3E,IAAK,IAAIhK,EAAI,EAAGA,EAAIC,UAAUC,OAAQF,IAAK,CACzC,IAAImP,EAASlP,UAAUD,GACvB,IAAK,IAAIc,KAAOqO,EACVxO,OAAOC,UAAUf,eAAekB,KAAKoO,EAAQrO,KAC/CkJ,EAAOlJ,GAAOqO,EAAOrO,GAG3B,CACA,OAAOkJ,CACT,EAAG/I,EAAOC,QAAQ4N,YAAa,EAAM7N,EAAOC,QAAiB,QAAID,EAAOC,QACjE+N,EAASxO,MAAM2E,KAAMnF,UAC9B,CACAgB,EAAOC,QAAU+N,EAAUhO,EAAOC,QAAQ4N,YAAa,EAAM7N,EAAOC,QAAiB,QAAID,EAAOC,2BCThGD,EAAOC,QALP,SAAgC8N,GAC9B,OAAOA,GAAOA,EAAIF,WAAaE,EAAM,CACnC,QAAWA,EAEf,EACyC/N,EAAOC,QAAQ4N,YAAa,EAAM7N,EAAOC,QAAiB,QAAID,EAAOC,+BCL9G,IAAIkO,EAAU,iBACd,SAASC,EAAyBC,GAChC,GAAuB,mBAAZZ,QAAwB,OAAO,KAC1C,IAAIa,EAAoB,IAAIb,QACxBc,EAAmB,IAAId,QAC3B,OAAQW,EAA2B,SAAkCC,GACnE,OAAOA,EAAcE,EAAmBD,CAC1C,GAAGD,EACL,CAgCArO,EAAOC,QA/BP,SAAiC8N,EAAKM,GACpC,IAAKA,GAAeN,GAAOA,EAAIF,WAC7B,OAAOE,EAET,GAAY,OAARA,GAAiC,WAAjBI,EAAQJ,IAAoC,mBAARA,EACtD,MAAO,CACL,QAAWA,GAGf,IAAIS,EAAQJ,EAAyBC,GACrC,GAAIG,GAASA,EAAM1J,IAAIiJ,GACrB,OAAOS,EAAMlK,IAAIyJ,GAEnB,IAAIU,EAAS,CAAC,EACVC,EAAwBhP,OAAO2E,gBAAkB3E,OAAOiP,yBAC5D,IAAK,IAAI9O,KAAOkO,EACd,GAAY,YAARlO,GAAqBH,OAAOC,UAAUf,eAAekB,KAAKiO,EAAKlO,GAAM,CACvE,IAAI+O,EAAOF,EAAwBhP,OAAOiP,yBAAyBZ,EAAKlO,GAAO,KAC3E+O,IAASA,EAAKtK,KAAOsK,EAAKnK,KAC5B/E,OAAO2E,eAAeoK,EAAQ5O,EAAK+O,GAEnCH,EAAO5O,GAAOkO,EAAIlO,EAEtB,CAMF,OAJA4O,EAAgB,QAAIV,EAChBS,GACFA,EAAM/J,IAAIsJ,EAAKU,GAEVA,CACT,EAC0CzO,EAAOC,QAAQ4N,YAAa,EAAM7N,EAAOC,QAAiB,QAAID,EAAOC,+BCxC/G,IAAI4O,EAA+B,EAAQ,MAgB3C7O,EAAOC,QAfP,SAAkCiO,EAAQY,GACxC,GAAc,MAAVZ,EAAgB,MAAO,CAAC,EAC5B,IACIrO,EAAKd,EADLgK,EAAS8F,EAA6BX,EAAQY,GAElD,GAAIpP,OAAOqP,sBAAuB,CAChC,IAAIC,EAAmBtP,OAAOqP,sBAAsBb,GACpD,IAAKnP,EAAI,EAAGA,EAAIiQ,EAAiB/P,OAAQF,IACvCc,EAAMmP,EAAiBjQ,GACnB+P,EAASvH,QAAQ1H,IAAQ,GACxBH,OAAOC,UAAUsP,qBAAqBnP,KAAKoO,EAAQrO,KACxDkJ,EAAOlJ,GAAOqO,EAAOrO,GAEzB,CACA,OAAOkJ,CACT,EAC2C/I,EAAOC,QAAQ4N,YAAa,EAAM7N,EAAOC,QAAiB,QAAID,EAAOC,0BCJhHD,EAAOC,QAZP,SAAuCiO,EAAQY,GAC7C,GAAc,MAAVZ,EAAgB,MAAO,CAAC,EAC5B,IAEIrO,EAAKd,EAFLgK,EAAS,CAAC,EACVmG,EAAaxP,OAAOuJ,KAAKiF,GAE7B,IAAKnP,EAAI,EAAGA,EAAImQ,EAAWjQ,OAAQF,IACjCc,EAAMqP,EAAWnQ,GACb+P,EAASvH,QAAQ1H,IAAQ,IAC7BkJ,EAAOlJ,GAAOqO,EAAOrO,IAEvB,OAAOkJ,CACT,EACgD/I,EAAOC,QAAQ4N,YAAa,EAAM7N,EAAOC,QAAiB,QAAID,EAAOC,0BCZrH,SAASkP,EAAgBC,EAAGpO,GAK1B,OAJAhB,EAAOC,QAAUkP,EAAkBzP,OAAO2P,eAAiB3P,OAAO2P,eAAevJ,OAAS,SAAyBsJ,EAAGpO,GAEpH,OADAoO,EAAEE,UAAYtO,EACPoO,CACT,EAAGpP,EAAOC,QAAQ4N,YAAa,EAAM7N,EAAOC,QAAiB,QAAID,EAAOC,QACjEkP,EAAgBC,EAAGpO,EAC5B,CACAhB,EAAOC,QAAUkP,EAAiBnP,EAAOC,QAAQ4N,YAAa,EAAM7N,EAAOC,QAAiB,QAAID,EAAOC,+BCPvG,IAAIkO,EAAU,iBAWdnO,EAAOC,QAVP,SAAsBsP,EAAOC,GAC3B,GAAuB,WAAnBrB,EAAQoB,IAAiC,OAAVA,EAAgB,OAAOA,EAC1D,IAAIE,EAAOF,EAAMnP,OAAOsP,aACxB,QAAaC,IAATF,EAAoB,CACtB,IAAIG,EAAMH,EAAK3P,KAAKyP,EAAOC,GAAQ,WACnC,GAAqB,WAAjBrB,EAAQyB,GAAmB,OAAOA,EACtC,MAAM,IAAI7C,UAAU,+CACtB,CACA,OAAiB,WAATyC,EAAoBK,OAASC,QAAQP,EAC/C,EAC+BvP,EAAOC,QAAQ4N,YAAa,EAAM7N,EAAOC,QAAiB,QAAID,EAAOC,+BCXpG,IAAIkO,EAAU,iBACVuB,EAAc,EAAQ,OAK1B1P,EAAOC,QAJP,SAAwBf,GACtB,IAAIW,EAAM6P,EAAYxQ,EAAK,UAC3B,MAAwB,WAAjBiP,EAAQtO,GAAoBA,EAAMgQ,OAAOhQ,EAClD,EACiCG,EAAOC,QAAQ4N,YAAa,EAAM7N,EAAOC,QAAiB,QAAID,EAAOC,2BCNtG,SAASkO,EAAQJ,GAGf,OAAQ/N,EAAOC,QAAUkO,EAAU,mBAAqB/N,QAAU,iBAAmBA,OAAO2P,SAAW,SAAUhC,GAC/G,cAAcA,CAChB,EAAI,SAAUA,GACZ,OAAOA,GAAO,mBAAqB3N,QAAU2N,EAAIiC,cAAgB5P,QAAU2N,IAAQ3N,OAAOT,UAAY,gBAAkBoO,CAC1H,EAAG/N,EAAOC,QAAQ4N,YAAa,EAAM7N,EAAOC,QAAiB,QAAID,EAAOC,QAAUkO,EAAQJ,EAC5F,CACA/N,EAAOC,QAAUkO,EAASnO,EAAOC,QAAQ4N,YAAa,EAAM7N,EAAOC,QAAiB,QAAID,EAAOC", "sources": ["webpack:///./node_modules/classnames/index.js", "webpack:///./node_modules/react-is/cjs/react-is.production.min.js", "webpack:///./node_modules/react-is/index.js", "webpack:///./node_modules/resize-observer-polyfill/dist/ResizeObserver.es.js", "webpack:///./node_modules/@babel/runtime/helpers/assertThisInitialized.js", "webpack:///./node_modules/@babel/runtime/helpers/defineProperty.js", "webpack:///./node_modules/@babel/runtime/helpers/extends.js", "webpack:///./node_modules/@babel/runtime/helpers/interopRequireDefault.js", "webpack:///./node_modules/@babel/runtime/helpers/interopRequireWildcard.js", "webpack:///./node_modules/@babel/runtime/helpers/objectWithoutProperties.js", "webpack:///./node_modules/@babel/runtime/helpers/objectWithoutPropertiesLoose.js", "webpack:///./node_modules/@babel/runtime/helpers/setPrototypeOf.js", "webpack:///./node_modules/@babel/runtime/helpers/toPrimitive.js", "webpack:///./node_modules/@babel/runtime/helpers/toPropertyKey.js", "webpack:///./node_modules/@babel/runtime/helpers/typeof.js"], "sourcesContent": ["/*!\n\tCopyright (c) 2018 <PERSON>.\n\tLicensed under the MIT License (MIT), see\n\thttp://jedwatson.github.io/classnames\n*/\n/* global define */\n\n(function () {\n\t'use strict';\n\n\tvar hasOwn = {}.hasOwnProperty;\n\tvar nativeCodeString = '[native code]';\n\n\tfunction classNames() {\n\t\tvar classes = [];\n\n\t\tfor (var i = 0; i < arguments.length; i++) {\n\t\t\tvar arg = arguments[i];\n\t\t\tif (!arg) continue;\n\n\t\t\tvar argType = typeof arg;\n\n\t\t\tif (argType === 'string' || argType === 'number') {\n\t\t\t\tclasses.push(arg);\n\t\t\t} else if (Array.isArray(arg)) {\n\t\t\t\tif (arg.length) {\n\t\t\t\t\tvar inner = classNames.apply(null, arg);\n\t\t\t\t\tif (inner) {\n\t\t\t\t\t\tclasses.push(inner);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t} else if (argType === 'object') {\n\t\t\t\tif (arg.toString !== Object.prototype.toString && !arg.toString.toString().includes('[native code]')) {\n\t\t\t\t\tclasses.push(arg.toString());\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tfor (var key in arg) {\n\t\t\t\t\tif (hasOwn.call(arg, key) && arg[key]) {\n\t\t\t\t\t\tclasses.push(key);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\treturn classes.join(' ');\n\t}\n\n\tif (typeof module !== 'undefined' && module.exports) {\n\t\tclassNames.default = classNames;\n\t\tmodule.exports = classNames;\n\t} else if (typeof define === 'function' && typeof define.amd === 'object' && define.amd) {\n\t\t// register as 'classnames', consistent with npm package name\n\t\tdefine('classnames', [], function () {\n\t\t\treturn classNames;\n\t\t});\n\t} else {\n\t\twindow.classNames = classNames;\n\t}\n}());\n", "/** @license React v16.13.1\n * react-is.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';var b=\"function\"===typeof Symbol&&Symbol.for,c=b?Symbol.for(\"react.element\"):60103,d=b?Symbol.for(\"react.portal\"):60106,e=b?Symbol.for(\"react.fragment\"):60107,f=b?Symbol.for(\"react.strict_mode\"):60108,g=b?Symbol.for(\"react.profiler\"):60114,h=b?Symbol.for(\"react.provider\"):60109,k=b?Symbol.for(\"react.context\"):60110,l=b?Symbol.for(\"react.async_mode\"):60111,m=b?Symbol.for(\"react.concurrent_mode\"):60111,n=b?Symbol.for(\"react.forward_ref\"):60112,p=b?Symbol.for(\"react.suspense\"):60113,q=b?\nSymbol.for(\"react.suspense_list\"):60120,r=b?Symbol.for(\"react.memo\"):60115,t=b?Symbol.for(\"react.lazy\"):60116,v=b?Symbol.for(\"react.block\"):60121,w=b?Symbol.for(\"react.fundamental\"):60117,x=b?Symbol.for(\"react.responder\"):60118,y=b?Symbol.for(\"react.scope\"):60119;\nfunction z(a){if(\"object\"===typeof a&&null!==a){var u=a.$$typeof;switch(u){case c:switch(a=a.type,a){case l:case m:case e:case g:case f:case p:return a;default:switch(a=a&&a.$$typeof,a){case k:case n:case t:case r:case h:return a;default:return u}}case d:return u}}}function A(a){return z(a)===m}exports.AsyncMode=l;exports.ConcurrentMode=m;exports.ContextConsumer=k;exports.ContextProvider=h;exports.Element=c;exports.ForwardRef=n;exports.Fragment=e;exports.Lazy=t;exports.Memo=r;exports.Portal=d;\nexports.Profiler=g;exports.StrictMode=f;exports.Suspense=p;exports.isAsyncMode=function(a){return A(a)||z(a)===l};exports.isConcurrentMode=A;exports.isContextConsumer=function(a){return z(a)===k};exports.isContextProvider=function(a){return z(a)===h};exports.isElement=function(a){return\"object\"===typeof a&&null!==a&&a.$$typeof===c};exports.isForwardRef=function(a){return z(a)===n};exports.isFragment=function(a){return z(a)===e};exports.isLazy=function(a){return z(a)===t};\nexports.isMemo=function(a){return z(a)===r};exports.isPortal=function(a){return z(a)===d};exports.isProfiler=function(a){return z(a)===g};exports.isStrictMode=function(a){return z(a)===f};exports.isSuspense=function(a){return z(a)===p};\nexports.isValidElementType=function(a){return\"string\"===typeof a||\"function\"===typeof a||a===e||a===m||a===g||a===f||a===p||a===q||\"object\"===typeof a&&null!==a&&(a.$$typeof===t||a.$$typeof===r||a.$$typeof===h||a.$$typeof===k||a.$$typeof===n||a.$$typeof===w||a.$$typeof===x||a.$$typeof===y||a.$$typeof===v)};exports.typeOf=z;\n", "'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-is.production.min.js');\n} else {\n  module.exports = require('./cjs/react-is.development.js');\n}\n", "/**\r\n * A collection of shims that provide minimal functionality of the ES6 collections.\r\n *\r\n * These implementations are not meant to be used outside of the ResizeObserver\r\n * modules as they cover only a limited range of use cases.\r\n */\r\n/* eslint-disable require-jsdoc, valid-jsdoc */\r\nvar MapShim = (function () {\r\n    if (typeof Map !== 'undefined') {\r\n        return Map;\r\n    }\r\n    /**\r\n     * Returns index in provided array that matches the specified key.\r\n     *\r\n     * @param {Array<Array>} arr\r\n     * @param {*} key\r\n     * @returns {number}\r\n     */\r\n    function getIndex(arr, key) {\r\n        var result = -1;\r\n        arr.some(function (entry, index) {\r\n            if (entry[0] === key) {\r\n                result = index;\r\n                return true;\r\n            }\r\n            return false;\r\n        });\r\n        return result;\r\n    }\r\n    return /** @class */ (function () {\r\n        function class_1() {\r\n            this.__entries__ = [];\r\n        }\r\n        Object.defineProperty(class_1.prototype, \"size\", {\r\n            /**\r\n             * @returns {boolean}\r\n             */\r\n            get: function () {\r\n                return this.__entries__.length;\r\n            },\r\n            enumerable: true,\r\n            configurable: true\r\n        });\r\n        /**\r\n         * @param {*} key\r\n         * @returns {*}\r\n         */\r\n        class_1.prototype.get = function (key) {\r\n            var index = getIndex(this.__entries__, key);\r\n            var entry = this.__entries__[index];\r\n            return entry && entry[1];\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @param {*} value\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.set = function (key, value) {\r\n            var index = getIndex(this.__entries__, key);\r\n            if (~index) {\r\n                this.__entries__[index][1] = value;\r\n            }\r\n            else {\r\n                this.__entries__.push([key, value]);\r\n            }\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.delete = function (key) {\r\n            var entries = this.__entries__;\r\n            var index = getIndex(entries, key);\r\n            if (~index) {\r\n                entries.splice(index, 1);\r\n            }\r\n        };\r\n        /**\r\n         * @param {*} key\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.has = function (key) {\r\n            return !!~getIndex(this.__entries__, key);\r\n        };\r\n        /**\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.clear = function () {\r\n            this.__entries__.splice(0);\r\n        };\r\n        /**\r\n         * @param {Function} callback\r\n         * @param {*} [ctx=null]\r\n         * @returns {void}\r\n         */\r\n        class_1.prototype.forEach = function (callback, ctx) {\r\n            if (ctx === void 0) { ctx = null; }\r\n            for (var _i = 0, _a = this.__entries__; _i < _a.length; _i++) {\r\n                var entry = _a[_i];\r\n                callback.call(ctx, entry[1], entry[0]);\r\n            }\r\n        };\r\n        return class_1;\r\n    }());\r\n})();\n\n/**\r\n * Detects whether window and document objects are available in current environment.\r\n */\r\nvar isBrowser = typeof window !== 'undefined' && typeof document !== 'undefined' && window.document === document;\n\n// Returns global object of a current environment.\r\nvar global$1 = (function () {\r\n    if (typeof global !== 'undefined' && global.Math === Math) {\r\n        return global;\r\n    }\r\n    if (typeof self !== 'undefined' && self.Math === Math) {\r\n        return self;\r\n    }\r\n    if (typeof window !== 'undefined' && window.Math === Math) {\r\n        return window;\r\n    }\r\n    // eslint-disable-next-line no-new-func\r\n    return Function('return this')();\r\n})();\n\n/**\r\n * A shim for the requestAnimationFrame which falls back to the setTimeout if\r\n * first one is not supported.\r\n *\r\n * @returns {number} Requests' identifier.\r\n */\r\nvar requestAnimationFrame$1 = (function () {\r\n    if (typeof requestAnimationFrame === 'function') {\r\n        // It's required to use a bounded function because IE sometimes throws\r\n        // an \"Invalid calling object\" error if rAF is invoked without the global\r\n        // object on the left hand side.\r\n        return requestAnimationFrame.bind(global$1);\r\n    }\r\n    return function (callback) { return setTimeout(function () { return callback(Date.now()); }, 1000 / 60); };\r\n})();\n\n// Defines minimum timeout before adding a trailing call.\r\nvar trailingTimeout = 2;\r\n/**\r\n * Creates a wrapper function which ensures that provided callback will be\r\n * invoked only once during the specified delay period.\r\n *\r\n * @param {Function} callback - Function to be invoked after the delay period.\r\n * @param {number} delay - Delay after which to invoke callback.\r\n * @returns {Function}\r\n */\r\nfunction throttle (callback, delay) {\r\n    var leadingCall = false, trailingCall = false, lastCallTime = 0;\r\n    /**\r\n     * Invokes the original callback function and schedules new invocation if\r\n     * the \"proxy\" was called during current request.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function resolvePending() {\r\n        if (leadingCall) {\r\n            leadingCall = false;\r\n            callback();\r\n        }\r\n        if (trailingCall) {\r\n            proxy();\r\n        }\r\n    }\r\n    /**\r\n     * Callback invoked after the specified delay. It will further postpone\r\n     * invocation of the original function delegating it to the\r\n     * requestAnimationFrame.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function timeoutCallback() {\r\n        requestAnimationFrame$1(resolvePending);\r\n    }\r\n    /**\r\n     * Schedules invocation of the original function.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    function proxy() {\r\n        var timeStamp = Date.now();\r\n        if (leadingCall) {\r\n            // Reject immediately following calls.\r\n            if (timeStamp - lastCallTime < trailingTimeout) {\r\n                return;\r\n            }\r\n            // Schedule new call to be in invoked when the pending one is resolved.\r\n            // This is important for \"transitions\" which never actually start\r\n            // immediately so there is a chance that we might miss one if change\r\n            // happens amids the pending invocation.\r\n            trailingCall = true;\r\n        }\r\n        else {\r\n            leadingCall = true;\r\n            trailingCall = false;\r\n            setTimeout(timeoutCallback, delay);\r\n        }\r\n        lastCallTime = timeStamp;\r\n    }\r\n    return proxy;\r\n}\n\n// Minimum delay before invoking the update of observers.\r\nvar REFRESH_DELAY = 20;\r\n// A list of substrings of CSS properties used to find transition events that\r\n// might affect dimensions of observed elements.\r\nvar transitionKeys = ['top', 'right', 'bottom', 'left', 'width', 'height', 'size', 'weight'];\r\n// Check if MutationObserver is available.\r\nvar mutationObserverSupported = typeof MutationObserver !== 'undefined';\r\n/**\r\n * Singleton controller class which handles updates of ResizeObserver instances.\r\n */\r\nvar ResizeObserverController = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserverController.\r\n     *\r\n     * @private\r\n     */\r\n    function ResizeObserverController() {\r\n        /**\r\n         * Indicates whether DOM listeners have been added.\r\n         *\r\n         * @private {boolean}\r\n         */\r\n        this.connected_ = false;\r\n        /**\r\n         * Tells that controller has subscribed for Mutation Events.\r\n         *\r\n         * @private {boolean}\r\n         */\r\n        this.mutationEventsAdded_ = false;\r\n        /**\r\n         * Keeps reference to the instance of MutationObserver.\r\n         *\r\n         * @private {MutationObserver}\r\n         */\r\n        this.mutationsObserver_ = null;\r\n        /**\r\n         * A list of connected observers.\r\n         *\r\n         * @private {Array<ResizeObserverSPI>}\r\n         */\r\n        this.observers_ = [];\r\n        this.onTransitionEnd_ = this.onTransitionEnd_.bind(this);\r\n        this.refresh = throttle(this.refresh.bind(this), REFRESH_DELAY);\r\n    }\r\n    /**\r\n     * Adds observer to observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be added.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.addObserver = function (observer) {\r\n        if (!~this.observers_.indexOf(observer)) {\r\n            this.observers_.push(observer);\r\n        }\r\n        // Add listeners if they haven't been added yet.\r\n        if (!this.connected_) {\r\n            this.connect_();\r\n        }\r\n    };\r\n    /**\r\n     * Removes observer from observers list.\r\n     *\r\n     * @param {ResizeObserverSPI} observer - Observer to be removed.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.removeObserver = function (observer) {\r\n        var observers = this.observers_;\r\n        var index = observers.indexOf(observer);\r\n        // Remove observer if it's present in registry.\r\n        if (~index) {\r\n            observers.splice(index, 1);\r\n        }\r\n        // Remove listeners if controller has no connected observers.\r\n        if (!observers.length && this.connected_) {\r\n            this.disconnect_();\r\n        }\r\n    };\r\n    /**\r\n     * Invokes the update of observers. It will continue running updates insofar\r\n     * it detects changes.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.refresh = function () {\r\n        var changesDetected = this.updateObservers_();\r\n        // Continue running updates if changes have been detected as there might\r\n        // be future ones caused by CSS transitions.\r\n        if (changesDetected) {\r\n            this.refresh();\r\n        }\r\n    };\r\n    /**\r\n     * Updates every observer from observers list and notifies them of queued\r\n     * entries.\r\n     *\r\n     * @private\r\n     * @returns {boolean} Returns \"true\" if any observer has detected changes in\r\n     *      dimensions of it's elements.\r\n     */\r\n    ResizeObserverController.prototype.updateObservers_ = function () {\r\n        // Collect observers that have active observations.\r\n        var activeObservers = this.observers_.filter(function (observer) {\r\n            return observer.gatherActive(), observer.hasActive();\r\n        });\r\n        // Deliver notifications in a separate cycle in order to avoid any\r\n        // collisions between observers, e.g. when multiple instances of\r\n        // ResizeObserver are tracking the same element and the callback of one\r\n        // of them changes content dimensions of the observed target. Sometimes\r\n        // this may result in notifications being blocked for the rest of observers.\r\n        activeObservers.forEach(function (observer) { return observer.broadcastActive(); });\r\n        return activeObservers.length > 0;\r\n    };\r\n    /**\r\n     * Initializes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.connect_ = function () {\r\n        // Do nothing if running in a non-browser environment or if listeners\r\n        // have been already added.\r\n        if (!isBrowser || this.connected_) {\r\n            return;\r\n        }\r\n        // Subscription to the \"Transitionend\" event is used as a workaround for\r\n        // delayed transitions. This way it's possible to capture at least the\r\n        // final state of an element.\r\n        document.addEventListener('transitionend', this.onTransitionEnd_);\r\n        window.addEventListener('resize', this.refresh);\r\n        if (mutationObserverSupported) {\r\n            this.mutationsObserver_ = new MutationObserver(this.refresh);\r\n            this.mutationsObserver_.observe(document, {\r\n                attributes: true,\r\n                childList: true,\r\n                characterData: true,\r\n                subtree: true\r\n            });\r\n        }\r\n        else {\r\n            document.addEventListener('DOMSubtreeModified', this.refresh);\r\n            this.mutationEventsAdded_ = true;\r\n        }\r\n        this.connected_ = true;\r\n    };\r\n    /**\r\n     * Removes DOM listeners.\r\n     *\r\n     * @private\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.disconnect_ = function () {\r\n        // Do nothing if running in a non-browser environment or if listeners\r\n        // have been already removed.\r\n        if (!isBrowser || !this.connected_) {\r\n            return;\r\n        }\r\n        document.removeEventListener('transitionend', this.onTransitionEnd_);\r\n        window.removeEventListener('resize', this.refresh);\r\n        if (this.mutationsObserver_) {\r\n            this.mutationsObserver_.disconnect();\r\n        }\r\n        if (this.mutationEventsAdded_) {\r\n            document.removeEventListener('DOMSubtreeModified', this.refresh);\r\n        }\r\n        this.mutationsObserver_ = null;\r\n        this.mutationEventsAdded_ = false;\r\n        this.connected_ = false;\r\n    };\r\n    /**\r\n     * \"Transitionend\" event handler.\r\n     *\r\n     * @private\r\n     * @param {TransitionEvent} event\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverController.prototype.onTransitionEnd_ = function (_a) {\r\n        var _b = _a.propertyName, propertyName = _b === void 0 ? '' : _b;\r\n        // Detect whether transition may affect dimensions of an element.\r\n        var isReflowProperty = transitionKeys.some(function (key) {\r\n            return !!~propertyName.indexOf(key);\r\n        });\r\n        if (isReflowProperty) {\r\n            this.refresh();\r\n        }\r\n    };\r\n    /**\r\n     * Returns instance of the ResizeObserverController.\r\n     *\r\n     * @returns {ResizeObserverController}\r\n     */\r\n    ResizeObserverController.getInstance = function () {\r\n        if (!this.instance_) {\r\n            this.instance_ = new ResizeObserverController();\r\n        }\r\n        return this.instance_;\r\n    };\r\n    /**\r\n     * Holds reference to the controller's instance.\r\n     *\r\n     * @private {ResizeObserverController}\r\n     */\r\n    ResizeObserverController.instance_ = null;\r\n    return ResizeObserverController;\r\n}());\n\n/**\r\n * Defines non-writable/enumerable properties of the provided target object.\r\n *\r\n * @param {Object} target - Object for which to define properties.\r\n * @param {Object} props - Properties to be defined.\r\n * @returns {Object} Target object.\r\n */\r\nvar defineConfigurable = (function (target, props) {\r\n    for (var _i = 0, _a = Object.keys(props); _i < _a.length; _i++) {\r\n        var key = _a[_i];\r\n        Object.defineProperty(target, key, {\r\n            value: props[key],\r\n            enumerable: false,\r\n            writable: false,\r\n            configurable: true\r\n        });\r\n    }\r\n    return target;\r\n});\n\n/**\r\n * Returns the global object associated with provided element.\r\n *\r\n * @param {Object} target\r\n * @returns {Object}\r\n */\r\nvar getWindowOf = (function (target) {\r\n    // Assume that the element is an instance of Node, which means that it\r\n    // has the \"ownerDocument\" property from which we can retrieve a\r\n    // corresponding global object.\r\n    var ownerGlobal = target && target.ownerDocument && target.ownerDocument.defaultView;\r\n    // Return the local global object if it's not possible extract one from\r\n    // provided element.\r\n    return ownerGlobal || global$1;\r\n});\n\n// Placeholder of an empty content rectangle.\r\nvar emptyRect = createRectInit(0, 0, 0, 0);\r\n/**\r\n * Converts provided string to a number.\r\n *\r\n * @param {number|string} value\r\n * @returns {number}\r\n */\r\nfunction toFloat(value) {\r\n    return parseFloat(value) || 0;\r\n}\r\n/**\r\n * Extracts borders size from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @param {...string} positions - Borders positions (top, right, ...)\r\n * @returns {number}\r\n */\r\nfunction getBordersSize(styles) {\r\n    var positions = [];\r\n    for (var _i = 1; _i < arguments.length; _i++) {\r\n        positions[_i - 1] = arguments[_i];\r\n    }\r\n    return positions.reduce(function (size, position) {\r\n        var value = styles['border-' + position + '-width'];\r\n        return size + toFloat(value);\r\n    }, 0);\r\n}\r\n/**\r\n * Extracts paddings sizes from provided styles.\r\n *\r\n * @param {CSSStyleDeclaration} styles\r\n * @returns {Object} Paddings box.\r\n */\r\nfunction getPaddings(styles) {\r\n    var positions = ['top', 'right', 'bottom', 'left'];\r\n    var paddings = {};\r\n    for (var _i = 0, positions_1 = positions; _i < positions_1.length; _i++) {\r\n        var position = positions_1[_i];\r\n        var value = styles['padding-' + position];\r\n        paddings[position] = toFloat(value);\r\n    }\r\n    return paddings;\r\n}\r\n/**\r\n * Calculates content rectangle of provided SVG element.\r\n *\r\n * @param {SVGGraphicsElement} target - Element content rectangle of which needs\r\n *      to be calculated.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getSVGContentRect(target) {\r\n    var bbox = target.getBBox();\r\n    return createRectInit(0, 0, bbox.width, bbox.height);\r\n}\r\n/**\r\n * Calculates content rectangle of provided HTMLElement.\r\n *\r\n * @param {HTMLElement} target - Element for which to calculate the content rectangle.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getHTMLElementContentRect(target) {\r\n    // Client width & height properties can't be\r\n    // used exclusively as they provide rounded values.\r\n    var clientWidth = target.clientWidth, clientHeight = target.clientHeight;\r\n    // By this condition we can catch all non-replaced inline, hidden and\r\n    // detached elements. Though elements with width & height properties less\r\n    // than 0.5 will be discarded as well.\r\n    //\r\n    // Without it we would need to implement separate methods for each of\r\n    // those cases and it's not possible to perform a precise and performance\r\n    // effective test for hidden elements. E.g. even jQuery's ':visible' filter\r\n    // gives wrong results for elements with width & height less than 0.5.\r\n    if (!clientWidth && !clientHeight) {\r\n        return emptyRect;\r\n    }\r\n    var styles = getWindowOf(target).getComputedStyle(target);\r\n    var paddings = getPaddings(styles);\r\n    var horizPad = paddings.left + paddings.right;\r\n    var vertPad = paddings.top + paddings.bottom;\r\n    // Computed styles of width & height are being used because they are the\r\n    // only dimensions available to JS that contain non-rounded values. It could\r\n    // be possible to utilize the getBoundingClientRect if only it's data wasn't\r\n    // affected by CSS transformations let alone paddings, borders and scroll bars.\r\n    var width = toFloat(styles.width), height = toFloat(styles.height);\r\n    // Width & height include paddings and borders when the 'border-box' box\r\n    // model is applied (except for IE).\r\n    if (styles.boxSizing === 'border-box') {\r\n        // Following conditions are required to handle Internet Explorer which\r\n        // doesn't include paddings and borders to computed CSS dimensions.\r\n        //\r\n        // We can say that if CSS dimensions + paddings are equal to the \"client\"\r\n        // properties then it's either IE, and thus we don't need to subtract\r\n        // anything, or an element merely doesn't have paddings/borders styles.\r\n        if (Math.round(width + horizPad) !== clientWidth) {\r\n            width -= getBordersSize(styles, 'left', 'right') + horizPad;\r\n        }\r\n        if (Math.round(height + vertPad) !== clientHeight) {\r\n            height -= getBordersSize(styles, 'top', 'bottom') + vertPad;\r\n        }\r\n    }\r\n    // Following steps can't be applied to the document's root element as its\r\n    // client[Width/Height] properties represent viewport area of the window.\r\n    // Besides, it's as well not necessary as the <html> itself neither has\r\n    // rendered scroll bars nor it can be clipped.\r\n    if (!isDocumentElement(target)) {\r\n        // In some browsers (only in Firefox, actually) CSS width & height\r\n        // include scroll bars size which can be removed at this step as scroll\r\n        // bars are the only difference between rounded dimensions + paddings\r\n        // and \"client\" properties, though that is not always true in Chrome.\r\n        var vertScrollbar = Math.round(width + horizPad) - clientWidth;\r\n        var horizScrollbar = Math.round(height + vertPad) - clientHeight;\r\n        // Chrome has a rather weird rounding of \"client\" properties.\r\n        // E.g. for an element with content width of 314.2px it sometimes gives\r\n        // the client width of 315px and for the width of 314.7px it may give\r\n        // 314px. And it doesn't happen all the time. So just ignore this delta\r\n        // as a non-relevant.\r\n        if (Math.abs(vertScrollbar) !== 1) {\r\n            width -= vertScrollbar;\r\n        }\r\n        if (Math.abs(horizScrollbar) !== 1) {\r\n            height -= horizScrollbar;\r\n        }\r\n    }\r\n    return createRectInit(paddings.left, paddings.top, width, height);\r\n}\r\n/**\r\n * Checks whether provided element is an instance of the SVGGraphicsElement.\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\r\nvar isSVGGraphicsElement = (function () {\r\n    // Some browsers, namely IE and Edge, don't have the SVGGraphicsElement\r\n    // interface.\r\n    if (typeof SVGGraphicsElement !== 'undefined') {\r\n        return function (target) { return target instanceof getWindowOf(target).SVGGraphicsElement; };\r\n    }\r\n    // If it's so, then check that element is at least an instance of the\r\n    // SVGElement and that it has the \"getBBox\" method.\r\n    // eslint-disable-next-line no-extra-parens\r\n    return function (target) { return (target instanceof getWindowOf(target).SVGElement &&\r\n        typeof target.getBBox === 'function'); };\r\n})();\r\n/**\r\n * Checks whether provided element is a document element (<html>).\r\n *\r\n * @param {Element} target - Element to be checked.\r\n * @returns {boolean}\r\n */\r\nfunction isDocumentElement(target) {\r\n    return target === getWindowOf(target).document.documentElement;\r\n}\r\n/**\r\n * Calculates an appropriate content rectangle for provided html or svg element.\r\n *\r\n * @param {Element} target - Element content rectangle of which needs to be calculated.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction getContentRect(target) {\r\n    if (!isBrowser) {\r\n        return emptyRect;\r\n    }\r\n    if (isSVGGraphicsElement(target)) {\r\n        return getSVGContentRect(target);\r\n    }\r\n    return getHTMLElementContentRect(target);\r\n}\r\n/**\r\n * Creates rectangle with an interface of the DOMRectReadOnly.\r\n * Spec: https://drafts.fxtf.org/geometry/#domrectreadonly\r\n *\r\n * @param {DOMRectInit} rectInit - Object with rectangle's x/y coordinates and dimensions.\r\n * @returns {DOMRectReadOnly}\r\n */\r\nfunction createReadOnlyRect(_a) {\r\n    var x = _a.x, y = _a.y, width = _a.width, height = _a.height;\r\n    // If DOMRectReadOnly is available use it as a prototype for the rectangle.\r\n    var Constr = typeof DOMRectReadOnly !== 'undefined' ? DOMRectReadOnly : Object;\r\n    var rect = Object.create(Constr.prototype);\r\n    // Rectangle's properties are not writable and non-enumerable.\r\n    defineConfigurable(rect, {\r\n        x: x, y: y, width: width, height: height,\r\n        top: y,\r\n        right: x + width,\r\n        bottom: height + y,\r\n        left: x\r\n    });\r\n    return rect;\r\n}\r\n/**\r\n * Creates DOMRectInit object based on the provided dimensions and the x/y coordinates.\r\n * Spec: https://drafts.fxtf.org/geometry/#dictdef-domrectinit\r\n *\r\n * @param {number} x - X coordinate.\r\n * @param {number} y - Y coordinate.\r\n * @param {number} width - Rectangle's width.\r\n * @param {number} height - Rectangle's height.\r\n * @returns {DOMRectInit}\r\n */\r\nfunction createRectInit(x, y, width, height) {\r\n    return { x: x, y: y, width: width, height: height };\r\n}\n\n/**\r\n * Class that is responsible for computations of the content rectangle of\r\n * provided DOM element and for keeping track of it's changes.\r\n */\r\nvar ResizeObservation = /** @class */ (function () {\r\n    /**\r\n     * Creates an instance of ResizeObservation.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     */\r\n    function ResizeObservation(target) {\r\n        /**\r\n         * Broadcasted width of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */\r\n        this.broadcastWidth = 0;\r\n        /**\r\n         * Broadcasted height of content rectangle.\r\n         *\r\n         * @type {number}\r\n         */\r\n        this.broadcastHeight = 0;\r\n        /**\r\n         * Reference to the last observed content rectangle.\r\n         *\r\n         * @private {DOMRectInit}\r\n         */\r\n        this.contentRect_ = createRectInit(0, 0, 0, 0);\r\n        this.target = target;\r\n    }\r\n    /**\r\n     * Updates content rectangle and tells whether it's width or height properties\r\n     * have changed since the last broadcast.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    ResizeObservation.prototype.isActive = function () {\r\n        var rect = getContentRect(this.target);\r\n        this.contentRect_ = rect;\r\n        return (rect.width !== this.broadcastWidth ||\r\n            rect.height !== this.broadcastHeight);\r\n    };\r\n    /**\r\n     * Updates 'broadcastWidth' and 'broadcastHeight' properties with a data\r\n     * from the corresponding properties of the last observed content rectangle.\r\n     *\r\n     * @returns {DOMRectInit} Last observed content rectangle.\r\n     */\r\n    ResizeObservation.prototype.broadcastRect = function () {\r\n        var rect = this.contentRect_;\r\n        this.broadcastWidth = rect.width;\r\n        this.broadcastHeight = rect.height;\r\n        return rect;\r\n    };\r\n    return ResizeObservation;\r\n}());\n\nvar ResizeObserverEntry = /** @class */ (function () {\r\n    /**\r\n     * Creates an instance of ResizeObserverEntry.\r\n     *\r\n     * @param {Element} target - Element that is being observed.\r\n     * @param {DOMRectInit} rectInit - Data of the element's content rectangle.\r\n     */\r\n    function ResizeObserverEntry(target, rectInit) {\r\n        var contentRect = createReadOnlyRect(rectInit);\r\n        // According to the specification following properties are not writable\r\n        // and are also not enumerable in the native implementation.\r\n        //\r\n        // Property accessors are not being used as they'd require to define a\r\n        // private WeakMap storage which may cause memory leaks in browsers that\r\n        // don't support this type of collections.\r\n        defineConfigurable(this, { target: target, contentRect: contentRect });\r\n    }\r\n    return ResizeObserverEntry;\r\n}());\n\nvar ResizeObserverSPI = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback function that is invoked\r\n     *      when one of the observed elements changes it's content dimensions.\r\n     * @param {ResizeObserverController} controller - Controller instance which\r\n     *      is responsible for the updates of observer.\r\n     * @param {ResizeObserver} callbackCtx - Reference to the public\r\n     *      ResizeObserver instance which will be passed to callback function.\r\n     */\r\n    function ResizeObserverSPI(callback, controller, callbackCtx) {\r\n        /**\r\n         * Collection of resize observations that have detected changes in dimensions\r\n         * of elements.\r\n         *\r\n         * @private {Array<ResizeObservation>}\r\n         */\r\n        this.activeObservations_ = [];\r\n        /**\r\n         * Registry of the ResizeObservation instances.\r\n         *\r\n         * @private {Map<Element, ResizeObservation>}\r\n         */\r\n        this.observations_ = new MapShim();\r\n        if (typeof callback !== 'function') {\r\n            throw new TypeError('The callback provided as parameter 1 is not a function.');\r\n        }\r\n        this.callback_ = callback;\r\n        this.controller_ = controller;\r\n        this.callbackCtx_ = callbackCtx;\r\n    }\r\n    /**\r\n     * Starts observing provided element.\r\n     *\r\n     * @param {Element} target - Element to be observed.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.observe = function (target) {\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        // Do nothing if current environment doesn't have the Element interface.\r\n        if (typeof Element === 'undefined' || !(Element instanceof Object)) {\r\n            return;\r\n        }\r\n        if (!(target instanceof getWindowOf(target).Element)) {\r\n            throw new TypeError('parameter 1 is not of type \"Element\".');\r\n        }\r\n        var observations = this.observations_;\r\n        // Do nothing if element is already being observed.\r\n        if (observations.has(target)) {\r\n            return;\r\n        }\r\n        observations.set(target, new ResizeObservation(target));\r\n        this.controller_.addObserver(this);\r\n        // Force the update of observations.\r\n        this.controller_.refresh();\r\n    };\r\n    /**\r\n     * Stops observing provided element.\r\n     *\r\n     * @param {Element} target - Element to stop observing.\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.unobserve = function (target) {\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        // Do nothing if current environment doesn't have the Element interface.\r\n        if (typeof Element === 'undefined' || !(Element instanceof Object)) {\r\n            return;\r\n        }\r\n        if (!(target instanceof getWindowOf(target).Element)) {\r\n            throw new TypeError('parameter 1 is not of type \"Element\".');\r\n        }\r\n        var observations = this.observations_;\r\n        // Do nothing if element is not being observed.\r\n        if (!observations.has(target)) {\r\n            return;\r\n        }\r\n        observations.delete(target);\r\n        if (!observations.size) {\r\n            this.controller_.removeObserver(this);\r\n        }\r\n    };\r\n    /**\r\n     * Stops observing all elements.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.disconnect = function () {\r\n        this.clearActive();\r\n        this.observations_.clear();\r\n        this.controller_.removeObserver(this);\r\n    };\r\n    /**\r\n     * Collects observation instances the associated element of which has changed\r\n     * it's content rectangle.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.gatherActive = function () {\r\n        var _this = this;\r\n        this.clearActive();\r\n        this.observations_.forEach(function (observation) {\r\n            if (observation.isActive()) {\r\n                _this.activeObservations_.push(observation);\r\n            }\r\n        });\r\n    };\r\n    /**\r\n     * Invokes initial callback function with a list of ResizeObserverEntry\r\n     * instances collected from active resize observations.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.broadcastActive = function () {\r\n        // Do nothing if observer doesn't have active observations.\r\n        if (!this.hasActive()) {\r\n            return;\r\n        }\r\n        var ctx = this.callbackCtx_;\r\n        // Create ResizeObserverEntry instance for every active observation.\r\n        var entries = this.activeObservations_.map(function (observation) {\r\n            return new ResizeObserverEntry(observation.target, observation.broadcastRect());\r\n        });\r\n        this.callback_.call(ctx, entries, ctx);\r\n        this.clearActive();\r\n    };\r\n    /**\r\n     * Clears the collection of active observations.\r\n     *\r\n     * @returns {void}\r\n     */\r\n    ResizeObserverSPI.prototype.clearActive = function () {\r\n        this.activeObservations_.splice(0);\r\n    };\r\n    /**\r\n     * Tells whether observer has active observations.\r\n     *\r\n     * @returns {boolean}\r\n     */\r\n    ResizeObserverSPI.prototype.hasActive = function () {\r\n        return this.activeObservations_.length > 0;\r\n    };\r\n    return ResizeObserverSPI;\r\n}());\n\n// Registry of internal observers. If WeakMap is not available use current shim\r\n// for the Map collection as it has all required methods and because WeakMap\r\n// can't be fully polyfilled anyway.\r\nvar observers = typeof WeakMap !== 'undefined' ? new WeakMap() : new MapShim();\r\n/**\r\n * ResizeObserver API. Encapsulates the ResizeObserver SPI implementation\r\n * exposing only those methods and properties that are defined in the spec.\r\n */\r\nvar ResizeObserver = /** @class */ (function () {\r\n    /**\r\n     * Creates a new instance of ResizeObserver.\r\n     *\r\n     * @param {ResizeObserverCallback} callback - Callback that is invoked when\r\n     *      dimensions of the observed elements change.\r\n     */\r\n    function ResizeObserver(callback) {\r\n        if (!(this instanceof ResizeObserver)) {\r\n            throw new TypeError('Cannot call a class as a function.');\r\n        }\r\n        if (!arguments.length) {\r\n            throw new TypeError('1 argument required, but only 0 present.');\r\n        }\r\n        var controller = ResizeObserverController.getInstance();\r\n        var observer = new ResizeObserverSPI(callback, controller, this);\r\n        observers.set(this, observer);\r\n    }\r\n    return ResizeObserver;\r\n}());\r\n// Expose public methods of ResizeObserver.\r\n[\r\n    'observe',\r\n    'unobserve',\r\n    'disconnect'\r\n].forEach(function (method) {\r\n    ResizeObserver.prototype[method] = function () {\r\n        var _a;\r\n        return (_a = observers.get(this))[method].apply(_a, arguments);\r\n    };\r\n});\n\nvar index = (function () {\r\n    // Export existing implementation if available.\r\n    if (typeof global$1.ResizeObserver !== 'undefined') {\r\n        return global$1.ResizeObserver;\r\n    }\r\n    return ResizeObserver;\r\n})();\n\nexport default index;\n", "function _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nmodule.exports = _assertThisInitialized, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var toPropertyKey = require(\"./toPropertyKey.js\");\nfunction _defineProperty(obj, key, value) {\n  key = toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nmodule.exports = _defineProperty, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _extends() {\n  module.exports = _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n  return _extends.apply(this, arguments);\n}\nmodule.exports = _extends, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nmodule.exports = _interopRequireDefault, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") {\n    return {\n      \"default\": obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj[\"default\"] = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nmodule.exports = _interopRequireWildcard, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var objectWithoutPropertiesLoose = require(\"./objectWithoutPropertiesLoose.js\");\nfunction _objectWithoutProperties(source, excluded) {\n  if (source == null) return {};\n  var target = objectWithoutPropertiesLoose(source, excluded);\n  var key, i;\n  if (Object.getOwnPropertySymbols) {\n    var sourceSymbolKeys = Object.getOwnPropertySymbols(source);\n    for (i = 0; i < sourceSymbolKeys.length; i++) {\n      key = sourceSymbolKeys[i];\n      if (excluded.indexOf(key) >= 0) continue;\n      if (!Object.prototype.propertyIsEnumerable.call(source, key)) continue;\n      target[key] = source[key];\n    }\n  }\n  return target;\n}\nmodule.exports = _objectWithoutProperties, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nmodule.exports = _objectWithoutPropertiesLoose, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _setPrototypeOf(o, p) {\n  module.exports = _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;\n  return _setPrototypeOf(o, p);\n}\nmodule.exports = _setPrototypeOf, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nfunction _toPrimitive(input, hint) {\n  if (_typeof(input) !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (_typeof(res) !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nmodule.exports = _toPrimitive, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "var _typeof = require(\"./typeof.js\")[\"default\"];\nvar toPrimitive = require(\"./toPrimitive.js\");\nfunction _toPropertyKey(arg) {\n  var key = toPrimitive(arg, \"string\");\n  return _typeof(key) === \"symbol\" ? key : String(key);\n}\nmodule.exports = _toPropertyKey, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;", "function _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return (module.exports = _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, module.exports.__esModule = true, module.exports[\"default\"] = module.exports), _typeof(obj);\n}\nmodule.exports = _typeof, module.exports.__esModule = true, module.exports[\"default\"] = module.exports;"], "names": ["hasOwn", "hasOwnProperty", "classNames", "classes", "i", "arguments", "length", "arg", "argType", "push", "Array", "isArray", "inner", "apply", "toString", "Object", "prototype", "includes", "key", "call", "join", "module", "exports", "default", "b", "Symbol", "for", "c", "d", "e", "f", "g", "h", "k", "l", "m", "n", "p", "q", "r", "t", "v", "w", "x", "y", "z", "a", "u", "$$typeof", "type", "A", "AsyncMode", "ConcurrentMode", "ContextConsumer", "ContextProvider", "Element", "ForwardRef", "Fragment", "Lazy", "Memo", "Portal", "Profiler", "StrictMode", "Suspense", "isAsyncMode", "isConcurrentMode", "isContextConsumer", "isContextProvider", "isElement", "isForwardRef", "isFragment", "isLazy", "isMemo", "isPortal", "isProfiler", "isStrictMode", "isSuspense", "isValidElementType", "typeOf", "MapShim", "Map", "getIndex", "arr", "result", "some", "entry", "index", "class_1", "this", "__entries__", "defineProperty", "get", "enumerable", "configurable", "set", "value", "delete", "entries", "splice", "has", "clear", "for<PERSON>ach", "callback", "ctx", "_i", "_a", "<PERSON><PERSON><PERSON><PERSON>", "window", "document", "global$1", "Math", "self", "Function", "requestAnimationFrame$1", "requestAnimationFrame", "bind", "setTimeout", "Date", "now", "<PERSON><PERSON><PERSON><PERSON>", "mutationObserverSupported", "MutationObserver", "ResizeObserverController", "connected_", "mutationEventsAdded_", "mutationsObserver_", "observers_", "onTransitionEnd_", "refresh", "delay", "leadingCall", "trailingCall", "lastCallTime", "resolvePending", "proxy", "timeout<PERSON><PERSON><PERSON>", "timeStamp", "throttle", "addObserver", "observer", "indexOf", "connect_", "removeObserver", "observers", "disconnect_", "updateObservers_", "activeObservers", "filter", "gatherActive", "hasActive", "broadcastActive", "addEventListener", "observe", "attributes", "childList", "characterData", "subtree", "removeEventListener", "disconnect", "_b", "propertyName", "getInstance", "instance_", "defineConfigurable", "target", "props", "keys", "writable", "getWindowOf", "ownerDocument", "defaultView", "emptyRect", "createRectInit", "toFloat", "parseFloat", "getBordersSize", "styles", "positions", "reduce", "size", "position", "isSVGGraphicsElement", "SVGGraphicsElement", "SVGElement", "getBBox", "getContentRect", "bbox", "width", "height", "getSVGContentRect", "clientWidth", "clientHeight", "getComputedStyle", "paddings", "positions_1", "getPaddings", "horizPad", "left", "right", "vertPad", "top", "bottom", "boxSizing", "round", "documentElement", "isDocumentElement", "vertScrollbar", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "abs", "getHTMLElementContentRect", "ResizeObservation", "broadcastWidth", "broadcastHeight", "contentRect_", "isActive", "rect", "broadcastRect", "ResizeObserverEntry", "rectInit", "Constr", "contentRect", "DOMRectReadOnly", "create", "ResizeObserverSPI", "controller", "callbackCtx", "activeObservations_", "observations_", "TypeError", "callback_", "controller_", "callbackCtx_", "observations", "unobserve", "clearActive", "_this", "observation", "map", "WeakMap", "ResizeObserver", "method", "ReferenceError", "__esModule", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "obj", "_extends", "assign", "source", "_typeof", "_getRequireWildcardCache", "nodeInterop", "cacheBabelInterop", "cacheNodeInterop", "cache", "newObj", "hasPropertyDescriptor", "getOwnPropertyDescriptor", "desc", "objectWithoutPropertiesLoose", "excluded", "getOwnPropertySymbols", "sourceSymbolKeys", "propertyIsEnumerable", "sourceKeys", "_setPrototypeOf", "o", "setPrototypeOf", "__proto__", "input", "hint", "prim", "toPrimitive", "undefined", "res", "String", "Number", "iterator", "constructor"], "sourceRoot": ""}